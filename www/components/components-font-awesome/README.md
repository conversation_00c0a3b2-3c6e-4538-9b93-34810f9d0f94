Font Awesome
============

Shim repository for Font Awesome.

The full suite of pictographic icons, examples, and documentation can be found at:
http://fortawesome.github.com/Font-Awesome/


Package Managers
----------------

* [npm](http://npmjs.org): `components-font-awesome`
* [Bower](http://bower.io): `components-font-awesome`
* [Component](https://github.com/component/component): `components/font-awesome`
* [Composer](http://packagist.org/packages/components/font-awesome): `components/font-awesome`


License
-------

- The Font Awesome font is licensed under the SIL Open Font License - http://scripts.sil.org/OFL
- Font Awesome CSS, LESS, and SASS files are licensed under the MIT License - http://opensource.org/licenses/mit-license.html
- The Font Awesome pictograms are licensed under the CC BY 3.0 License - http://creativecommons.org/licenses/by/3.0/
- Attribution is no longer required in Font Awesome 3.0, but much appreciated: "Font Awesome by <PERSON> - http://fortawesome.github.com/Font-Awesome"
