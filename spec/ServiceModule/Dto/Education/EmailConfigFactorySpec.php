<?php

namespace spec\ServiceModule\Dto\Education;

use ServiceModule\Dto\Education\EmailConfig;
use ServiceModule\Dto\Education\EmailConfigFactory;
use PhpSpec\ObjectBehavior;
use ServiceModule\Exceptions\InvalidConfigurationException;
use Utils\Date;

/**
 * @mixin EmailConfigFactory
 */
class EmailConfigFactorySpec extends ObjectBehavior
{
    function it_is_initializable()
    {
        $this->shouldHaveType(EmailConfigFactory::class);
    }

    function it_validates_valid_configuration_without_service_dependency()
    {
        $config = [
            [
                EmailConfigFactory::CONFIG_KEY_MONTHS_AFTER_FORMATION => 2,
                EmailConfigFactory::CONFIG_KEY_EMAIL_TAG => 'emailTag',
                EmailConfigFactory::CONFIG_KEY_EMAIL_ID => 1
            ]
        ];
        $this->shouldNotThrow(InvalidConfigurationException::missingParameter())
            ->during('getList', [$config]);

        $list = $this->getList($config);

        $end = new Date(sprintf('-%d months', 2));
        $start = clone $end;
        $start->modify(EmailConfigFactory::INTERVAL_RANGE);

        $list->shouldHaveCount(1);
        $list[0]->shouldHaveType(EmailConfig::class);
        $list[0]->getIntervalStart()->shouldBeLike($start);
        $list[0]->getIntervalEnd()->shouldBeLike($end);

    }

    function it_validates_valid_configuration_with_service_dependency()
    {
        $config = [
            [
                EmailConfigFactory::CONFIG_KEY_MONTHS_AFTER_FORMATION => 2,
                EmailConfigFactory::CONFIG_KEY_EMAIL_TAG => 'defTag',
                EmailConfigFactory::CONFIG_KEY_EMAIL_ID => 1,
                EmailConfigFactory::CONFIG_KEY_SERVICE_DEPENDENCY => [
                    EmailConfigFactory::CONFIG_KEY_SERVICE_DEPENDENCY_TYPES => ['PSC'],
                    EmailConfigFactory::CONFIG_KEY_EMAIL_TAG => 'tag',
                    EmailConfigFactory::CONFIG_KEY_EMAIL_ID => 2,
                ]
            ]
        ];

        $this->shouldNotThrow(InvalidConfigurationException::missingParameter())
            ->during('getList', [$config]);
    }

    function it_throws_exception_with_invalid_configuration_missing_months()
    {
        $config = [
            [
                EmailConfigFactory::CONFIG_KEY_EMAIL_TAG => 'tag',
                EmailConfigFactory::CONFIG_KEY_EMAIL_ID => 1,
                EmailConfigFactory::CONFIG_KEY_SERVICE_DEPENDENCY => [
                    EmailConfigFactory::CONFIG_KEY_SERVICE_DEPENDENCY_TYPES => [],
                    EmailConfigFactory::CONFIG_KEY_EMAIL_TAG => 'tag',
                    EmailConfigFactory::CONFIG_KEY_EMAIL_ID => 1,
                ]
            ]
        ];

        $this->shouldThrow(InvalidConfigurationException::missingParameter())
            ->during('getList', [$config]);

    }

    function it_throws_exception_with_invalid_configuration_with_service_dependency()
    {
        $config = [
            [
                EmailConfigFactory::CONFIG_KEY_MONTHS_AFTER_FORMATION => 2,
                EmailConfigFactory::CONFIG_KEY_EMAIL_TAG => 'tag',
                EmailConfigFactory::CONFIG_KEY_EMAIL_ID => 1,
                EmailConfigFactory::CONFIG_KEY_SERVICE_DEPENDENCY => [
                    EmailConfigFactory::CONFIG_KEY_SERVICE_DEPENDENCY_TYPES => [],
                    // missing email tag in dependency
                    EmailConfigFactory::CONFIG_KEY_EMAIL_ID => 1,
                ]
            ]
        ];

        $this->shouldThrow(InvalidConfigurationException::missingParameter())
            ->during('getList', [$config]);
    }

    function it_throws_exception_with_invalid_configuration_without_service_dependency()
    {
        $config = [
            [
                EmailConfigFactory::CONFIG_KEY_MONTHS_AFTER_FORMATION => 2,
                // missing email tag
                EmailConfigFactory::CONFIG_KEY_EMAIL_ID => 1
            ]
        ];

        $this->shouldThrow(InvalidConfigurationException::missingParameter())
            ->during('getList', [$config]);
    }
}
