<?php

declare(strict_types=1);

namespace spec\BankingModule\Domain;

use BankingModule\Domain\BankCashBack;
use BankingModule\Domain\BankCashBackList;
use Entities\Company;
use Entities\Customer;
use Models\Products\Package;
use PhpSpec\ObjectBehavior;

class BankCashBackOptionsSpec extends ObjectBehavior
{
    public function let(Package $package)
    {
        $company = new Company(new Customer('test', 'test'), 'test');
        $this->beConstructedWith(new BankCashBackList(), $company, $package);
    }

    public function it_is_initializable()
    {
        $this->shouldHaveType('BankingModule\Domain\BankCashBackOptions');
    }

    public function it_should_have_cash_back_allowed(Package $package)
    {
        $package->bankingEnabled = true;
        $company = $this->getCompany();
        $this->beConstructedWith(new BankCashBackList(), $company, $package);
        $this->isCashBackAllowed()->shouldBe(true);
    }

    public function it_should_have_cash_back_allowed_for_imported_companies()
    {
        $company = $this->getCompany();
        $this->beConstructedWith(new BankCashBackList(), $company);
        $this->isCashBackAllowed()->shouldBe(true);
    }

    public function it_should_not_have_cash_back_allowed_for_non_uk_customer(Package $package)
    {
        $package->bankingEnabled = true;
        $company = $this->getCompany('Austria');
        $this->beConstructedWith(new BankCashBackList(), $company, $package);
        $this->isCashBackAllowed()->shouldBe(false);
    }

    public function it_should_be_able_to_apply_for_cash_back(Package $package)
    {
        $package->bankingEnabled = true;
        $company = $this->getCompany();
        $bankList = new BankCashBackList();
        $bankList->addBank(new BankCashBack('test', 30, false));
        $this->beConstructedWith($bankList, $company, $package);
        $this->canApplyForCashBack()->shouldBe(true);
    }

    public function it_should_not_be_able_to_apply_for_cash_back_if_there_are_not_offers(Package $package)
    {
        $package->bankingEnabled = true;
        $company = $this->getCompany();
        $bankList = new BankCashBackList();
        $this->beConstructedWith($bankList, $company, $package);
        $this->canApplyForCashBack()->shouldBe(false);
    }

    public function it_should_be_able_to_show_bank_offer(Package $package)
    {
        $package->bankingEnabled = true;
        $package->isBankingPartnerPackage()->willReturn(false);
        $company = $this->getCompany();
        $bankList = new BankCashBackList();
        $bankList->addBank(new BankCashBack('test1', 30, true));
        $bankList->addBank(new BankCashBack('test2', 30, false));
        $this->beConstructedWith($bankList, $company, $package);
        $this->canShowBankOffer()->shouldBe(true);
    }

    public function it_should_not_show_bank_offer_if_all_offers_are_used(Package $package)
    {
        $package->bankingEnabled = true;
        $package->isBankingPartnerPackage()->willReturn(false);
        $company = $this->getCompany();
        $bankList = new BankCashBackList();
        $bankList->addBank(new BankCashBack('test1', 30, true));
        $bankList->addBank(new BankCashBack('test2', 30, true));
        $this->beConstructedWith($bankList, $company, $package);
        $this->canShowBankOffer()->shouldBe(false);
    }

    public function it_should_be_able_to_dismiss_bank_offer(Package $package)
    {
        $package->bankingEnabled = true;
        $company = $this->getCompany();
        $bankList = new BankCashBackList();
        $bankList->addBank(new BankCashBack('test1', 30, false));
        $bankList->addBank(new BankCashBack('test2', 30, true));
        $this->beConstructedWith($bankList, $company, $package);
        $this->canDismissBankOffer()->shouldBe(true);
    }

    public function it_should_be_able_to_apply_for_cash_back_for_specific_bank(Package $package)
    {
        $package->bankingEnabled = true;
        $company = $this->getCompany();
        $bankList = new BankCashBackList();
        $bankList->addBank(new BankCashBack('test1', 30, false));
        $bankList->addBank(new BankCashBack('test2', 30, true));
        $this->beConstructedWith($bankList, $company, $package);
        $this->canApplyForCashBackWith('test1')->shouldBe(true);
        $this->canApplyForCashBackWith('test2')->shouldBe(false);
        $this->canApplyForCashBackWith('test3')->shouldBe(false);
    }

    public function it_should_provide_applied_bank_names(Package $package)
    {
        $package->bankingEnabled = true;
        $company = $this->getCompany();
        $bankList = new BankCashBackList();
        $bankList->addBank(new BankCashBack('TEST_BANK_1', 50, false));
        $bankList->addBank(new BankCashBack('TEST_BANK_2', 50, true));
        $bankList->addBank(new BankCashBack('TEST_BANK_3', 50, true));
        $this->beConstructedWith($bankList, $company);
        $this->getAppliedBankNames()->shouldBe(['TEST_BANK_2', 'TEST_BANK_3']);
    }

    public function it_should_not_show_bank_offer_if_package_is_banking_partner(Package $package)
    {
        $package->bankingEnabled = true;
        $package->isBankingPartnerPackage()->willReturn(true);
        $company = $this->getCompany();
        $bankList = new BankCashBackList();
        $bankList->addBank(new BankCashBack('test1', 30, true));
        $bankList->addBank(new BankCashBack('test2', 30, true));
        $this->beConstructedWith($bankList, $company, $package);
        $this->canShowBankOffer()->shouldBe(false);
    }

    private function getCompany($country = null)
    {
        $customer = new Customer('test', 'test');
        if ($country) {
            $customer->setCountryFromString($country);
        } else {
            $customer->setCountryId(223);
        }
        $company = new Company($customer, 'test');
        $company->setCompanyNumber('test');

        return $company;
    }
}
