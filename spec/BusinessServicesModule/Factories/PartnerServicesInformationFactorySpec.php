<?php

namespace spec\BusinessServicesModule\Factories;

use BusinessServicesModule\Factories\PartnerServicesInformationFactory;
use BusinessServicesModule\Entities\PartnerServicesInformation;
use CompaniesHouseModule\Entities\Address;
use PhpSpec\ObjectBehavior;

class PartnerServicesInformationFactorySpec extends ObjectBehavior
{
    private $data = [
        'title' => 'Lord',
        'first_name' => '<PERSON>',
        'last_name' => 'Bond',
        'phone' => 007,
        'address' => 'MI6',
        'email' => '<EMAIL>',
        'county' => 'london',
        'country' => 'uk',
        'street' => 'secret',
        'premise' => 'secret',
        'thoroughfare' => 'secret',
        'post_town' => 'secret',
        'post_code' => 'secret',
    ];

    function it_is_initializable()
    {
        $this->shouldHaveType(PartnerServicesInformationFactory::class);
    }

    function it_should_create_new_partner_services_information(Address $address)
    {
        $address->getCounty()->willReturn($this->data['county']);
        $address->getCountry()->willReturn($this->data['country']);
        $address->getStreet()->willReturn($this->data['street']);
        $address->getPremise()->willReturn($this->data['premise']);
        $address->getThoroughfare()->willReturn($this->data['thoroughfare']);
        $address->getPostTown()->willReturn($this->data['post_town']);
        $address->getPostcode()->willReturn($this->data['post_code']);

        $psi = (new PartnerServicesInformation())
            ->setCounty($this->data['county'])
            ->setCountry($this->data['country'])
            ->setStreet($this->data['street'])
            ->setNameNumber($this->data['premise'])
            ->setLocality($this->data['thoroughfare'])
            ->setTown($this->data['post_town'])
            ->setPostcode($this->data['post_code']);

        $this->fromData($address)->shouldBeLike($psi);
    }
}
