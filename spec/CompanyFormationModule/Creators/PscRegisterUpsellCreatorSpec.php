<?php

namespace spec\CompanyFormationModule\Creators;

use CompanyFormationModule\Creators\PscRegisterUpsellCreator;
use CompanyFormationModule\Factories\CorporatePscFactory;
use CompanyFormationModule\Factories\PersonPscFactory;
use Entities\Company;
use Entities\CompanyHouse\FormSubmission\CompanyIncorporation;
use Entities\CompanyHouse\FormSubmission\CompanyIncorporation\Psc;
use Entities\Register\Psc\CorporatePsc;
use Entities\Register\Psc\PersonPsc;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use Services\Register\Psc\EntryService;

class PscRegisterUpsellCreatorSpec extends ObjectBehavior
{
    /**
     * @var EntryService
     */
    private $entryService;

    /**
     * @var PersonPscFactory
     */
    private $personPscFactory;

    /**
     * @var CorporatePscFactory
     */
    private $corporatePscFactory;

    function let(EntryService $entryService, PersonPscFactory $personPscFactory, CorporatePscFactory $corporatePscFactory){
        $this->entryService = $entryService;
        $this->personPscFactory = $personPscFactory;
        $this->corporatePscFactory = $corporatePscFactory;

        $this->beConstructedWith($entryService, $personPscFactory, $corporatePscFactory);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(PscRegisterUpsellCreator::class);
    }

    function it_should_create_corporate_psc_register(Company $company, CompanyIncorporation $incorporation, Psc $psc, CorporatePsc $corporatePsc)
    {
        $company->getIncorporationFormSubmission()->willReturn($incorporation);
        $incorporation->getPscs()->willReturn([$psc]);
        $psc->isCorporatePsc()->willReturn(TRUE);
        $this->corporatePscFactory->fromIncorporation($company, $psc)->willReturn($corporatePsc);
        $this->entryService->addEntry($corporatePsc)->shouldBeCalled();
        $this->createPscRegister($company);

    }

    function it_should_create_person_psc_register(Company $company, CompanyIncorporation $incorporation, Psc $psc, PersonPsc $personPsc)
    {
        $company->getIncorporationFormSubmission()->willReturn($incorporation);
        $incorporation->getPscs()->willReturn([$psc]);
        $psc->isCorporatePsc()->willReturn(FALSE);
        $this->personPscFactory->fromIncorporation($company, $psc)->willReturn($personPsc);
        $this->entryService->addEntry($personPsc)->shouldBeCalled();
        $this->createPscRegister($company);

    }

}
