<?php

/**
 * Nette Framework
 *
 * Copyright (c) 2004, 2009 <PERSON> (http://davidgrudl.com)
 *
 * This source file is subject to the "Nette license" that is bundled
 * with this package in the file license.txt.
 *
 * For more information please see http://nettephp.com
 *
 * @copyright  Copyright (c) 2004, 2009 <PERSON>
 * @license    http://nettephp.com/license  Nette license
 * @link       http://nettephp.com
 * @category   Nette
 * @package    Nette\Templates
 * @version    $Id: ITemplate.php 182 2008-12-31 00:28:33Z <EMAIL> $
 */

namespace Legacy\Nette\Templates;

/**
 * Defines template methods.
 *
 * <AUTHOR>
 * @copyright  Copyright (c) 2004, 2009 <PERSON>
 * @package    Nette\Templates
 */
interface ITemplate
{

	/**
	 * Renders template to output.
	 * @return void
	 */
	function render();

}
