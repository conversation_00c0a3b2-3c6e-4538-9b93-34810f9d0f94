<?php

namespace Framework;


use <PERSON><PERSON>\Connection;
use Config\Constants\DiLocator;
use ErrorModule\Controllers\IErrorHandlerController;
use ErrorModule\Ext\PanelExt;
use Legacy\Nette\Web\HttpResponse;
use Legacy\Nette\Caching\Cache;
use Legacy\Nette\Web\HttpRequest;
use Services\OldServices\VoucherService;
use Services\Registry;
use RouterModule\Exceptions\RouteException;
use Legacy\Nette\Web\Session;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\HttpFoundation\Response;
use Throwable;
use Utils\Directory;
use Utils\NetteSmartObject;

include LIBS_DIR . '/3rdParty/Nette/compatibility.php';

/**
 * @deprecated do not use directly use. dependency injection instead (if not possible use StaticContainer)
 * Provides run application
 */
class FApplication extends NetteSmartObject
{
    const SESSION_SAVE_HANDLER_FILES = 'files';

    /** @var int */
    public static $nodeId = NULL;

    /** @var string */
    public static $lang = 'EN';

    /** @var string */
    public static $action = NULL;

    /** @var string */
    public static $view;

    /** @var string */
    public static $host;

    /** @var array */
    public static $config;

    /**
     * @var HttpRequest
     */
    public static $httpRequest;

    /** @var array */
    public static $get;

    /** @var array */
    public static $post;

    /** @var array */
    public static $files;

    /** @var array */
    public static $cookies;

    /**
     * @var HttpResponse
     */
    public static $httpResponse;

    /**
     * @var Session
     */
    public static $session;

    /** @var object */
    public static $db;

    /** @var object */
    public static $template;

    /** @var object */
    public static $controler;

    /**
     * @var FRouter
     */
    public static $router;

    /**
     * @var Container
     */
    public static $container;

    /**
     * @var IErrorHandlerController
     */
    private $errorController;

    /**
     * @param array $config
     * @param Container $containerBuilder
     */
    public function __construct(
        $config,
        Container $container,
        Session $session,
        FRouter $router,
        Connection $dibiConnection,
        IErrorHandlerController $errorController
    )
    {
        self::$config = $config;
        self::$container = $container;
        self::$session = $session;
        self::$router = $router;
        self::$db = $dibiConnection;
        $this->errorController = $errorController;
    }

    public function setUpLegacyDeps()
    {
        // template causes many other resources to load
        self::$template = self::$container->get('ftemplate');
        HttpRequest::$port = self::$container->get('symfony.request')->getPort();
        HttpRequest::$scheme = self::$container->get('symfony.request')->getScheme();
        self::$httpRequest = new HttpRequest();

        self::$get = self::$httpRequest->getQuery();
        self::$post = self::$httpRequest->getPost();
        self::$files = self::$httpRequest->getFiles();
        self::$cookies = self::$httpRequest->getCookies();

        //removing friendly url from global get (symfony forms used without form name produces error since friendly_url is additional parameter)
        if (isset($_GET['friendly_url'])) unset($_GET['friendly_url']);

        self::$httpResponse = new HttpResponse();

        if (self::$container->getParameter('session.save_handler') === self::SESSION_SAVE_HANDLER_FILES)
            Directory::createWritable(self::$container->getParameter('session.save_path'));

        self::$nodeId = self::$router->getCurrentNodeId();
        self::$lang = self::$router->getCurrentLanguage();
        self::$action = self::$router->getCurrentAction();
        self::$view = self::$router->getCurrentView();

        if (
            self::$container->getParameter('isProduction')
            && !self::$httpRequest->isAjax()
            && FUser::isAdminSignedIn()
        )
            PanelExt::$showDebugBar = TRUE;

        if (isset(self::$get['redeem'])) VoucherService::enableUpgradeVoucherForm();
    }

    public function oldRun()
    {
        self::$controler = self::$router->getControler();

        try {
            $response = self::$controler->run();
            if ($response instanceof Response) $response->send();

        } catch (Throwable $e) {
            $this->errorController->handleControllerError($e);
        }
    }

    public function run()
    {
        try {
            $this->setUpLegacyDeps();
            $request = self::$container->get('symfony.request');
            FApplication::$container->get('router_module.route_processor')->handle($request);
        } catch (RouteException $e) {
            $this->oldRun();
        } catch (Throwable $e) {
            $this->errorController->handleControllerError($e);
        }
    }

    /**
     * @return boolean
     */
    public static function isAdmin()
    {
        return !empty($_SERVER['REQUEST_URI']) && str_starts_with($_SERVER['REQUEST_URI'], '/admin/');
    }

    /**
     * @return boolean
     */
    public static function hasAllowedFriendlyUrl()
    {
        if (isset(self::$config['friendly_url']))
            return (bool)self::$config['friendly_url'];
        else
            return false;
    }

    /**
     * @param string $text
     * @return string
     */
    public static function modifyCmsText($text)
    {
        $parser = Registry::getService(DiLocator::UTILS_TEXT_PARSER);
        return $parser->parse($text);
    }

    /**
     * @param string $namespace
     * @return object $cache
     */
    public static function getCache($namespace = 'cache-cms')
    {
        return new Cache(Registry::getService(DiLocator::CACHE_NETTE_FILESYSTEM), $namespace);
    }

    public static function getControlers($type)
    {
        $controlers = [];
        switch ($type) {
            case 'front':
                $files = FTools::scanDir(FRONT_CONTROLERS_DIR, FTools::RETURN_FILES);
                foreach ($files as $key => $file) {
                    if ($file == 'BaseControler.php' || $file == 'ErrorControler.php')
                        continue;
                    $key = substr($file, 0, -4);
                    $controlers[$key] = preg_replace('#Controler|\.php#', '', $file);
                }
                asort($controlers);
                break;
            case 'admin':
                // from project admin
                //$files = FTools::scanDir(ADMIN_PROJECT_CONTROLERS_DIR, FTools::RETURN_FILES);
                //foreach ($files as $key => $file) {
                //$key = substr($file, 0, -4);
                //$controlers['Custom'][$key] = preg_replace('#AdminControler|\.php#', '', $file);
                //}
                //asort($controlers['Custom']);
                // from admin
                $files = FTools::scanDir(ADMIN_CONTROLERS_DIR, FTools::RETURN_FILES);
                foreach ($files as $key => $file) {
                    if (in_array($file, ['BaseAdminControler.php', 'ErrorAdminControler.php', 'AuthAdminControler.php']))
                        continue;
                    $key = substr($file, 0, -4);
                    $controlers['Main'][$key] = preg_replace('#AdminControler|\.php#', '', $file);
                }
                asort($controlers['Main']);
                break;
        }
        return $controlers;
    }

    /**
     * @return FTemplate
     */
    public static function getTemplate()
    {
        return self::$template;
    }
}
