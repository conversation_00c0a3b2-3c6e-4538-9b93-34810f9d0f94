import { isCountryUk, isPostCodeValid } from "../helpers/addressHelper";
import { DateOfBirthValidator } from "./dateOfBirthValidator";
import {countryPhoneCodeList} from "@/modules/CompanyIncorporation/constants/countryPhoneCodeList";
import {
    englishKeyboardValidator,
    findInvalidChar
} from "@/modules/CompanyIncorporation/validators/englishKeyboardValidator";

interface PersonalDetails {
    firstName?: string;
    middleName?: string;
    lastName?: string;
    phone?: string;
    titleId?: string;
    dateOfBirth: {
        day: string;
        month: string;
        year: string;
    };
}

interface Address {
    address1?: string;
    address2?: string;
    city?: string;
    postcode?: string;
    countryIso?: string;
    recipientName?: string;
}

interface Addresses {
    primaryAddress: Address;
    invoiceAddress: Address;
    differentInvoiceAddress?: boolean;
}

export class AboutYourselfValidator {
    personalDetails: PersonalDetails;
    addresses: Addresses;
    errors: Record<string, string> = {};
    maxPhoneLength = 14;

    constructor(personalDetails: PersonalDetails, addresses: Addresses) {
        this.personalDetails = personalDetails;
        this.addresses = addresses;
    }

    validate(): boolean {
        this.errors = {};
        this.validatePersonalDetails();
        this.validatePrimaryAddress();
        if (this.addresses.differentInvoiceAddress) {
            this.validateInvoiceAddress();
        }
        return Object.keys(this.errors).length === 0;
    }

    private addError(field: string, message: string) {
        this.errors[field] = message;
    }

    private validatePersonalDetails() {
        const { firstName, middleName, lastName, phone, titleId, dateOfBirth } = this.personalDetails;
        const phoneRegex = /^\+?\d+$/;

        if (!firstName?.trim()) {
            this.addError('firstName', 'First name is required.');
        }
        if (!englishKeyboardValidator(firstName)) {
            this.addError('firstName',`Invalid character (${findInvalidChar(firstName)}). Please only use characters found on an English keyboard`);
        }

        if (!lastName?.trim()) {
            this.addError('lastName', 'Last name is required.');
        }
        if (!englishKeyboardValidator(lastName)) {
            this.addError('lastName',`Invalid character (${findInvalidChar(lastName)}). Please only use characters found on an English keyboard`);
        }

        if (!englishKeyboardValidator(middleName)) {
            this.addError('middleName',`Invalid character (${findInvalidChar(middleName)}). Please only use characters found on an English keyboard`);
        }

        if (phone && phone.length > 14) {
            for (const country of countryPhoneCodeList) {
                if (country.code.length < 4) {
                    continue;
                }

                if (phone.startsWith(country.code)) {
                    this.maxPhoneLength = 16;
                    break;
                }
            }
        }

        if (!phone || !phoneRegex.test(phone) || phone.replace(/^\+/, '').replace(/\s/g, '').length > this.maxPhoneLength) {
            this.addError('phone', 'Maximum length allowed is 14 digits. Only numbers are allowed, no spaces. A plus (+) is allowed, but only at the start.');
        }

        if (!titleId || titleId === '0' || titleId === '') {
            this.addError('title', 'Title is required.');
        }

        const dateOfBirthValidator = new DateOfBirthValidator(dateOfBirth)
        if (!dateOfBirthValidator.validate()) {
            this.errors = { ...this.errors, ...dateOfBirthValidator.errors };
        }
    }

    private validatePrimaryAddress() {
        this.validateAddress(this.addresses.primaryAddress, 'primaryAddress');
    }

    private validateInvoiceAddress() {
        if (!this.addresses.invoiceAddress.recipientName?.trim()) {
            this.addError(`invoiceAddressRecipientName`, 'Please provide the Company name.');
        }
        if (!englishKeyboardValidator(this.addresses.invoiceAddress.recipientName)) {
            this.addError('invoiceAddressRecipientName',`Invalid character (${findInvalidChar(this.addresses.invoiceAddress.recipientName)}). Please only use characters found on an English keyboard`);
        }

        this.validateAddress(this.addresses.invoiceAddress, 'invoiceAddress');
    }

  private validateAddress(address: Address, prefix: string) {
      if (!address.countryIso) {
          this.addError(`${prefix}CountryIso`, 'Please provide a country.');
      }

      if (!address.postcode?.trim()) {
          this.addError(`${prefix}Postcode`, 'Please provide a postcode. (If UK postcode please add 1 space in the middle)');
      } else if (isCountryUk(address.countryIso) && !isPostCodeValid(address.postcode)) {
          this.addError(`${prefix}Postcode`, 'Postcode is invalid.');
      }
      if (!englishKeyboardValidator(address.postcode)) {
          this.addError(`${prefix}Postcode`,`Invalid character (${findInvalidChar(address.postcode)}). Please only use characters found on an English keyboard`);
      }

      if (!address.address1?.trim()) {
          this.addError(`${prefix}Address1`, 'Please provide the first line of your address.');
      }
      if (!englishKeyboardValidator(address.address1)) {
          this.addError(`${prefix}Address1`,`Invalid character (${findInvalidChar(address.address1)}). Please only use characters found on an English keyboard`);
      }

      if (!englishKeyboardValidator(address.address2)) {
          this.addError(`${prefix}Address2`,`Invalid character (${findInvalidChar(address.address2)}). Please only use characters found on an English keyboard`);
      }

      if (!address.city?.trim()) {
          this.addError(`${prefix}City`, 'Please provide a city.');
      }
      if (!englishKeyboardValidator(address.city)) {
          this.addError(`${prefix}City`,`Invalid character (${findInvalidChar(address.city)}). Please only use characters found on an English keyboard`);
      }
  }
  
}
