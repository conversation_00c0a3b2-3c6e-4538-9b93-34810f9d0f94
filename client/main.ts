import * as bootstrap from 'bootstrap';
import './css/main.scss'
import Vue from 'vue';
import CredasWithDynamicDependencies from './modules/idcheck/components/CredasWithDynamicDependencies.vue';
import CredasWithStaticDependencies from './modules/idcheck/components/CredasWithStaticDependencies.vue';
import CredasCorporateWithDynamicDependencies from './modules/idcheck/components/CredasCorporateWithDynamicDependencies.vue';
import CredasCorporateWithStaticDependencies from './modules/idcheck/components/CredasCorporateWithStaticDependencies.vue';
import MoneyPennyNumbers from "./modules/moneyPennyNumbers/components/Main.vue";
import SicComponent from './modules/CompanyFormation/components/Sic/Main.vue';
import CompanyNameSearch from './modules/CompanyFormation/components/CompanyNameSearch/Main.vue';
import GhostContent from './modules/ghost/GhostContent.vue';
import StepTitle from './modules/CompanyIncorporation/components/StepTitle.vue';
import StepButton from './modules/CompanyIncorporation/components/StepButton.vue';
import ChangeCompanyName from './modules/CompanyIncorporation/components/ChangeCompanyName.vue';
import IncorporationSteps from './modules/CompanyIncorporation/components/IncorporationSteps.vue';
import AddressComponent from './modules/CompanyIncorporation/components/AddressComponent.vue';
import RegisteredOffice from './modules/CompanyIncorporation/components/CompanyFormationStep/RegisteredOffice.vue';
import MailForwardingAddressComponent from './modules/CompanyIncorporation/components/CompanyFormationStep/MailForwardingAddressComponent.vue';
import IndustryType from './modules/CompanyIncorporation/components/CompanyFormationStep/IndustryType.vue';
import BusinessServicesStep from "@/modules/CompanyIncorporation/components/BusinessServicesStep/BusinessServicesStep.vue";
import BusinessBanking from "@/modules/CompanyIncorporation/components/CompanyFormationStep/BusinessBanking.vue"
import AppointmentsStep from './modules/CompanyIncorporation/components/AppointmentsStep/AppointmentsStep.vue';
import AppointmentsSummary from './modules/CompanyIncorporation/components/AppointmentsStep/AppointmentsSummary.vue';
import CallAndMessagesStep from './modules/CompanyIncorporation/components/CallAndMessagesStep/CallAndMessagesStep.vue';
import AboutYourselfStep from './modules/CompanyIncorporation/components/AboutYourselfStep/AboutYourselfStep.vue';
import MemorandumAndArticlesStep
  from "@/modules/CompanyIncorporation/components/MemorandumAndArticlesStep/MemorandumAndArticlesStep.vue";
import SummaryStep from "@/modules/CompanyIncorporation/components/SummaryStep/SummaryStep.vue";
import CountryPhoneCodeSelector from "./modules/CompanyIncorporation/components/global/CountryPhoneCodeSelector.vue";
import inboxSettings from "./modules/MailScan/components/InboxSettings.vue";
import CompanyInbox from "./modules/MailScan/components/CompanyInbox/CompanyInbox.vue";

import store from './store';

import {Observable, Subscription, Subject} from 'rxjs';
import VueRx from 'vue-rx';

Vue.use(VueRx, {Observable, Subscription, Subject});

Vue.config.productionTip = false;
//Vue.config.devtools = true;

const el = document.getElementById("app");

const myDetailsCustomerPhoneElement = document.getElementById("my-details-customer-phone");

if (el) {
  new Vue({
    store,
    components: {
      CompanyNameSearch,
      CredasWithDynamicDependencies,
      CredasWithStaticDependencies,
      CredasCorporateWithDynamicDependencies,
      CredasCorporateWithStaticDependencies,
      MoneyPennyNumbers,
      SicComponent,
      GhostContent,
      StepTitle,
      StepButton,
      ChangeCompanyName,
      IncorporationSteps,
      AddressComponent,
      RegisteredOffice,
      MailForwardingAddressComponent,
      IndustryType,
      BusinessServicesStep,
      CompanyFormationBusinessBanking: BusinessBanking,
      AppointmentsStep,
      AppointmentsSummary,
      MemorandumAndArticlesStep,
      CallAndMessagesStep,
      AboutYourselfStep,
      SummaryStep,
      CountryPhoneCodeSelector,
      inboxSettings,
      CompanyInbox
    },
  }).$mount(el);
}

if (myDetailsCustomerPhoneElement) {
  new Vue({
    store,
    components: {
      CountryPhoneCodeSelector
    },
  }).$mount(myDetailsCustomerPhoneElement);
}

(<any>window).bootstrap = bootstrap

// loading Popper
var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
  return new bootstrap.Tooltip(tooltipTriggerEl)
})




