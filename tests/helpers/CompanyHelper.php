<?php

namespace tests\helpers;

use BankingModule\Entities\CompanyCustomer as BankingCompanyCustomer;
use CompaniesHouseModule\Entities\Address;
use CompaniesHouseModule\Entities\DirectorPerson;
use CompaniesHouseModule\Entities\Member;
use CompaniesHouseModule\Entities\PscCorporate;
use CompaniesHouseModule\Entities\SecretaryPerson;
use Entities\Company;
use Entities\Customer;
use Entities\Customer as CustomerEntity;
use Entities\Order;
use Id3GlobalApiClient\Entities\Address as Id3GlobalAddress;
use Id3GlobalApiClient\Entities\PersonalDetails;
use IdModule\Domain\CompanyDetails;
use Libs\CHFiling\Core\Banking\CardOne;
use Libs\CHFiling\Core\Company as OldCompany;
use Models\OldModels\CFControllerModel;
use Models\OldModels\CompanyCustomer;
use Models\OldModels\Customer as OldCustomer;
use Models\Products\Package;
use Models\Products\Product;
use Utils\Date;

class CompanyHelper
{
    /**
     * @param Customer|CustomerEntity $customer
     * @param string                  $companyNumber
     *
     * @return OldCompany
     */
    public static function createCompany($customer, $companyNumber = null)
    {
        $company = OldCompany::getNewCompany($customer->getId(), 'Test Company');

        if (isset($companyNumber)) {
            $company->setCompanyNumber($companyNumber);
        }

        return $company;
    }

    /**
     * @param OldCompany  $company
     * @param OldCustomer $customer
     */
    public static function addCardOne(OldCompany $company, OldCustomer $customer)
    {
        $companyCustomer = new CompanyCustomer();
        $companyCustomer->bankTypeId = BankingCompanyCustomer::BANK_TYPE_CARD_ONE;
        $companyCustomer->preferredContactDate = 0;
        $companyCustomer->wholesalerId = $customer->getId();
        $companyCustomer->companyId = $company->getCompanyId();
        $companyCustomer->email = 'TEST';
        $companyCustomer->titleId = 'TEST';
        $companyCustomer->firstName = 'TEST';
        $companyCustomer->lastName = 'TEST';
        $companyCustomer->phone = 'TEST';
        $companyCustomer->postcode = 'TEST';

        $companyCustomer->address1 = 'null';
        $companyCustomer->address3 = 'null';
        $companyCustomer->city = 'null';
        $companyCustomer->countryId = 'null';
        $companyCustomer->consent = 0;

        $companyCustomer->save();

        $service = new CFControllerModel($company, $customer);
        $service->addBankOrderItem(CardOne::CARD_ONE_PRODUCT);
    }

    public static function createFullCompany(
        CustomerEntity $customer,
        string $companyName,
        string $companyNumber,
        //        DiligenceLevelFactory $diligenceLevelFactory,
        int $productId = Package::PACKAGE_PRIVACY,
        string $category = 'BYSHR',
        bool $expired = false,
        bool $includeRegisteredOffice = true,
    ) {
        $company = new Company($customer, $companyName);
        $company->setProductId($productId);
        $company->setIsCertificatePrinted(true);
        $company->setIsMaCoverLetterPrinted(true);
        $company->setCompanyCategory($category);
        $company->setSicCode1('01110');
        $company->setEreminderId(0);
        $company->setCompanyNumber($companyNumber);
        $company->setCompanyStatus('Active');
        $company->setIncorporationDate(new Date(sprintf('-%d days', rand(1, 7200))));
        $company->setRegisteredOffice(
            new Address(
                '4',
                'Sesame street',
                'London',
                'N1 7GU',
                'United Kingdom'
            )
        );
        $company->setJurisdiction('EW');

        $order = new Order($customer);
        $company->setOrder($order);

        //        $businessInformation = $customer->getBusinessInformation();
        //        if (!$businessInformation) {
        //            $companyInformation = CompanyInformation::fromRegulatedBody(true, 'regulated by', '12345678');
        //            $businessInformation = new BusinessInformation($customer,
        //                BusinessInformation::NOT_PROFESSIONAL_COMPANY_FORMATION, $companyInformation);
        //            $customer->setBusinessInformation($businessInformation);
        //        }

        //        $validations = ['validations' => [], 'events' => []];

        $members = [
            0 => self::createMember($company, DirectorPerson::class, 'Ted Morse', 'N1 7GU', new Date('-20 years')),
            1 => self::createMember($company, PscCorporate::class, 'HELIUM LTD', 'N3K 9DH0'),
            2 => self::createMember($company, SecretaryPerson::class, 'Rita Morse', 'N3K 9DH0', new Date('-20 years')),
        ];

        $service = null;
        $serviceSettings = null;
        if ($includeRegisteredOffice) {
            $service = ServicesHelper::getActiveForCompany($company);
            $service->setRenewalProduct(new Product(334));
            $service->setProduct(new Product(165));

            if ($expired) {
                $service->setDtStart(new Date('-1 year -1 day'));
                $service->setDtExpires(new Date('-1 day'));
            }

            $serviceSettings = ServiceSettingsHelper::fromService($service);
        }

        //        $entities = [
        //            IdEntity::fromPerson($company, new PersonalDetails($members[0]->getForename(), $members[0]->getSurname()), new Address()),
        //            IdEntity::fromCompany($company, new CompanyDetails(CompanyName::uppercased($members[1]->getCorporateName()), []), new Address()),
        //            IdEntity::fromPerson($company, new PersonalDetails($members[2]->getForename(), $members[2]->getSurname()), new Address()),
        //        ];
        //
        //
        //        if (!$customer->getCountryIso()) {
        //            $customer->setCountryIso(Country::UNITED_KINGDOM);
        //
        //            if ($diligenceLevelFactory) {
        //                $entity = IdEntity::fromCustomer($company, $customer, null);
        //                $validations = IdEntityHelper::createEntityValidations(
        //                    $entity,
        //                    $diligenceLevelFactory->createDiligenceLevel($entity)
        //                );
        //            }
        //        }
        //
        //        if ($customer->isUkCustomer()) {
        //            // should we check if validated ?
        //            if ($diligenceLevelFactory) {
        //                $entity = IdEntity::fromCustomer($company, $customer, null);
        //                $validations = IdEntityHelper::createEntityValidations(
        //                    $entity,
        //                    $diligenceLevelFactory->createDiligenceLevel($entity)
        //                );
        //            }
        //        }
        return array_merge(
            [$customer, $service, $serviceSettings, $order, 'company' => $company],
            $members
        );
    }

    public static function createFullCompanyWithMailboxService(
        CustomerEntity $customer,
        string $companyName,
        string $companyNumber,
        int $formationProductId = Package::PACKAGE_BASIC,
        string $category = 'BYSHR',
        ?string $mailboxServiceName = null,
        bool $overdueMailboxService = false,
    ) {
        $company = new Company($customer, $companyName);
        $company->setProductId($formationProductId);
        $company->setIsCertificatePrinted(true);
        $company->setIsMaCoverLetterPrinted(true);
        $company->setCompanyCategory($category);
        $company->setSicCode1('01110');
        $company->setEreminderId(0);
        $company->setCompanyNumber($companyNumber);
        $company->setCompanyStatus('Active');
        $company->setIncorporationDate(new Date(sprintf('-150 days')));
        $company->setRegisteredOffice(
            new Address(
                '4',
                'Sesame street',
                'London',
                'N1 7GU',
                'United Kingdom'
            )
        );
        $company->setJurisdiction('EW');

        $order = new Order($customer);
        $orderItem = ObjectHelper::createOrderItem($order);
        $company->setOrder($order);

        $members = [
            0 => self::createMember($company, DirectorPerson::class, 'Ted Morse', 'N1 7GU', new Date('-20 years')),
            1 => self::createMember($company, PscCorporate::class, 'HELIUM LTD', 'N3K 9DH0'),
            2 => self::createMember($company, SecretaryPerson::class, 'Rita Morse', 'N3K 9DH0', new Date('-20 years')),
        ];

        if (isset($mailboxServiceName)) {
            $service = ObjectHelper::createServiceFromProductName(
                $company,
                $orderItem,
                $mailboxServiceName,
                new Date('-150 days'),
                new Date('+175 days')
            );
            $service->getRenewalProductId();
            if ($overdueMailboxService) {
                $service->setDtExpires(new Date('-5 day'));
            }
            $company->addService($service);
            return array_merge([$customer, $service, $order, 'company' => $company], $members);
        }

        $service = ObjectHelper::createServiceFromProductName(
            $company,
            $orderItem,
            $formationProductId,
            new Date('-150 days'), new Date('+175 days')
        );
        if ($overdueMailboxService) {
            $service->setDtExpires(new Date('-5 day'));
        }
        $company->addService($service);

        return array_merge([$customer, $service, $order, 'company' => $company], $members);
    }

    public static function createMember(Company $company, $memberClass, $name, $postCode, ?Date $personDateOfBirth = null): Member
    {
        if ($personDateOfBirth) {
            $names = explode(' ', $name);
            $member = $memberClass::fromPersonalDetails(
                new PersonalDetails($names[0], $names[1], $personDateOfBirth),
                new Id3GlobalAddress('1', null, null, null, null, $postCode)
            );
        } else {
            $member = $memberClass::fromCompanyDetails(
                CompanyDetails::fromCompany($company),
                new Id3GlobalAddress('1', null, null, null, null, $postCode)
            );
        }
        $member->setCompany($company);

        return $member;
    }

    public static function createIncorporatedCompanies(
        CustomerEntity $customer,
        //        DiligenceLevelFactory $digitalLevelFactory,
        //        ToolkitOfferService $offerService,
        int $count,
    ): array {
        $packages = [
            Package::PACKAGE_BASIC,
            Package::PACKAGE_PRIVACY,
            Package::PACKAGE_COMPREHENSIVE,
            Package::PACKAGE_ULTIMATE,
        ];
        $companies = [];

        while ($count-- > 0) {
            $companies[] = $company = self::createFullCompany(
                $customer,
                'MANY-' . date('YmdHis') . '-' . $count,
                random_int(10000000, 99999999),
                $packages[array_rand($packages)]
                //                $digitalLevelFactory
            );
        }

        return $companies;
    }

    public static function createCompanyWithExpiredPackage(CustomerEntity $customer, int $package = Package::PACKAGE_ULTIMATE)
    {
        return self::createFullCompany(
            $customer,
            'EXP-' . date('YmdHis'),
            random_int(10000000, 99999999),
            $package,
            'BYSHR',
            true
        );
    }
}
