<?php

namespace tests\project\models\LTDCalculator;

use InvalidArgumentException;
use PHPUnit\Framework\TestCase;
use SelfEmployedCalculator;

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.1 on 2014-04-16 at 11:06:45.
 */
class SelfEmployedCalculatorTest extends TestCase
{
    public function setUp(): void
    {
        $this->markTestIncomplete('Calculator constants waiting for update');
    }
    
    /**
     * @covers SelfEmployedCalculator::__construct
     */
    public function testGetNationalInsuranceMinusRange()
    {
        $this->expectException(InvalidArgumentException::class);
        $object = new SelfEmployedCalculator(-2);
    }

    /**
     * @covers SelfEmployedCalculator::__construct
     */
    public function testGetNationalInsuranceZero()
    {
        $this->expectException(InvalidArgumentException::class);
        $object = new SelfEmployedCalculator(0);
    }

    /**
     * @covers SelfEmployedCalculator::__construct
     */
    public function testGetNationalInsuranceUpperRange()
    {
        $this->expectException(InvalidArgumentException::class);
        $object = new SelfEmployedCalculator(100000000001);
    }


    /**
     * @covers Calculator::getTaxPayable
     */
    public function testGetTaxPayable()
    {
        $object = new SelfEmployedCalculator(11111);
        $this->assertEquals(102.20, $object->getTaxPayable());
        $object = new SelfEmployedCalculator(1222);
        $this->assertEquals(0, $object->getTaxPayable());
        $object = new SelfEmployedCalculator(152222);
        $this->assertEquals(54642.90, $object->getTaxPayable());
        $object = new SelfEmployedCalculator(10000000000);
        $this->assertEquals(4499986143, $object->getTaxPayable());
    }

    /**
     * @covers SelfEmployedCalculator::getNationalInsurance
     */
    public function testGetNationalInsurance()
    {
        $object = new SelfEmployedCalculator(11111);
        $this->assertEquals(420.19, $object->getNationalInsurance());
        $object = new SelfEmployedCalculator(1222);
        $this->assertEquals(145.60, $object->getNationalInsurance());
        $object = new SelfEmployedCalculator(152222);
        $this->assertEquals(5431.59, $object->getNationalInsurance());
        $object = new SelfEmployedCalculator(10000000000);
        $this->assertEquals(*********.15, $object->getNationalInsurance());
    }

    /**
     * @covers SelfEmployedCalculator::getTotalPayable
     */
    public function testGetTotalPayable()
    {
        $object = new SelfEmployedCalculator(11111);
        $this->assertEquals(522.39, $object->getTotalPayable());
        $object = new SelfEmployedCalculator(1222);
        $this->assertEquals(145.60, $object->getTotalPayable());
        $object = new SelfEmployedCalculator(152222);
        $this->assertEquals(60074.49, $object->getTotalPayable());
        $object = new SelfEmployedCalculator(10000000000);
        $this->assertEquals(4699988530.15, $object->getTotalPayable());
    }

    /**
     * @covers SelfEmployedCalculator::getNetSpendablePersonalIncome
     */
    public function testGetNetSpendablePersonalIncome()
    {
        $object = new SelfEmployedCalculator(11111);
        $this->assertEquals(10588.61, $object->getNetSpendablePersonalIncome());
        $object = new SelfEmployedCalculator(1222);
        $this->assertEquals(1076.40, $object->getNetSpendablePersonalIncome());
        $object = new SelfEmployedCalculator(152222);
        $this->assertEquals(92147.51, $object->getNetSpendablePersonalIncome());
        $object = new SelfEmployedCalculator(10000000000);
        $this->assertEquals(5300011469.85, $object->getNetSpendablePersonalIncome());
    }
}
