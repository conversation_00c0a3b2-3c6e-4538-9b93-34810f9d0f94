<?php

namespace tests\project\MarketingModule\Repositories;

use DateTime;
use Doctrine\DBAL\Exception as DBALException;
use MarketingModule\Entities\NamescoVoucher;
use MarketingModule\Repositories\NamescoVoucherRepository;
use TestModule\Annotations\Inject;
use TestModule\Helpers\DatabaseHelper;
use TestModule\PhpUnit\TestCase;
use tests\helpers\EntityHelper;

class NamescoVoucherRepositoryTest extends TestCase
{
    /**
     * @var NamescoVoucherRepository
     */
    private $repository;

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @Inject({
     *     "repository"="marketing_module.repositories.namesco_voucher_repository",
     *     "databaseHelper"="test_module.database_helper"
     * })
     * @throws DBALException
     */
    public function setupDependencies(NamescoVoucherRepository $repository, DatabaseHelper $databaseHelper)
    {
        $this->repository = $repository;
        $this->databaseHelper = $databaseHelper;

        $this->emptyTables();
    }

    public function testEntity()
    {
        $customer = EntityHelper::createCustomer();
        $company = EntityHelper::createCompany($customer);

        $voucher = new NamescoVoucher('voucher_code');

        $this->repository->saveEntity($voucher);
        $this->assertNotNull($voucher->getId());

        $voucher->setCompany($company);
        $voucher->setDateSent(new DateTime());

        $this->repository->saveEntity($voucher);
        $this->assertNotNull($voucher->getDateSent());
        $this->assertNotNull($voucher->getCompany());
    }

    protected function tearDown(): void
    {
        $this->emptyTables();
    }

    /**
     * @throws DBALException
     */
    private function emptyTables()
    {
        $this->databaseHelper->emptyTables(
            [
                TBL_CUSTOMERS,
                TBL_COMPANIES,
                TBL_NAMESCO_VOUCHERS,
            ]
        );
    }


}