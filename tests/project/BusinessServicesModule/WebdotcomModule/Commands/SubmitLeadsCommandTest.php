<?php

namespace tests\project\BusinessServicesModule\WebdotcomModule\Commands;

use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Entities\PartnerServicesInformation;
use BusinessServicesModule\Providers\MediatorProvider;
use BusinessServicesModule\Repositories\LeadRepository;
use BusinessServicesModule\WebdotcomModule\Commands\SubmitLeadsCommand;
use BusinessServicesModule\WebdotcomModule\Factories\LeadsToProcessCriteriaFactory;
use EmailModule\IEmailGateway;
use EmailModule\Repositories\ITemplateEmailRepository;
use Entities\Company;
use Entities\Customer;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use TestModule\Annotations\Inject;
use TestModule\Helpers\DatabaseHelper;
use TestModule\PhpUnit\TestCase;
use tests\helpers\CompanyHouse\IncorporationHelper;
use tests\helpers\EntityHelper;
use Utils\Date;

class SubmitLeadsCommandTest extends TestCase
{
    const WEBDOTCOM_LEAD_EMAIL = 'webdotcom-lead-email';
    const WEBDOTCOM_LEAD_ID = 26;

    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var Company
     */
    private $company;

    /**
     * @var SubmitLeadsCommand
     */
    private $command;

    /**
     * @var LeadRepository
     */
    private $leadRepository;

    /**
     * @var LeadsToProcessCriteriaFactory
     */
    private $criteriaFactory;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var ITemplateEmailRepository
     */
    private $emailRepository;

    /**
     * @var IEmailGateway
     */
    private $emailGateway;

    /**
     * @var MediatorProvider
     */
    private $mediatorProvider;

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @Inject({
     *     "leadRepository" = "business_services_module.repositories.lead_repository",
     *     "criteriaFactory" = "business_services_module.webdotcom_module.factories.leads_to_process_criteria_factory",
     *     "logger" = "cron.loggers.default_logger",
     *     "emailRepository" = "email_module.repositories.predefined_email_repository",
     *     "emailGateway"="email_module.gateways.restricted_mailgun_gateway",
     *     "mediatorProvider"="business_services_module.providers.mediator_provider",
     *     "databaseHelper"="test_module.helpers.database_helper"
     * })
     */

    public function setupDependencies(
        LeadRepository $leadRepository,
        LeadsToProcessCriteriaFactory $criteriaFactory,
        LoggerInterface $logger,
        ITemplateEmailRepository $emailRepository,
        IEmailGateway $emailGateway,
        MediatorProvider $mediatorProvider,
        DatabaseHelper $databaseHelper
    )
    {
        $this->leadRepository = $leadRepository;
        $this->criteriaFactory = $criteriaFactory;
        $this->logger = $logger;
        $this->emailRepository = $emailRepository;
        $this->emailGateway = $emailGateway;
        $this->mediatorProvider = $mediatorProvider;
        $this->databaseHelper = $databaseHelper;
        $this->command = new SubmitLeadsCommand(
            $this->leadRepository,
            $this->criteriaFactory,
            $this->logger,
            $this->emailRepository,
            $this->emailGateway,
            $this->mediatorProvider
        );

        $this->clearTables();
    }

    public function tearDown(): void
    {
        $this->clearTables();
    }

    public function testRun()
    {
        $this->init();
        $this->command->sendLeads();
        $this->assertTrue($this->leads[0]->isProcessed());
        $this->assertNotTrue($this->leads[1]->isProcessed());
        $this->assertTrue($this->leads[2]->isProcessed());
    }

    private function clearTables(): void
    {
        EntityHelper::emptyTables(EntityHelper::$tables);
    }

    private function init(): void
    {
        $this->customer = EntityHelper::createCustomer('<EMAIL>');

        $entities = IncorporationHelper::createReadyToBeIncorporated($this->customer, Company::COMPANY_CATEGORY_BYSHR);
        /** @var Company $company */
        $company = $entities['company'];
        $company->setCompanyNumber('10000001');
        $company->setIncorporationDate(new Date('-1 month'));

        $this->databaseHelper->saveEntities($entities);
        $this->databaseHelper->clearEntities();

        $this->company = $this->databaseHelper->find(Company::class, $company->getId());

        $l1 = new Lead($this->company, self::WEBDOTCOM_LEAD_ID, true);
        $l2 = new Lead($this->company, self::WEBDOTCOM_LEAD_ID, false);
        $l3 = new Lead($this->company, self::WEBDOTCOM_LEAD_ID, true);

        $this->leads = [$l1, $l2, $l3];
        $this->databaseHelper->saveEntities($this->leads);


        $info = new PartnerServicesInformation();
        $info->setLead($l1);
        $info->setFirstName('Tester');
        $l1->setPartnerServicesInformation($info);

        $info = new PartnerServicesInformation();
        $info->setLead($l2);
        $info->setFirstName('Tester');
        $l2->setPartnerServicesInformation($info);

        $info = new PartnerServicesInformation();
        $info->setLead($l3);
        $info->setFirstName('Tester');
        $l3->setPartnerServicesInformation($info);

        $this->databaseHelper->saveEntities($this->leads);
    }

}
