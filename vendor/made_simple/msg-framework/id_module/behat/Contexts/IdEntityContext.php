<?php

namespace IdModule\Contexts;

use Behat\Behat\Context\Context;
use Behat\Gherkin\Node\TableNode;
use CompanyModule\Domain\Company\CompanyName;
use CompanyModule\ICompany;
use CustomerModule\Entities\BusinessInformation;
use CustomerModule\Entities\CompanyInformation;
use DateTime;
use Entities\Company;
use Entities\Customer;
use Id3GlobalApiClient\Entities\Address;
use Id3GlobalApiClient\Entities\PersonalDetails;
use IdModule\Domain\Check;
use IdModule\Domain\CompanyDetails;
use IdModule\Domain\DiligenceLevel;
use IdModule\Domain\IdEntity;
use IdModule\Entities\IdValidation;
use IdModule\Factories\IDiligenceLevelFactory;
use IdModule\Repositories\ICompanyEntityProvider;
use IdModule\Repositories\IdInfoRepository;
use IdModule\Repositories\IdValidationRepository;
use IdModule\Views\IdCompanyInfo;
use IdModule\Views\IdCustomerInfo;
use IdModule\Views\IdEntityInfo;
use InvalidArgumentException;
use PHPUnit\Framework\Assert as test;
use Prophecy\Argument;
use function random_int;
use Models\Products\RegisterOffice;
use TestModule\Behat\ObjectInjectorContext;
use UserModule\Contracts\ICustomer;
use Utils\Date;

class IdEntityContext extends ObjectInjectorContext implements Context
{
    /**
     * @var IdEntity[]
     */
    private $entities;

    /**
     * @var IdValidation[]
     */
    private $validations;

    /**
     * @var ICompany[]
     */
    private $companies;

    /**
     * @var array
     */
    private $companyEntities;

    /**
     * @var ICustomer[]
     */
    private $customers;

    /**
     * @var array
     */
    private $customerCompanies;

    /**
     * @var DiligenceLevel[]
     */
    private $diligenceLevels;

    /**
     * @var IdInfoRepository
     */
    private $idInfoRepository;

    public function setUpWithMocks(
        IDiligenceLevelFactory $diligenceLevelFactory,
        IdValidationRepository $validationRepository,
        ICompanyEntityProvider $entityProvider
    )
    {
        $this->idInfoRepository = new IdInfoRepository($diligenceLevelFactory, $validationRepository, $entityProvider);
        $diligenceLevels = &$this->diligenceLevels;
        $idEntities = &$this->entities;
        $this->diligenceLevelFactory->createDiligenceLevel(Argument::type(IdEntity::class))->will(function ($args) use (&$diligenceLevels, &$idEntities) {
            $index = array_search($args[0], $idEntities);
            return $diligenceLevels[$index];
        });
        $validations = &$this->validations;
        $this->validationRepository->getLastValidationsWithoutFilters(Argument::type(IdEntity::class))->will(function($args) use (&$validations, &$idEntities) {
            $index = array_search($args[0], $idEntities);
            if (!isset($validations[$index])) {
                return [];
            }
            return $validations[$index];
        });
        $this->validationRepository->getLastValidations(Argument::type(IdEntity::class))->will(function($args) use (&$validations, &$idEntities) {
            $index = array_search($args[0], $idEntities);
            if (!isset($validations[$index])) {
                return [];
            }
            return array_filter($validations[$index], function(IdValidation $validation) {
                return !$validation->isNullified();
            });
        });
    }

    /**
     * @Transform /^(valid|not valid|failed)$/
     */
    public function transformValidToBool($name)
    {
        return $name === 'valid';
    }

    /**
     * @Given /^the following customers:$/
     */
    public function customersExist(TableNode $table)
    {
        foreach ($table as $row) {

            /** @var Customer $customer */
            $customer = $this->customers[$row['email']] = Customer::temporary($row['email']);
            $customer->setDtc(new DateTime($row['dtc'] ?? 'now'));

            if (isset($row['professional'])) {

                $companyInformation = null;
                if ($row['registeredBody'] === 'true') {
                    $companyInformation = CompanyInformation::fromRegulatedBody(
                        true,
                        'regulated body',
                        12345678
                    );
                }

                $businessInformation = new BusinessInformation(
                    $customer,
                    $row['professional'] === 'true'
                        ? BusinessInformation::PROFESSIONAL_COMPANY_FORMATION : BusinessInformation::NOT_PROFESSIONAL_COMPANY_FORMATION,
                    $companyInformation
                );


                $customer->setBusinessInformation($businessInformation);
            }
            if (isset($row['customerPostcode'])) {
                $customer->setPostcode($row['customerPostcode']);
                $customer->setCountryIso('GB');
            } elseif (isset($row['customerCountry'])) {
                $customer->setCountryIso($row['customerCountry']);
            }
            if ($row['hasCompany'] !== 'false') {
                $company = new Company($customer, $row['hasCompany']);
                $this->companies[$row['hasCompany']] = $company;

                if (isset($row['companyStatus'])) {
                    $company->setCompanyStatus($row['companyStatus']);
                }
                if (isset($row['companyPostcode'])) {
                    $company->setPostcode($row['companyPostcode']);
                    $company->setCountry('GBR');
                }
                if (isset($row['incorporationDate'])) {
                    $company->setCompanyNumber(random_int(100000, 1000000));
                    $company->setIncorporationDate(new Date($row['incorporationDate']));
                }
            }
        }
    }

    /**
     * @Given /^customer (.+) has the following companies:$/
     */
    public function customerHasTheFollowingCompanies(string $email, TableNode $table)
    {
        $this->customers[$email] = Customer::temporaryWithId($email);
        foreach ($table as $row) {
            $this->customerCompanies[$email][] = $row['companyName'];
            $company = new Company($this->customers[$email], $row['companyName']);
            $this->companies[$row['companyName']] = $company;
            if (isset($row['companyStatus'])) {
                $company->setCompanyStatus($row['companyStatus']);
            }
            if (isset($row['incorporationDate'])) {
                $company->setCompanyNumber(1);
                $company->setIncorporationDate(new Date($row['incorporationDate']));
            }
        }
    }

    /**
     * @Given /^the following entities:$/
     */
    public function entitiesExist(TableNode $table)
    {
        foreach ($table as $row) {
            if (isset($row['entityOverallIdCheckStatus'])) {
                $company = new Company(Customer::temporaryWithId('test1'), $row['companyName']);
                $this->companyEntities[$row['companyName']][] = $row['entityId'];
                $this->companies[$row['companyName']] = $company;
                $this->diligenceLevels[$row['entityId']] = DiligenceLevel::fromArr('test', ['PEP' => ['responsible_verifier' => 'c']]);
                if ($row['entityOverallIdCheckStatus'] === 'valid') {
                    $this->validations[$row['entityId']]['PEP'] = new IdValidation($row['entityId'], TRUE, [], 'PEP');
                }
                $this->entities[$row['entityId']] = IdEntity::fromPerson($company, new PersonalDetails(), new Address());
            } else {
                $this->companyHasTheFollowingEntities($row['companyName'], new TableNode([
                    0 => array_keys($row) + ['type'],
                    1 => array_values($row) + [IdEntity::COMPANY]
                ]));
            }
        }
    }

    /**
     * @Given /^the following companies:$/
     */
    public function companiesExist(TableNode $table)
    {
        $i = 1;
        foreach ($table as $row) {
            $this->customerCompanies[$row['customerId']][] = $row['companyName'];
            $this->customers[$row['customerId']] = Customer::temporaryWithId($row['customerId']);
            $this->entitiesExist(
                new TableNode([
                    0 => ['entityId', 'companyName', 'entityOverallIdCheckStatus'],
                    1 => [$i++, $row['companyName'], $row['companyOverallIdCheckStatus']]
                ])
            );
        }
    }

    /**
     * @Given /^company ([\w ]+) has the following entities:$/
     */
    public function companyHasTheFollowingEntities(string $companyName, TableNode $table)
    {
        $defaultCompany = Company::incorporated(new Customer('t', 't'), $companyName, $companyName, new Date('-1 year'));
        $defaultCompany->setPostcode(RegisterOffice::POSTCODE);

        $company = isset($this->companies[$companyName]) ? $this->companies[$companyName] : $defaultCompany;

        foreach ($table as $row) {
            if (!isset($row['name'])) {
                $row['name'] = 'Johny Bravo';
            }
            if ($row['type'] === IdEntity::PERSON) {
                $names = explode(' ', $row['name'], 3);
                $date = NULL;
                if (!empty($row['dateOfBirth'])) {
                    $date = new Date($row['dateOfBirth']);
                }
                $entity = IdEntity::fromPerson(
                    $company,
                    new PersonalDetails($names[0], isset($names[2]) ? $names[2] : $names[1], $date, NULL, isset($names[2]) ? $names[1] : NULL),
                    new Address(NULL, NULL, NULL, NULL, NULL, $row['postcode'] ?? NULL, $row['country'] ?? NULL)
                );
            } elseif ($row['type'] === IdEntity::COMPANY) {
                $entity = IdEntity::fromCompany(
                    $company,
                    new CompanyDetails(CompanyName::uppercased($row['name']), [], ''),
                    new Address(NULL, NULL, NULL, NULL, NULL, $row['postcode'] ?? NULL, $row['country'] ?? NULL)
                    //new Address(NULL, NULL, NULL, NULL, NULL, NULL, NULL)
                );
            } elseif ($row['type'] === IdEntity::CUSTOMER) {
                $entity = IdEntity::fromCustomer($company, $company->getCustomer());

            } elseif ($row['type'] === IdEntity::CUSTOMER_COMPANY) {
                $entity = IdEntity::fromCustomerCompany(
                    $company,
                    new CompanyDetails(CompanyName::uppercased($row['name']), [], NULL),
                    new Address(NULL, NULL, NULL, NULL, NULL, $row['postcode'] ?? NULL, $row['country'] ?? NULL)
                    //new Address(NULL, NULL, NULL, NULL, NULL, NULL, NULL)
                );
            }
            if (isset($row['roles'])) {
                $entity->addRoles(explode(', ', $row['roles']));
            }
            $this->entities[$row['entityId']] = $entity;
            $this->companyEntities[$companyName][] = $row['entityId'];
        }
        $this->companies[$companyName] = $company;
        return $this->entities;
    }

    /**
     * @Given following entity validations:
     */
    public function followingEntityValidations(TableNode $table)
    {
        foreach ($table as $row) {
            if (isset($row['originalData'])) {
                $originalData = json_decode($row['originalData'], TRUE);
                if ($originalData === NULL) {
                    throw new InvalidArgumentException('Failed to convert originalData. Please verify that it is valid json');
                }
            } else {
                $originalData = [];
            }
            $this->validations[$row['entityId']][$row['checkName']] = $validation = new IdValidation(
                $row['entityId'],
                $row['valid'] === 'true',
                $originalData,
                $row['checkName']
            );
            if (isset($row['checkType'])) {
                $validation->setCheckType($row['checkType']);
            }
            if (isset($row['nullified']) && $row['nullified'] === 'true') {
                $validation->markAsNullified();
            }
        }
    }

    /**
     * @Given /^entity (\w+) has diligence level (\w+) with checks:$/
     */
    public function entityHasDiligenceLevelTestWithChecks(string $entityId, string $name, TableNode $table)
    {
        $checks = [];
        foreach ($table as $row) {
            $checks[$row['name']] = Check::fromArr($row['name'], $row + ['responsible_verifier' => 'a']);
        }
        $this->diligenceLevels[$entityId] = new DiligenceLevel($name, $checks);
    }

    /**
     * @Then /^entity (\w+) should be (valid|not valid)$/
     */
    public function entityShouldBeValid(string $entityId, bool $isValid)
    {
        $idEntityInfo = $this->getIdEntityInfo($entityId);
        test::assertEquals($isValid, $idEntityInfo->isValid());
    }

    /**
     * @Then /^company ([\w ]+) should be (valid|not valid)$/
     */
    public function companyShouldBeValid(string $companyName, bool $isValid)
    {
        $idCompanyInfo = $this->getIdCompanyInfo($companyName);
        test::assertEquals($isValid, $idCompanyInfo->isValid());
    }

    /**
     * @Then /^customer (.+) should be (valid|not valid)$/
     */
    public function customerShouldBeValid(string $email, bool $isValid)
    {
        $idCompanyInfo = $this->getIdCustomerInfo($email);
        test::assertEquals($isValid, $idCompanyInfo->isValid());
    }

    /**
     * @return IdEntity[]
     */
    public function &getEntities(): ?array
    {
        return $this->entities;
    }

    /**
     * @return IdValidation[]
     */
    public function &getValidations(): ?array
    {
        return $this->validations;
    }

    /**
     * @return IdValidation[]
     */
    public function &getDiligenceLevels(): ?array
    {
        return $this->diligenceLevels;
    }

    /**
     * @param string $entityId
     * @return IdEntityInfo
     * @throws InvalidArgumentException
     */
    private function getIdEntityInfo(string $entityId): IdEntityInfo
    {
        if (!isset($this->diligenceLevels[$entityId])) {
            throw new InvalidArgumentException(sprintf('Diligence level for entity %s not found', $entityId));
        }
        if (!isset($this->entities[$entityId])) {
            throw new InvalidArgumentException(sprintf('Entity %s not found', $entityId));
        }
        return $this->idInfoRepository->getIdInfoForEntity($this->entities[$entityId]);
        //$validations =
        //return new IdEntityInfo($this->entities[$entityId], $this->diligenceLevels[$entityId], $this->validations[$entityId] ?? []);
    }

    /**
     * @param string $companyName
     * @return IdCompanyInfo
     * @throws InvalidArgumentException
     */
    private function getIdCompanyInfo(string $companyName): IdCompanyInfo
    {
        if (!isset($this->companies[$companyName])) {
            throw new InvalidArgumentException(sprintf('Company %s not found', $companyName));
        }
        $idEntityInfos = [];
        foreach ($this->companyEntities[$companyName] as $entityId) {
            $idEntityInfos[] = $this->getIdEntityInfo($entityId);
        }
        return new IdCompanyInfo($this->companies[$companyName], $idEntityInfos);
    }

    /**
     * @param string $email
     * @return IdCustomerInfo
     * @throws InvalidArgumentException
     */
    private function getIdCustomerInfo(string $email): IdCustomerInfo
    {
        if (!isset($this->customers[$email])) {
            throw new InvalidArgumentException(sprintf('Customer %s not found', $email));
        }

        $idCompanies = [];
        foreach ($this->customerCompanies[$email] as $companyName) {
            $idCompanies[] = $this->getIdCompanyInfo($companyName);
        }
        return new IdCustomerInfo($this->customers[$email], $idCompanies);
    }
}