<?php

namespace Bootstrap\Ext;

use BootstrapModule\Ext\DoctrineExt;
use BootstrapModule\Ext\FrameworkExt_Abstract;
use BootstrapModule\Ext\IExtension;
use BootstrapModule\Singletons\StaticContainer;
use chfiling\util\DoctrineEnum;
use CompanyIncorporationModule\Deciders\IncorporationDecider;
use Models\OldModels\Customer;
use Config\Constants\DiLocator;
use Framework\FApplication;
use Framework\FUser;
use Legacy\Nette\Environment;
use FeatureModule\Context;
use FeatureModule\Matchers\HashContainer;
use FeatureModule\Matchers\Identity;
use FeatureModule\Matchers\Ip;
use OmnipayModule\Deciders\OmnipayDecider;
use Services\Registry;
use Legacy\Nette\Web\Session;
use Libs\StatCollector;
use Symfony\Component\DependencyInjection\Container;
use Symfony\Component\HttpFoundation\Request;

class FrameworkExt extends FrameworkExt_Abstract implements IExtension
{
    /**
     * @param Container $container
     */
    public function load(Container $container)
    {
        Registry::$emailerFactory = $container->get(DiLocator::EMAILER_FACTORY);
        Registry::$container = $container;
        DoctrineEnum::registerEnumTypes($container->get(DoctrineExt::CONNECTION));

        $host = $container->getParameter('host');
        if (!isCli() && $container->hasParameter('trusted_hosts') && $container->getParameter('trusted_hosts')) {
            $host = $container->get('symfony.request')->getHost();
        }
        if ($container->getParameter('environment') === Environment::PRODUCTION) {
            StatCollector::setup($container->getParameter('stats_collector'), TRUE);
            FApplication::$host = $host;
        } else {
            FApplication::$host = isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : $host;
        }

        // enable global filters everywhere except not in admin
        $filters =  $container->get('doctrine.orm.entity_manager')->getFilters();
        $doctrineEnableFilters = $container->getParameter('doctrine.enable.filters');
        if (!FApplication::isAdmin()) {
                        //enable doctrine filters
            foreach ($doctrineEnableFilters as $filterName) {
                $filters->enable($filterName);
            }
        } else {
            // some filters are enabled in admin as well
            if (in_array('zeroAmountCashBacks', $doctrineEnableFilters)) {
                $filters->enable('zeroAmountCashBacks');
            }
            if (in_array('deletedBusinessServicesLeads', $doctrineEnableFilters)) {
                $filters->enable('deletedBusinessServicesLeads');
            }
        }

        // initialze static memebers for legacy code during startup
        $container->get('application');
    }

    /**
     * @param Request $request
     * @return Context
     */
    public static function createContext(Request $request)
    {
        $context = new Context();
        $context->set(Context::TYPE_IP, new Ip($request->getClientIp()));
        $context->set(Context::TYPE_URL, new Identity($request->getPathInfo()));
        $context->set(Context::TYPE_COOKIE, new HashContainer($request->cookies->all()));
        $context->set(Context::TYPE_QUERY_PARAM, new HashContainer($request->query->all()));
        $context->set('traffic', new HashContainer($request->cookies->all()));
        $context->set('cookie_toggle', new HashContainer($request->cookies->all()));
        if (Customer::isSignedIn()) {
            $customer = Customer::getSignedIn();
            self::destroySessionOnCustomerBlocked($customer);
            $context->set('customer_id', new Identity($customer->getId()));

            $credas = $customer->isPartOfCredas() ?? false;
            $context->set('part_of_credas', new Identity($credas));
        }

        /** @var OmnipayDecider $omnipayDecider */
        $omnipayDecider = StaticContainer::get('omnipay_module.deciders.omnipay_decider');
        $context->set('payment_gateway', new Identity($omnipayDecider->getPaymentGateway()));

        /** @var IncorporationDecider $incorporationDecider */
        $incorporationDecider = StaticContainer::get('company_incorporation_module.deciders.incorporation_decider');
        $context->set('is_company_incorporation', new Identity($incorporationDecider->getLoggedInCompanyIncorporation()));

        if (FUser::isSignedIn()) {
            $user = FUser::getSignedIn();
            self::destroySessionOnCustomerBlocked($user);
            $context->set('user_id', new Identity($user->getId()));
            $context->set('role', new Identity($user->role->key));
            $context->set('role_settings', new Identity([
                    'role' => $user->role->key,
                    'hasIdAccess' => (bool)$user->hasIdAccess,
                    'hasPhonePaymentAccess' => (bool)$user->hasPhonePaymentAccess
                ])
            );
        }
        return $context;
    }

    // check if a user status on the db is set to blocked and if true destroy the session
    private static function destroySessionOnCustomerBlocked($user) {
        if($user->isBlocked()) {
            //use static container to get session
            /** @var Session $session */
            $session = StaticContainer::get('session');
            // destroy php session using $session
            $session->destroy();
        }
    }
}
