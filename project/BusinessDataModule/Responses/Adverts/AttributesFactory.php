<?php

namespace BusinessDataModule\Responses\Adverts;

use Utils\Helpers\ArrayHelper;

class AttributesFactory
{
    public static function fromData(array $data): Attributes
    {
        return new Attributes(
            ArrayHelper::get($data, 'advertiserId'),
            ArrayHelper::get($data, 'advertiserName'),
            ArrayHelper::get($data, 'industrySector'),
            ArrayHelper::get($data, 'terms'),
            ArrayHelper::get($data, 'termsShouldBeSigned'),
            ArrayHelper::get($data, 'fscsConfirmRequired'),
            ArrayHelper::get($data, 'ORDER'),
            Components::fromData(ArrayHelper::get($data, 'components')),
            FollowUp::fromData(ArrayHelper::get($data, 'followUp')),
            self::createRules($data),
            self::createContacts($data)
        );
    }

    private static function createRules(array $data): array
    {
        $rules = [];
        foreach (ArrayHelper::get($data, 'rules') as $type => $rule) {
            $rules[] = Rule::fromData($type, $rule);
        }
        return $rules;
    }

    private static function createContacts(array $data): array
    {
        $contacts = [];
        foreach (ArrayHelper::get($data, 'contact') as $type => $contact) {
            $contacts[] = ContactRule::fromData($type, $contact);
        }
        return $contacts;
    }

}