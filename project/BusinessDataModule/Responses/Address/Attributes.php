<?php

namespace BusinessDataModule\Responses\Address;

use Utils\Helpers\ArrayHelper;

class Attributes
{
    /**
     * @var string
     */
    private $premise;

    /**
     * @var string
     */
    private $street;

    /**
     * @var string
     */
    private $thoroughfare;

    /**
     * @var string
     */
    private $postTown;

    /**
     * @var string
     */
    private $county;

    /**
     * @var string
     */
    private $postcode;

    /**
     * @var string
     */
    private $country;

    public function __construct(
        string $premise,
        string $street,
        string $thoroughfare,
        string $postTown,
        string $county,
        string $postcode,
        string $country
    ) {
        $this->premise = $premise;
        $this->street = $street;
        $this->thoroughfare = $thoroughfare;
        $this->postTown = $postTown;
        $this->county = $county;
        $this->postcode = $postcode;
        $this->country = $country;
    }

    public static function fromData(array $data): self
    {
        return new self(
            ArrayHelper::get($data, 'premise'),
            ArrayHelper::get($data, 'street'),
            ArrayHelper::get($data, 'thoroughfare'),
            ArrayHelper::get($data, 'postTown'),
            ArrayHelper::get($data, 'county'),
            ArrayHelper::get($data, 'postcode'),
            ArrayHelper::get($data, 'country')
        );
    }

    public function getPremise(): string
    {
        return $this->premise;
    }

    public function getStreet(): string
    {
        return $this->street;
    }

    public function getThoroughfare(): string
    {
        return $this->thoroughfare;
    }

    public function getPostTown(): string
    {
        return $this->postTown;
    }

    public function getCounty(): string
    {
        return $this->county;
    }

    public function getPostcode(): string
    {
        return $this->postcode;
    }

    public function getCountry(): string
    {
        return $this->country;
    }
}