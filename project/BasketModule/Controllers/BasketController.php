<?php

namespace BasketModule\Controllers;

use BasketModule\BasketValidators\MultipleValidatorsValidator;
use BasketModule\Forms\Data\VoucherFormData;
use BasketModule\Forms\VoucherForm;
use BasketModule\Services\BasketService;
use BasketModule\Views\PackageBasketView;
use BasketModule\Facades\ExperimentProductBasketFacade;
use CustomerModule\Services\PaymentGatewayService;
use Entities\Customer;
use Libs\Basket as OldBasket;
use Models\Products\Package;
use OmnipayModule\ApiClient\OmnipayApiClient;
use Models\Products\Product;
use RouterModule\Helpers\ControllerHelper;
use Services\NodeService;
use Services\Payment\TokenService;
use Legacy\Nette\Web\Session;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;
use UserModule\Contracts\ICustomer;
use VoucherModule\Services\VoucherService;
use CustomerModule\Entities\Settings\PaymentGatewaySetting;
use PaymentModule\Loggers\PaymentGatewayLogger;

class BasketController
{
    /**
     * @var IRenderer
     */
    private $renderer;

    /**
     * @var Request
     */
    private $request;

    /**
     * @var ControllerHelper
     */
    private $controllerHelper;

    /**
     * @var OldBasket
     */
    private $basket;

    /**
     * @var VoucherService
     */
    private $voucherService;

    /**
     * @var MultipleValidatorsValidator
     */
    private $basketValidator;

    /**
     * @var NodeService
     */
    private $nodeService;

    /**
     * @var PackageBasketView
     */
    private $view;

    /**
     * @var ExperimentProductBasketFacade
     */
    private $experimentProductBasketFacade;

    /**
     * @var PaymentGatewayService
     */
    private $paymentGatewayService;

    /**
     * @var PaymentGatewayLogger
     */
    private $paymentGatewayLogger;

    /**
     * @var TokenService
     */
    private $tokenService;

    /**
     * @var Session
     */
    private $session;

    /**
     * @var OmnipayApiClient
     */
    private $omnipayApiClient;

    /**
     * @var BasketService
     */
    private $basketService;

    public function __construct(
        IRenderer $renderer,
        ControllerHelper $controllerHelper,
        OldBasket $basket,
        VoucherService $voucherService,
        MultipleValidatorsValidator $basketValidator,
        NodeService $nodeService,
        PackageBasketView $view,
        ExperimentProductBasketFacade $experimentProductBasketFacade,
        Request $request,
        PaymentGatewayService $paymentGatewayService,
        PaymentGatewayLogger $paymentGatewayLogger,
        TokenService $tokenService,
        Session $session,
        OmnipayApiClient $omnipayApiClient,
        BasketService $basketService
    )
    {
        $this->renderer = $renderer;
        $this->controllerHelper = $controllerHelper;
        $this->basket = $basket;
        $this->voucherService = $voucherService;
        $this->basketValidator = $basketValidator;
        $this->nodeService = $nodeService;
        $this->view = $view;
        $this->experimentProductBasketFacade = $experimentProductBasketFacade;
        $this->request = $request;
        $this->paymentGatewayService = $paymentGatewayService;
        $this->paymentGatewayLogger = $paymentGatewayLogger;
        $this->tokenService = $tokenService;
        $this->session = $session;
        $this->omnipayApiClient = $omnipayApiClient;
        $this->basketService = $basketService;
    }

    public function basket(ICustomer $customer = NULL): Response
    {
        $package = $this->basket->getPackageOrNull();
        $packageId = $package ? $package->getId() : NULL;

        if ($this->request->query->get('package_id') && ($packageId != $this->request->query->get('package_id'))) {
            return new RedirectResponse($this->controllerHelper->url('basket_module_package_basket_upgrade', ['package_id' => $packageId]));
        }

        if ($packageId === Package::PACKAGE_ANNA && !empty($this->request->query->get('package_id'))) {
            return new RedirectResponse($this->controllerHelper->url('payment'));
        }

        $voucherForm = $this->controllerHelper->buildForm(VoucherForm::class, new VoucherFormData);

        if ($voucherForm->isSubmitted() && $voucherForm->isValid()) {
            $voucher = $this->voucherService->getVoucherByCode($voucherForm->getData()->getVoucherCode());
            $this->basket->setVoucher($voucher->getId());

            return new RedirectResponse($this->getRedirectionUrl());
        }

        $this->experimentProductBasketFacade->updateBasketForRunningExperiments($this->basket);

        $voucherMinimumPrice = 0;
        if ($voucher = $this->basket->getVoucher()) {
            if (!$voucher->isAvailableForUse($this->basket->getPrice('subTotal'))) {
                $voucherMinimumPrice = $voucher->minSpend;
            }
        }

        if ($this->request->query->get('payment_gateway') === PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY) {
            if ($customer) {
                $this->paymentGatewayService->updateCustomerPaymentGatewayFeatureFlag(PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY, $customer);
                $this->session->set('payment_gateway_changed_from_URL', true);
            }
            $this->paymentGatewayService->setPaymentGatewayInSession(PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY);
            return $this->controllerHelper->redirectionTo('basket_module_package_basket_upgrade');
        } else if ($this->request->query->get('payment_gateway') === PaymentGatewaySetting::PAYMENT_GATEWAY_SAGEPAY) {
            if ($customer) {
                $this->paymentGatewayService->updateCustomerPaymentGatewayFeatureFlag(PaymentGatewaySetting::PAYMENT_GATEWAY_SAGEPAY, $customer);
                $this->session->set('payment_gateway_changed_from_URL', true);
            }
            $this->paymentGatewayService->setPaymentGatewayInSession(PaymentGatewaySetting::PAYMENT_GATEWAY_SAGEPAY);
            if ($this->request->query->get('redirect') === 'true') {
                return $this->controllerHelper->redirectionTo('basket_module_package_basket_upgrade', ['redirect' => 'true']);
            }
            return $this->controllerHelper->redirectionTo('basket_module_package_basket_upgrade');
        }

        if ($customer && $this->isSagepayCustomerWithoutTokens($customer) && !$this->session->get('payment_gateway_changed_from_URL') && !$this->isCustomerWithForcedPaymentGateway($customer)) {
            $this->paymentGatewayService->updateCustomerPaymentGatewayFeatureFlag(PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY, $customer);
            $this->paymentGatewayService->setPaymentGatewayInSession(PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY);
            return $this->controllerHelper->redirectionTo('basket_module_package_basket_upgrade');
        }

        if ($this->request->query->get('redirect') === 'true') {
            $this->paymentGatewayLogger->logEvent(PaymentGatewayLogger::EVENT_FALLBACK_URL);
            return $this->controllerHelper->redirectionTo('payment');
        }

        $customerId = $customer ? $customer->getId() : NULL;

        $variables = [
            'validationErrors' => $this->getValidationErrors(),
            'voucherFormTop' => $voucherForm->createView(),
            'voucherFormBottom' => $voucherForm->createView(),
            'guaranteedSameDayProduct' => $this->nodeService->getProductById(Product::PRODUCT_GUARANTEED_SAME_DAY),
            'packageId' => $packageId,
            'basket' => $this->basket,
            'voucherMinimumPrice' => $voucherMinimumPrice,
            'view' => $this->view,
            'customer' => $customer,
            'vendorImageUrl' => $this->omnipayApiClient->getVendorImageUrl($customerId)
        ];
        return $this->renderer->render($variables);
    }

    /**
     * @return string
     */
    public function getRedirectionUrl(): string
    {
        if ($package = $this->basket->getPackageOrNull()) {
            $packageId = $package ? $package->getId() : NULL;
            return $this->controllerHelper->url('basket_module_package_basket_upgrade', ['package_id' => $packageId]);
        }

        return $this->controllerHelper->url('basket_module_package_basket_upgrade');
    }

    /**
     * @return RedirectResponse
     */
    public function removeVoucher()
    {
        $this->basket->removeVoucher();
        return new RedirectResponse($this->getRedirectionUrl());
    }

    /**
     * @return NULL|string
     */
    private function getValidationErrors()
    {
        $errors = $this->basketValidator->validate($this->basket)->getErrors();
        return $errors ? implode('<br>', $errors) : NULL;
    }

    /**
     * @param ICustomer $customer
     * @return boolean
     */
    private function isSagepayCustomerWithoutTokens(ICustomer $customer)
    {
        return !$this->paymentGatewayService->isOmnipay() && !$this->tokenService->getTokensByCustomer($customer);
    }

    /**
     * @param ICustomer $customer
     * @return void
     */
    public function isCustomerWithForcedPaymentGateway(ICustomer $customer): bool
    {
        /** @var PaymentGatewaySetting $paymentSetting */
        if ($paymentSetting = $customer->getSetting(PaymentGatewaySetting::class)) {
            if ($paymentSetting->isForced()) {
                return true;
            }
        }

        return false;
    }

    public function addService(Customer $customer, int $serviceId, int $productId): RedirectResponse
    {
        $this->basketService->clearBasket(false);
        $this->basketService->addRenewableServicesToBasket($this->basket, $customer, [$serviceId => $productId]);
        return new RedirectResponse($this->controllerHelper->url('basket_module_package_basket_upgrade'));
    }
}
