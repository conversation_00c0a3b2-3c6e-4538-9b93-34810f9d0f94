<?php

namespace FrontModule\forms\TaxAssistAccountsConsultation\Accounts;

use Framework\Forms\FForm;

class AccountsForm extends FForm
{
    public function startup()
    {
        $this->buildForm();
        $this->start();
    }

    private function buildForm()
    {
        $this->addText('email', 'Email: *')
            ->addRule(FForm::Required, 'Required!')
            ->addRule(FForm::Email, 'Not valid!')
            ->class('field220');
        $this->addText('fullName', 'Full name: *')
            ->addRule(FForm::Required, 'Required!')
            ->class('field220');
        $this->addText('phone', 'Contact number: *')
            ->addRule(FForm::Required, 'Required!')
            ->class('field220');
        $this->addText('postcode', 'Postcode: *')
            ->addRule(FForm::Required, 'Required!')
            ->class('field220');
        $this->addText('companyName', 'Company name: *')
            ->addRule(FForm::Required, 'Required!')
            ->class('field220');
        $this->addArea('details', 'Details: ')
            ->cols(40)
            ->rows(7);
        $this->addSubmit('send', 'Submit')
            ->addAtrib('data-style', 'slide-left')
            ->addAtrib('data-color', 'orange')
            ->addAtrib('data-size', 's')
            ->class('ladda-button spinner');
    }

}