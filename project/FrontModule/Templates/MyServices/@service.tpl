{* @TODO move to controller *}
{assign 'settings' $settingsService->getSettingsByService($service->getService())}
{assign 'serviceId' $service->getId()}

<tr class="service-row">
    <td>
        {$service->getServiceName()}
        {if $view->hasToShowMoreInfoLink($service)}
            <div class="small"><a href="{$view->getMoreInfoLink($service)}">What do I get?</a></div>
        {/if}
    </td>
    <td title="{$service->getStatus()}" >
        <i class="{$service->getStatusCSSClass()}"></i>
        {$service->getStatus()} {if !$service->isCancelled()}<a href="{url route='service_cancel_choose_reason' service=$service->getId()}" style="cursor: pointer;">cancel</a>{/if}
    </td>
    {if $service->isRenewable()}
        <td class="txtcenter">{$service->dtExpires|datetime}</td>
        <td class="txtcenter renewal-price">
            {$form->getControl($service->id) nofilter}
            {$form->getLabel($service->id) nofilter}

            {if $service->hasOptionalRenewalProduct() && $customer->isOptionalRenewalProductAvailable($service->getOptionalRenewalProduct()->getId())}
                {$form->getControl($service->getId()|cat:'_'|cat:$service->getOptionalRenewalProduct()->getId()) nofilter}
                {$form->getLabel($service->getId()|cat:'_'|cat:$service->getOptionalRenewalProduct()->getId()) nofilter}
            {/if}

            {if $service->hasLatePaymentFee()}<div style="color: orange; font-size: 12px;">£{$view->getLatePaymentFee()} late payment fee applies</div>{/if}
        </td>
    {else}
        <td class="txtcenter"></td>
        <td class="txtcenter"></td>
    {/if}
</tr>
