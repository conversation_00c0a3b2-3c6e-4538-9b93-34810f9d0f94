{if $company->hasServices()}
    <div class="table-responsive table-borderless" style="max-width: calc(100vw - 30px)">
        <table id="c_{$company->getId()}" class="table company-block" data-company-id="{$company->getId()}">
            <thead>
            <tr class="company-header-expanded">
                <th colspan="3" data-renewal-toggable="toggleCompany" class="company-name4">
                    <a name="company={$company->getId()}"></a>
                    {$company->getCompanyName()}
                </th>
                <th class="txtcenter width70">
                    <a href="#" class="toggle-company" data-renewal-action="toggleCompanyServices">
                        <i class="fa fa-angle-up"></i>
                        Hide
                    </a>
                </th>
            </tr>
            <tr class="company-header-collapsed hidden">
                <th colspan="3" data-renewal-toggable="toggleCompany" class="company-name2">
                    {$company->getCompanyName()}
                </th>
                <th colspan="2" data-renewal-toggable="toggleCompany" style="width: 50%;" class="action-required">
                    {if $company->hasAllExpiredServices()}
                        <i class="fa fa-times-circle largefont grey2"></i>
                        <span class="midfont">Services expired</span>
                    {elseif $company->isServiceActionRequired()}
                        <i class="fa fa-exclamation-circle largefont red"></i>
                        <span class="midfont">Action required</span>
                    {elseif $company->hasAllActiveServices()}
                        <i class="fa fa-check-circle largefont green1"></i>
                        <span class="midfont">No action needed</span>
                    {/if}
                </th>
                <th class="txtcenter width70" data-renewal-toggable="toggleCompany">
                    <a href="#" class="toggle-company expand-{$company->getId()}" data-renewal-action="toggleCompanyServices">
                        <i class="fa fa-angle-down"></i>
                        Expand
                    </a>
                </th>
            </tr>
            </thead>
            <tbody>
            <tr class="heading" data-renewal-toggable="toggleCompany">
                <td class="width300">SERVICE</td>
                <td class="width130">STATUS</td>
                <td class="txtcenter width70">RENEWS ON</td>
                <td class="txtcenter">RENEW?</td>
            </tr>
            {$services = $company->getServices()}
            {foreach $services as $service}
                {include 'MyServices/@service.tpl' view = $view}
            {/foreach}
            {$orderItems = $company->getOrderItems()}
            {foreach $orderItems as $item}
                {include 'MyServices/@orderItem.tpl'}
            {/foreach}
            </tbody>
        </table>
    </div>
{/if}
