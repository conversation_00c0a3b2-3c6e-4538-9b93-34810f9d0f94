<?php

namespace DevelopmentModule\Handlers;

use CompanyModule\Repositories\CompanyRepository;
use DateTime;
use Doctrine\ORM\EntityManagerInterface;
use Entities\Company;
use Utils\Date;

class FunctionalTestHandler
{
    public function __construct(
        private CompanyRepository $companyRepository,
        private EntityManagerInterface $em
    ) {}

    public function run(): array
    {
        /** @var Company[] $companies */
        $companies = $this->companyRepository->getActiveCompanies();

        $total = $dissolved = $hidden = 0;
        foreach ($companies as $company) {

            $dtc = $company->getIncorporationDate();
            $total++;

            if ($dtc >= new Date('2024-01-01')) {
                $company->setCompanyStatus(Company::COMPANY_STATUS_DISSOLVED);
                $company->setDissolutionDate(new Date());
                $this->em->persist($company);
                $this->em->flush();
                $dissolved++;

            } elseif ($dtc >= new Date('2023-01-01')) {
                $company->setHidden(true);
                $this->em->persist($company);
                $this->em->flush();
                $hidden++;
            }
        }

        return [
            'total' => $total,
            'dissolved' => $dissolved,
            'hidden' => $hidden
        ];
    }
}