company_incorporation_module.memorandum-and-articles.get_articles:
  path: /api/incorporation/{company}/memorandum-and-articles/get_articles/
  methods: [GET]
  defaults:
    controller: company_incorporation_module.controllers.api.memorandum_and_articles_step_api_controller
    action: getArticles
    requirements:
      company:
      converter: nonIncorporatedCompany
  requirements:
    company: '\d+'
  options:
    excludeFromSitemap: true