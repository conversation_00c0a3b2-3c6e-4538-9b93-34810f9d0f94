<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd"
>
    <services>
        <defaults public="true" />

        <service class="CompanyIncorporationModule\UseCase\MemorandumAndArticles\Api\RemoveArticle\Command"
                 id="company_incorporation_module.use_case.memorandum_and_articles.api.remove_article.command">
            <argument id="company_incorporation_module.use_case.memorandum_and_articles.api.remove_article.factory" type="service"/>
            <argument id="company_incorporation_module.services.memorandum_and_articles.service" type="service"/>
            <argument id="company_incorporation_module.factories.old_company_factory" type="service"/>
        </service>

        <service class="CompanyIncorporationModule\UseCase\MemorandumAndArticles\Api\RemoveArticle\Factory"
                 id="company_incorporation_module.use_case.memorandum_and_articles.api.remove_article.factory">
        </service>
    </services>
</container>