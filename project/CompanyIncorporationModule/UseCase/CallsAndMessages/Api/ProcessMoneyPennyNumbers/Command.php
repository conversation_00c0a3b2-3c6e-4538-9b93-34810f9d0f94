<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\UseCase\CallsAndMessages\Api\ProcessMoneyPennyNumbers;

use CompanyIncorporationModule\Exceptions\CallAndMessages\CompanyHasReservedNumberException;
use CompanyIncorporationModule\Exceptions\CallAndMessages\CompanyReserveNumberException;
use CompanyIncorporationModule\Factories\MoneyPennyCleverLeadDetailsFactory;
use Entities\Company;
use MoneyPennyNumbersModule\Deciders\MoneyPennyDecider;
use MoneyPennyNumbersModule\Dtos\Request\NumberDto;
use MoneyPennyNumbersModule\Entities\MoneyPennyCleverLeadDetails;
use MoneyPennyNumbersModule\Facades\MoneyPennyFacade;
use MoneyPennyNumbersModule\Loggers\MoneyPennyLogger;
use MoneyPennyNumbersModule\Repositories\MoneyPennyCleverLeadDetailsRepository;
use MoneyPennyNumbersModule\Responses\DummyResponse;
use MoneyPennyNumbersModule\Responses\IResponse;

class Command
{
    public function __construct(
        private Factory $factory,
        private MoneyPennyFacade $moneyPennyFacade,
        private MoneyPennyCleverLeadDetailsRepository $moneyPennyCleverLeadDetailsRepository,
        private MoneyPennyDecider $moneyPennyDecider,
        private MoneyPennyLogger $moneyPennyLogger,
        private MoneyPennyCleverLeadDetailsFactory $leadFactory,
    ) {
    }

    public function execute(Request $request): Response
    {
        if ($this->moneyPennyDecider->companyHasReserveNumber($request->company)) {
            $this->handleError(MoneyPennyLogger::COMPANY_HAS_RESERVED_NUMBER, $request, new CompanyHasReservedNumberException());
        }

        $numberDto = new NumberDto($request->cleverNumber, $request->reserved, $request->targetNumber);
        $response = $this->moneyPennyFacade->reserveNumber($request->company, $numberDto);

        $lead = $this->leadFactory->make(
            $request->company, $numberDto->getTargetNumber(),
            $numberDto->getCleverNumber(),
            $request->cmsPaymentMethod
        );

        if (!$response instanceof DummyResponse) {
            $this->moneyPennyCleverLeadDetailsRepository->saveEntity($lead);
        }

        if (!$response->isSuccess()) {
            $this->handleError(MoneyPennyLogger::NUMBER_RESERVE_ERROR, $request, new CompanyReserveNumberException($response->getBody()));
        }

        $this->logSuccess($request->company, $response, $lead);

        return $this->factory->makeResponse();
    }

    private function logSuccess(Company $company, IResponse $response, MoneyPennyCleverLeadDetails $lead): void
    {
        $this->moneyPennyLogger->logEvent(
            MoneyPennyLogger::NUMBER_RESERVE_SUCCESS,
            $company,
            [
                'code' => $response->getCode(),
                'message' => $response->getBody(),
            ],
            [
                'leadId' => $lead->getId(),
                'cmsPaymentMethod' => $lead->getCmsPaymentMethod(),
            ]
        );
    }

    private function handleError(
        string $eventName,
        Request $request,
        \Throwable $e,
    ): void {
        $this->moneyPennyLogger->logEvent(
            $eventName,
            $request->company,
            [
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
            ],
            [
                $request->cleverNumber,
                $request->reserved,
                $request->targetNumber,
                $request->cmsPaymentMethod,
            ]
        );

        throw $e;
    }
}
