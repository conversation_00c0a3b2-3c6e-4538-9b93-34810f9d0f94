<?php

namespace Libs\CHFiling\Core\UtilityClass;

use Libs\CHFiling\Core\UtilityClass\Authentication;

abstract class Member
{
    /**
     * @var int
     */
    private $memberId;

    /**
     * @var Address
     */
    private $address;

    /**
     * @var Authentication
     */
    private $authentication;

    /**
     * @var bool
     */
    private $consentToAct = FALSE;

    /**
     * @var int
     */
    private $designated_ind;

    /**
     * @var bool
     */
    private $isManager = FALSE;

    /**
     * @var int
     */
    private $corporate;

    /**
     * @var string
     */

    private $type;

    protected function __construct()
    {
        $this->address = new Address();
        $this->authentication = new Authentication();
    }

    /**
     * @param array $data
     */
    public function setFields(array $data)
    {
        if (isset($data['company_member_id'])) {
            $this->setMemberId($data['company_member_id']);
        }
        
        $this->address->setFields($data);
        if (isset($data['authentication']) && !empty($data['authentication'])) {
            $this->authentication->setDBFields($data['authentication']);
        }
        if (isset($data['designated_ind'])) {
            $this->setDesignatedInd($data['designated_ind']);
        }
        if (isset($data['consentToAct'])) {
            $this->setConsentToAct($data['consentToAct']);
        }
    }

    /**
     * @return array
     */
    public function getFields()
    {
        return array_merge(
            ['memberId' => $this->getMemberId(), 'consentToAct' => $this->consentToAct, 'isManager' => $this->isManager],
            $this->address->getFields(),
            $this->authentication->getFields()
        );
    }

    /**
     * Pre-condition: $address needs to be completed
     * Post-condition: exception is thrown if $address is not complete
     *
     * @param Address $address
     */
    public function setAddress(Address $address)
    {
        $this->address = clone $address;
    }

    /**
     * @return Address
     */
    public function getAddress()
    {
        return clone $this->address;
    }

    /**
     * @param Authentication $authentication
     */
    public function setAuthentication(Authentication $authentication)
    {
        $this->authentication = clone $authentication;
    }

    /**
     * @return Authentication
     */
    public function getAuthentication()
    {
        return clone $this->authentication;
    }

    /**
     * @return int
     */
    public function getMemberId(): ?int
    {
        return $this->memberId;
    }

    /**
     * @param int $memberId
     */
    public function setMemberId(int $memberId)
    {
        $this->memberId = $memberId;
    }

    /**
     * @param int $id
     */
    public function setDesignatedInd($id)
    {
        $this->designated_ind = $id;
    }

    /**
     * @return int
     */
    public function getDesignatedInd()
    {
        return $this->designated_ind;
    }

    /**
     * @param string $type
     */
    public function setType($type)
    {
        $this->type = $type;
    }

    /**
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param int $corporate
     */
    public function setCorporateType($corporate)
    {
        $this->corporate = $corporate;
    }

    /**
     * @return bool
     */
    public function isPerson()
    {
        return $this->corporate == 0;
    }

    /**
     * @return bool
     */
    public function isCorporate()
    {
        return $this->corporate == 1;
    }

    /**
     * @return bool
     */
    public function isLegalPerson()
    {
        return $this->corporate == 2;
    }

    /**
     * @return bool
     */
    public function hasConsentToAct()
    {
        return $this->consentToAct;
    }

    /**
     * @param bool $consentToAct
     */
    public function setConsentToAct($consentToAct)
    {
        $this->consentToAct = (bool) $consentToAct;
    }

    /** @TODO: Remove when project/CompanyFormationModule/Controllers/ShareholderUpdateController.php is refactored to use new entities */
    public function isManager(): bool
    {
        return $this->isManager;
    }

    public function setIsManager(bool $isManager): void
    {
        $this->isManager = $isManager;
    }
}
