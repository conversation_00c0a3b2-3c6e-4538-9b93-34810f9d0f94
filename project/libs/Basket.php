<?php

namespace Libs;

use BasketModule\Contracts\IBasket;
use Exception;
use Models\Products\BasketProduct;
use BootstrapModule\Singletons\StaticContainer;
use CompanyModule\Domain\Company\CompanyName;
use Exceptions\Business\BasketException;
use Framework\FApplication;
use FrontModule\Controlers\SearchControler;
use Models\Products\IPackage;
use JsonSerializable;
use Nette\Utils\Strings;
use Entities\Customer as CustomerEntity;
use Models\Products\Package;
use Models\Products\Product;
use PackageModule\Dto\ProductRequirements;
use PackageModule\Factories\ProductRequirementsFactory;
use UserModule\Contracts\ICustomer;
use Utils\NetteSmartObject;
use Models\OldModels\VoucherNew;
use Models\Products\WholesalePackage;

class Basket extends NetteSmartObject implements IBasket, JsonSerializable
{
    const VAT = 0.20;

    const PRODUCT_ITEM = 'Product';
    const PACKAGE_ITEM = 'Package';

    /**
     * @var string
     */
    private static $namespace = 'cms_basket';

    /**
     * @var BasketProduct[]
     */
    protected $items = [];

    /**
     * @var VoucherNew
     */
    protected $voucher = FALSE;

    /**
     * @var int
     */
    protected $orderId;

    /**
     * @var float
     */
    protected $credit;

    /**
     * @var CompanyName
     */
    protected $companyName;

    /**
     * @var boolean
     */
    protected $experimentProductHasBeenAdded;

    /**
     * @var float
     */
    protected $totalPriceFromSerializedBasket;

    /**
     * @param string $namespace
     */
    public function __construct($namespace = NULL)
    {
        if ($namespace) {
            self::$namespace = $namespace;
        }
        $session = StaticContainer::get('session')->getNameSpace(self::$namespace);

        if (!isset($session->items)) {
            $session->items = [];
        }
        if (!isset($session->voucher)) {
            $session->voucher = FALSE;
        }
        if (!isset($session->orderId)) {
            $session->orderId = FALSE;
        }
        if (!isset($session->credit)) {
            $session->credit = 0;
        }
        if (!isset($session->experimentProductHasBeenAdded)) {
            $session->experimentProductHasBeenAdded = false;
        }

        $this->items =& $session->items;
        $this->voucher =& $session->voucher;
        $this->orderId =& $session->orderId;
        $this->credit =& $session->credit;
        $this->companyName =& $session->companyName;
        $this->experimentProductHasBeenAdded =& $session->experimentProductHasBeenAdded;
    }

    public function save(): void
    {
        $session = StaticContainer::get('session')->getNameSpace(self::$namespace);
        $session->items = $this->items;
        $session->voucher = $this->voucher;
        $session->orderId = $this->orderId;
        $session->credit = $this->credit;
        $session->companyName = $this->companyName;
        $session->experimentProductHasBeenAdded = $this->experimentProductHasBeenAdded;
    }

    /**
     * @return BasketProduct[]
     */
    public function getItems(): array
    {
        $items = [];

        foreach ($this->items as $key => $item) {
            $items[$key] = $item;

            if ($child = $item->getChild()) {
                $items[crc32(sprintf('childOf%s', $key))] = $child;
            }
        }

        return $items;
    }

    /**
     * @return BasketProduct[]
     */
    public function getGroupedItems(): array
    {
        return $this->items;
    }

    /**
     * @param $itemId
     * @return BasketProduct|NULL
     */
    public function getItem($itemId)
    {
        $items = $this->getItems();
        return $items[$itemId] ?? NULL;
    }

    /**
     * @return BasketProduct|FALSE
     */
    public function getFirstItem()
    {
        $items = $this->getItems();
        return reset($items);
    }

    /**
     * @return VoucherNew|bool
     */
    public function getVoucher()
    {
        return $this->voucher;
    }

    /**
     * @param $orderId
     */
    public function setOrderId($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * @return string
     */
    public function getOrderId()
    {
        return $this->orderId;
    }


    /**
     * @throws BasketException
     */
    public function add(BasketProduct $product): bool
    {
        if (!empty($this->items) && ($product->getId() === Package::PACKAGE_ANNA)) {
            throw BasketException::itemCouldNotBeAdded($product);
        }

        $this->verifyBasketItems($product);
        $this->verifyProduct($product);
        $itemKeyInBasket = $product->inBasket($this);

        if ($itemKeyInBasket && $product->canBeUpdated($this)) {
            $this->items[$itemKeyInBasket]->qty += $product->qty;
            return true;
        }

        $this->items[] = $this->cleanProductUselessFieldsToBasket($product);
        return true;
    }

    /**
     * @param BasketProduct[] $products
     */
    public function addProducts($products)
    {
        foreach ($products as $product) {
            $this->add($product);
        }
    }

    /**
     * @param IPackage $package
     */
    public function addPackage(IPackage $package)
    {
        $this->add($package);
        $this->addProducts($package->getPackageProducts('associatedProducts1'));
        // @TODO call setIncorporationCompanyName when searching
        if (!$this->companyName && SearchControler::getCompanyName($package)) {
            $this->companyName = CompanyName::uppercased(SearchControler::getCompanyName($package));
        }
        if ($this->companyName) {
            $companyTypeSuffix = $package ? $package->getCompanyTypeSuffix() : NULL;
            $package->companyName = CompanyName::withSuffix((string)$this->companyName, $companyTypeSuffix);
        }
    }

    /**
     * @param $key
     * @return bool
     */
    public function hasItemOnKey($key): bool
    {
        return isset($this->items[$key]);
    }

    /**
     * @param $item
     * @param $key
     * @return bool
     */
    public function replace($item, $key)
    {
        if (isset($this->items[$key])) {
            $this->items[$key] = $item;
            return TRUE;
        }
        return FALSE;
    }


    /**
     * @param int $key
     * @param int $qty
     */
    public function update($key, $qty)
    {
        if (isset($this->items[$key]) && $this->items[$key]->canBeUpdated($this)) {
            $this->items[$key]->qty = max(1, (int)$qty);
        }
    }

    public function remove(int $key): ?int
    {
        if (isset($this->items[$key])) {
            $removedProduct = $this->items[$key];
            $removedId = $removedProduct->getId();
            unset($this->items[$key]);

            if ($removedProduct->isPackage()) {
                $includedProducts = $removedProduct->getPackageProducts('associatedProducts1');
                foreach (array_keys($includedProducts) as $id) {
                    $this->removeByProductId($id);
                }
                if ($this->hasProduct(Product::PRODUCT_GUARANTEED_SAME_DAY)) {
                    $this->removeByProductId(Product::PRODUCT_GUARANTEED_SAME_DAY);
                }

                foreach ($this->items as $product) {
                    if ($product->isRequiredCorePackage()) {
                        $this->removeByProductId($product->getId());
                    }
                }
            }

            return $removedId;
        }

        return null;
    }

    /**
     * @param int $voucherId
     */
    public function setVoucher($voucherId)
    {
        $this->voucher = new VoucherNew($voucherId);
    }

    /**
     * Provides removing voucher from basket
     * @return void
     */
    public function removeVoucher()
    {
        $this->voucher = NULL;
    }

    /**
     * Using for basket form - inputs
     * @return array $qty
     */
    public function getFormQty()
    {
        $qty = [];
        foreach ($this->getItems() as $key => $val) {
            if (!$val->onlyOne) {
                $qty[$key] = $val->qty;
            }
        }
        return $qty;
    }


    /**
     * @return int $count
     */
    public function getItemsCount()
    {
        $count = 0;
        foreach ($this->getItems() as $key => $val) {
            $count += $val->qty;
        }
        return $count;
    }


    /**
     * @return boolean
     */
    public function isEmpty()
    {
        return empty($this->getItems()) ? TRUE : FALSE;
    }


    /**
     * @return boolean
     */
    public function hasOrderId()
    {
        return $this->orderId == NULL ? FALSE : TRUE;
    }


    /**
     * @param bool $hardClean
     */
    public function clear($hardClean = TRUE)
    {
        $session = FApplication::$session->getNameSpace(self::$namespace);

        if ($hardClean === TRUE) {
            $session->remove();
        } else {
            $session->items = [];
            $session->voucher = FALSE;
            $session->orderId = FALSE;
        }
    }

    /**
     * @param string $part
     * @param boolean $round
     * @param CustomerEntity|ICustomer|null $customer
     * @return mixed
     */
    public function getPrice($part = NULL, $round = TRUE, $customer = NULL)
    {
        $price = ['subTotal' => 0, 'nonVatable' => 0, 'subTotal2' => 0, 'discount' => 0, 'vat' => 0, 'totalVat' => 0, 'total' => 0, 'account' => 0, 'totalWithoutCredit' => 0];

        // if there are any items
        if (!empty($this->getItems())) {

            // count sub total price
            foreach ($this->getItems() as $key => $item) {
                $price['subTotal'] += $item->getTotalPrice($customer); /** @phpstan-ignore-line */
                $price['vat'] += $item->getVat($customer); /** @phpstan-ignore-line */
                // no VAT
                if ($item->notApplyVat == TRUE) {
                    $price['nonVatable'] += $item->getTotalPrice($customer); /** @phpstan-ignore-line */
                    // non vatable value
                } elseif ($item->nonVatableValue) {
                    $price['nonVatable'] += $item->nonVatableValue;
                }
            }


            // voucher
            if ($this->voucher != NULL) {
                $price['discount'] = $this->voucher->discount($price['subTotal']);
                $price['subTotal2'] = $price['subTotal'] - $price['discount'];
            } else {
                $price['subTotal2'] = $price['subTotal'];
            }
            //add vat and count total price
            //we are left with greater non vatable then the price itself
            if ($price['nonVatable'] > $price['subTotal2']) {
                $price['vat'] = 0;
            } else {
                $price['vat'] = ($price['subTotal2'] - $price['nonVatable']) * self::VAT;
            }

            $price['total'] = $price['subTotal2'] + $price['vat'];
            $price['totalWithoutCredit'] = $price['total'];

            if ($this->isUsingCredit()) {
                if ($price['total'] >= $this->getCredit()) {
                    $price['account'] = $this->getCredit();
                    $price['total'] -= $this->getCredit();
                }
            }

            // round
            if ($round === TRUE) {
                foreach ($price as &$val) {
                    $val = self::round($val);
                }
                unset($val);
            }
        }

        // return part, or whole price
        if ($part && isset($price[$part])) {
            return $price[$part];
        } else {
            return (object)$price;
        }
    }


    /**
     * @param string $price
     * @return double
     */
    public static function round($price)
    {
        $round = round($price, 2);
        return number_format($round, 2, '.', '');
    }

    /**
     * @return bool
     */
    public function hasItemsRequiringCompanyNumber()
    {
        foreach ($this->getItems() as $item) {
            if ($item->requiredCompanyNumber) {
                return TRUE;
            }
        }

        return FALSE;
    }

    /**
     * Provide update company numbers for basket items
     *
     * @param array $data
     * @param array $companies
     */
    public function updateCompanyNumbers($data, $companies)
    {
        $items = $this->getItems();

        foreach ($data as $key => $val) {
            /** @var BasketProduct $item */
            $item = $items[$key];

            // from select
            if (($val['radio'] == 'existing') && ($val['select'] != '?')) {
                if (isset($companies['Incorporated'][$val['select']])) {
                    $item->companyName = $companies['Incorporated'][$val['select']];
                } elseif (isset($companies['Not Incorporated'][$val['select']])) {
                    $item->companyName = $companies['Not Incorporated'][$val['select']];
                } else {
                    $item->companyName = 'N/A';
                }

                if (isset($val['select']) && Strings::startsWith($val['select'], 'imported_')) {
                    $item->linkedCompanyStatus = BasketProduct::LINK_NEW_SELECT;
                    list(, $item->companyNumber) = explode('_', $val['select']);
                    $item->setCompanyId(NULL);
                } else {
                    $item->linkedCompanyStatus = BasketProduct::LINK_EXISTING_SELECT;
                    $item->setCompanyId($val['select']);
                }
            } elseif (($val['radio'] == 'import') && (!empty($val['text']))) {
                // linkedCompanyStatus is set by Import method in controller
                if ($item->isLinkedToNewImportedCompany()) {
                    $item->setCompanyId(NULL);
                }
                $item->companyNumber = Strings::padLeft($val['text'], 8, '0');
            } elseif (($val['radio'] == 'existing') && ($val['select'] == '?')) {
                $item->linkedCompanyStatus = NULL;
                $item->setCompanyId(NULL);
                $item->companyNumber = NULL;
                $item->companyName = '';
            }
        }
    }


    /**
     * Returns if package is included
     * @return boolean
     */
    public function hasPackageIncluded()
    {
        foreach ($this->getItems() as $key => $item) {
            if ($item instanceof Package) {
                return TRUE;
            }
        }
        return FALSE;
    }


    /**
     * Check if Barclays banking is included in basket
     *
     * @return boolean
     */
    public function hasBarlaysBankingIncluded()
    {
        foreach ($this->getItems() as $key => $item) {
            if ($item->getClass() == 'BarclaysBanking') {
                return TRUE;
            }
        }
        return FALSE;
    }

    /**
     * @return Package
     * @throws Exception
     */
    public function getPackage()
    {
        $package = $this->getPackageOrNull();
        if (!$package) {
            throw new Exception("Basket doesn't include Package");
        }

        return $package;
    }

    /**
     * @return Package|null
     */
    public function getPackageOrNull()
    {
        foreach ($this->getItems() as $key => $item) {
            if ($item instanceof Package) {
                return $item;
            }
        }

        return NULL;
    }

    /**
     * @param int $packageId
     * @return NULL|Package
     */
    public function getPackageById($packageId)
    {
        foreach ($this->getItems() as $item) {
            if ($item instanceof Package && $item->getId() == $packageId) {
                return $item;
            }
        }
    }

    /**
     * @return NULL|int
     */
    public function getPackageItemId()
    {
        foreach ($this->getItems() as $item) {
            if ($item instanceof Package && isset(Package::$types[$item->getId()])) {
                return $item->getId();
            }
        }
        return NULL;
    }

    /**
     * @param int $packageId
     * @return bool
     */
    public function hasPackage($packageId)
    {
        return (bool)$this->getPackageById($packageId);
    }

    /**
     * @param string $productId
     * @return bool
     */
    public function hasProduct(string $productId): bool
    {
        foreach ($this->getItems() as $item) {
            if ($item->getId() == $productId) {
                return TRUE;
            }

            if ($item->getNodeName() && $item->getNodeName() == $productId) {
                return TRUE;
            }
        }
        return FALSE;
    }

    /**
     * is our basket free
     * @return bool
     */
    public function isFreeBasket()
    {
        //do we have items in the basket
        if ($this->getItemsCount() > 0) {
            //do we have products that are not free
            $nonFreeItems = 0;
            foreach ($this->getItems() as $item) {
                if ($item->getPrice() > 0) {
                    $nonFreeItems++;
                }
            }
            $subtotal = $this->getPrice('subTotal');
            //do we have voucher applied
            if ($this->voucher && $this->voucher->isAvailableForUse($subtotal) && $nonFreeItems > 0) {
                if ($this->getPrice('total') === '0.00') {
                    return TRUE;
                }
            }

            if (FApplication::isAdmin() && $this->getPrice('total') === '0.00') return TRUE;
        }
        return FALSE;
    }

    /**
     * @return Package
     */
    public function hasPackageInBasket()
    {
        $package = NULL;
        foreach ($this->getItems() as $item) {
            if ($item instanceof Package && isset(Package::$types[$item->getId()])) {
                $package = $item;
            }
        }
        return $package;
    }

    /**
     * @return bool
     */
    public function hasWholesalePackage()
    {
        foreach ($this->getItems() as $item) {
            if ($item instanceof WholesalePackage) {
                return TRUE;
            }
        }
        return FALSE;
    }

    /**
     * @return bool
     */
    public function isUsingCredit()
    {
        return $this->credit > 0;
    }

    /**
     * @param float $amount
     */
    public function setCredit($amount)
    {
        $this->credit = $amount;
    }

    /**
     * @return float
     */
    public function getCredit()
    {
        return $this->credit;
    }

    /**
     * @return bool
     */
    public function isInsufficientCredit()
    {
        return $this->isUsingCredit()
            && ($this->getPrice('totalWithoutCredit') > $this->getCredit());
    }

    /**
     * @return bool
     */
    public function hasItemWithIdCheckRequired()
    {
        foreach ($this->getItems() as $item) {
            if ($item->isIdCheckRequired) {
                return TRUE;
            }
        }
        return FALSE;
    }

    public function removeByProductId(int $productId)
    {

        foreach ($this->getItems() as $itemId => $item) {
            if ($item->getId() == $productId) {
                $this->remove($itemId);
            }
        }

    }

    /**
     * @param int $productId
     * @return int
     */
    public function getBasketIndexByProductId($productId)
    {
        foreach ($this->getItems() as $index => $item) {
            if ($item->getId() == $productId) {
                return $index;
            }
        }
    }

    /**
     * @param array $productIds
     * @return BasketProduct[]
     */
    public function getProductsInCommonByIds(array $productIds)
    {
        $containedBlacklistProducts = [];
        foreach ($this->getItems() as $itemId => $product) {
            if (in_array($product->getId(), $productIds)) {
                $containedBlacklistProducts[$itemId] = $product;
            }
        }

        return $containedBlacklistProducts;
    }

    /**
     * @param CompanyName $companyName
     */
    public function setIncorporationCompanyName(CompanyName $companyName)
    {
        $this->companyName = $companyName;
        $package = $this->getPackageOrNull();
        if ($package) {
            $package->companyName = (string)$companyName;
        }
    }

    public function removeIncorporationCompanyName()
    {
        $this->companyName = null;
    }

    /**
     * @return CompanyName
     */
    public function getIncorporationCompanyName()
    {
        return $this->companyName;
    }

    /**
     * @return bool
     */
    public function hasIncorporationCompanyName()
    {
        return (bool)$this->companyName;
    }

    /**
     * @return bool
     */
    public function isFreePurchase()
    {
        return $this->isFreeBasket();
    }

    /**
     * @return float
     * @deprecated getTotalPrice() does not take a customer as argument, use getPrice('total', true, $customer) instead
     */
    public function getTotalPrice()
    {
        return $this->getPrice('total');
    }

    /**
     * @return float
     */
    public function getTotalPriceWithoutCredit()
    {
        return $this->getPrice('totalWithoutCredit');
    }

    /**
     * @return bool
     */
    public function isFirstItemPackage()
    {
        return $this->getFirstItem() instanceof Package;
    }

    /**
     * @return string
     */
    public function getName()
    {
        return self::$namespace === 'cms_basket' ? NULL : self::$namespace;
    }

    /**
     * @return void
     */
    public function setExperimentProductHasBeenAdded($experimentProductHasBeenAdded): void
    {
        $this->experimentProductHasBeenAdded = $experimentProductHasBeenAdded;
    }

    /**
     * @return boolean
     */
    public function getExperimentProductHasBeenAdded()
    {
        return $this->experimentProductHasBeenAdded;
    }

    public function hasVoucher(): bool
    {
        return !empty($this->voucher);
    }

    /**
     * @return array
     */
    public function jsonSerialize(): mixed
    {
        $items = $basket = [];
        foreach ($this->getItems() as $item) {
            $items[] = [
                'id' => $item->getId(),
                'title' => $item->getLngTitle(),
                'qty' => (int)$item->qty,
                'price' => (float)$item->getPrice(),
                'companyId' => $item->getCompanyId(),
                'companyName' => $item->companyName,
                'companyNumber' => $item->getCompanyNumber()
            ];
        }
        return [
            'items' => $items,
            'subTotalPrice' => (float)$this->getPrice('subTotal2'),
            'vat' => (float)$this->getPrice('vat'),
            'creditPrice' => $this->isUsingCredit() ? (float)$this->getPrice('account') : NULL,
            'totalPrice' => (float)$this->getPrice('total'),
            'totalPriceWithoutCredit' => (float)$this->getPrice('totalWithoutCredit'),
            'useCredit' => $this->isUsingCredit(),
            'companyName' => $this->companyName
        ];
    }

    /**
     * @param NULL|CustomerEntity $customer
     * @return string
     */
    public function getSerializedBasket(?CustomerEntity $customer): string
    {
        $items = [];
        foreach ($this->getItems() as $item) {
            $items[] = [
                'id' => $item->getId(),
                'title' => $item->getLngTitle(),
                'qty' => (int)$item->qty,
                'price' => (float)$item->getPrice($customer),
                'companyId' => $item->getCompanyId(),
                'companyName' => $item->companyName,
                'companyNumber' => $item->getCompanyNumber(),
                'isAssociated' => $item->isAssociated,
                'isOffer' => $item->isOffer(),
                'wholesalePrice' => $item->wholesalePrice,
                'linkedCompanyStatus' => $item->linkedCompanyStatus,
                'additionalInformation' => $item->getAdditionalInformation()
            ];
        }

        $round = TRUE;

        return json_encode([
            'items' => $items,
            'subTotalPrice' => (float)$this->getPrice('subTotal2', $round, $customer),
            'vat' => (float)$this->getPrice('vat', $round, $customer),
            'voucherId' => $this->hasVoucher() ? $this->voucher->getId() : NULL,
            'creditPrice' => $this->isUsingCredit() ? (float)$this->getPrice('account', $round, $customer) : NULL,
            'totalPrice' => (float)$this->getPrice('total', $round, $customer),
            'totalPriceWithoutCredit' => (float)$this->getPrice('totalWithoutCredit', $round, $customer),
            'useCredit' => $this->isUsingCredit(),
            'companyName' => $this->companyName
        ]);
    }

    /**
     * @param string $serialiazedString
     * @return void
     */
    public function initializeFromSerializedString(string $serialiazedString): void
    {
        $this->clear(true);
        $object = json_decode($serialiazedString);

        $this->companyName = isset($object->companyName) ? $object->companyName : NULL;
        $this->credit = isset($object->credit) ? $object->credit : 0;
        $this->experimentProductHasBeenAdded = isset($object->experimentProductHasBeenAdded) ? $object->experimentProductHasBeenAdded : FALSE;
        $this->orderId = isset($object->orderId) ? $object->orderId : FALSE;
        if(isset($object->voucherId)) {
            $this->setVoucher($object->voucherId);
        } else {
            $this->voucher = NULL;
        }
        if(isset($object->items)) {
            $this->setProductsFromItemsOfSerializedString($object->items);
        } else {
            $this->items = [];
        }
        $this->setTotalPriceFromSerializedBasket(isset($object->totalPrice) ? $object->totalPrice : 0);
    }

    /**
     * @param float $amount
     * @return void
     */
    private function setTotalPriceFromSerializedBasket(float $amount): void
    {
        $this->totalPriceFromSerializedBasket = $amount;
    }

    /**
     * @return float
     */
    public function getTotalPriceFromSerializedBasket(): float
    {
        return $this->totalPriceFromSerializedBasket;
    }

    /**
     * @param array $items
     * @return void
     */
    private function setProductsFromItemsOfSerializedString(array $items): void
    {
        foreach ($items as $item) {
            $product = Product::getProductById($item->id);
            $product->setQty($item->qty);
            $product->setPrice($item->price);
            if ($item->companyId) {
                $product->setCompanyId($item->companyId);

                /** @var \CompanyModule\Repositories\CompanyRepository $companyRepository */
                $companyRepository = StaticContainer::get('company_module.repositories.company_repository');

                if ($companyEntity = $companyRepository->optionalById($item->companyId)) {
                    $product->setCompanyEntity($companyEntity);
                }

            }

            if ($item->isAssociated) {
                $product->isAssociated = TRUE;
            }

            if ($item->isOffer) {
                $product->markAsOffer();
            }

            if ($item->companyName) {
                $product->companyName = $item->companyName;
            }
            elseif ($product->isPackage() && $this->companyName) {
                $product->companyName = CompanyName::withSuffix($this->companyName, $product->getCompanyTypeSuffix());
            }

            if ($item->companyNumber) {
                $product->companyNumber = $item->companyNumber;
            }

            if ($item->isAssociated) {
                $product->isAssociated = TRUE;
            }

            if ($item->isOffer) {
                $product->markAsOffer();
            }

            if ($item->wholesalePrice) {
                $product->wholesalePrice = $item->wholesalePrice;
            }

            if ($item->linkedCompanyStatus) {
                $product->linkedCompanyStatus = $item->linkedCompanyStatus;
            }

            if ($item->additionalInformation) {
                $product->setAdditionalInformation((array) $item->additionalInformation);
            }

            $this->add($product);
        }
    }

    private function cleanProductUselessFieldsToBasket(BasketProduct $basketProduct): BasketProduct
    {
        $basketProduct = $this->cleanProductUselessFields($basketProduct);

        if (!empty($basketProduct->upgradeProduct)) {
            $basketProduct->upgradeProduct = $this->cleanProductUselessFields($basketProduct->upgradeProduct);

            if(!empty($basketProduct->upgradeProduct->upgradeProduct)) {
                $basketProduct->upgradeProduct->upgradeProduct = null;
            }
        }

        return $basketProduct;
    }

    private function cleanProductUselessFields(BasketProduct $basketProduct): BasketProduct
    {
        $basketProduct->emailText = null;
        $basketProduct->author = null;
        $basketProduct->editor = null;
        $basketProduct->upgradeDescription = null;

        if (!empty($basketProduct->page)) {
            $basketProduct->page->abstract = '';
            $basketProduct->page->text = '';
        }

        return $basketProduct;
    }


    private function verifyBasketItems(BasketProduct $basketProduct): void
    {
        foreach ($this->items as $item) {
            if (in_array($item->getId(), [Product::SOLE_TRADER_START_UP_PACK, Product::SOLE_TRADER_START_UP_PACK_PLUS], true)
                && $basketProduct->getId() === Product::VAT_REGISTRATION) {
                throw BasketException::itemCouldNotBeAddedForSole($basketProduct, $item);
            }
            if ($item->getId() === Package::PACKAGE_ANNA) {
                throw BasketException::itemCouldNotBeAddedForAnnaPackage($basketProduct, $item);
            }
        }
    }

    private function verifyProduct(BasketProduct $basketProduct): void
    {
        if (!$basketProduct->canBeAdded($this)) {
            if (!$basketProduct->isLinkedOrNoPackage($this)) {
                throw BasketException::itemCouldNotBeAddedForPackage();
            }
            throw BasketException::itemCouldNotBeAdded($basketProduct);
        }
    }

    public function requiresUkDirector(): bool
    {
        foreach ($this->items as $item) {
            if ($item->requiresUkDirector()) {
                return true;
            }
        }

        return false;
    }

    public function requiresAdultDirector(): bool
    {
        foreach ($this->items as $item) {
            if ($item->requiresAdultDirector()) {
                return true;
            }
        }

        return false;
    }

    public function getRequirements(): ProductRequirements
    {
       return ProductRequirementsFactory::fromBasket($this);
    }
}
