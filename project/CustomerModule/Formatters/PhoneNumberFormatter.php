<?php

namespace CustomerModule\Formatters;

class PhoneNumberFormatter
{
    public static function convertToInternationalFormat(string $phone): string
    {
        $phone = preg_replace("/\D/", "", trim($phone));

        if (strlen($phone) < 9) {
            return $phone;
        }

        $phone = ltrim($phone, '0');
        $phone = preg_replace('/^(\+?440)/', '44', $phone);

        if (str_starts_with($phone, '7')) {
            return sprintf('44%s', $phone);
        }

        if (!str_starts_with($phone, '44')) {
            return sprintf('447%s', $phone);
        }

        return $phone;
    }

    public static function convertToInternationalFormatWithPlus(string $phone): string
    {
        $phone = preg_replace("/\D/", "", trim($phone));

        if (strlen($phone) < 9) {
            return $phone;
        }

        $phone = ltrim($phone, '0');
        $phone = preg_replace('/^(?:00|\+)?44(0)?/', '+44', $phone);

        if (str_starts_with($phone, '7')) {
            return sprintf('+44%s', $phone);
        }

        if (!str_starts_with($phone, '+447')) {
            return sprintf('+447%s', $phone);
        }

        return $phone;
    }

    public static function convertToLocalFormat(string $phone): string
    {
        $phone = preg_replace("/\D/", "", trim($phone));

        if (strlen($phone) < 9) {
            return $phone;
        }

        $phone = preg_replace('/^(?:00|\+)?44/', '', $phone);

        if (str_starts_with($phone, '7')) {
            return sprintf('0%s', $phone);
        }

        if (!str_starts_with($phone, '07')) {
            return sprintf('07%s', $phone);
        }

        return $phone;
    }
}
