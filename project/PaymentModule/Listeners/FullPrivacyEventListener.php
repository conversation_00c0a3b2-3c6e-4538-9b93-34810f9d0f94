<?php

namespace PaymentModule\Listeners;

use Entities\Customer;
use Entities\OrderItem;
use Exceptions\Business\ItemNotFound;
use Models\Products\Package;
use Repositories\OrderRepository;
use PaymentModule\Events\PaymentEvent;
use Models\Products\Product;
use Psr\Log\LoggerInterface;
use Repositories\CompanyRepository;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class FullPrivacyEventListener  implements EventSubscriberInterface
{
    /**
     * @var CompanyRepository
     */
    private $companyRepository;

    /**
     * @var OrderRepository
     */
    private $orderRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    public function __construct(
        CompanyRepository $companyRepository,
        OrderRepository $orderRepository,
        LoggerInterface $logger
    )
    {
        $this->companyRepository = $companyRepository;
        $this->orderRepository = $orderRepository;
        $this->logger = $logger;
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'payment.succeeded' => 'onPaymentSuccess'
        ];
    }

    public function onPaymentSuccess(PaymentEvent $paymentEvent): void
    {
        $basket = $paymentEvent->getBasket();
        if (
            $basket->hasProduct(Package::PACKAGE_FULL_PRIVACY)
            || $basket->hasProduct(Package::PACKAGE_FULL_PRIVACY_MONTHLY)
            || $basket->hasProduct(Package::PACKAGE_FULL_PRIVACY_MONTHLY_8_2023)
        ) {
            /** @var Customer $customer */
            $customer = $paymentEvent->getCustomer();
            $orderId = $basket->getOrderId();
            try {
                $order = $this->orderRepository->getCustomerOrder($customer, $orderId);
                /** @var OrderItem $orderItem */
                $orderItem = $order->getItems()->first();
                $company = $orderItem->getCompany();
                $company->setRegisteredOfficeId(Product::PRODUCT_REGISTERED_OFFICE);
                $company->setServiceAddressId(Product::PRODUCT_SERVICE_ADDRESS);
                $this->companyRepository->saveEntity($company);
            } catch (ItemNotFound $e) {
                $this->logger->error(
                    sprintf('Unable to find the order %s for the customer %s to set Full Privacy Requirements.', $orderId, $customer->getId()),
                    [
                        'paymentEvent' => $paymentEvent,
                        'customer' => $customer
                    ]
                );

            }
        }
    }
}