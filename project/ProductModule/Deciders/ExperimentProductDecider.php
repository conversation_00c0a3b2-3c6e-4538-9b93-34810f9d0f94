<?php

namespace ProductModule\Deciders;

class ExperimentProductDecider
{
    /**
     * @var array
     */
    private $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    public function isExperimentProductRunning(string $experimentActionId): bool
    {
        foreach ($this->config as $key => $experimentConfig) {
            if (isset($_COOKIE[$key])) {
                $variant = $_COOKIE[$key];

                if (isset($experimentConfig[$variant][$experimentActionId])) {
                    return true;
                }
            }
        }
        return false;
    }

}
