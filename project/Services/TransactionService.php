<?php

namespace Services;

use Entities\Customer;
use Entities\Order;
use Entities\Payment\Token;
use Entities\Transaction;
use Exception;
use OmnipayModule\Entities\OmnipayCardResponseDetails\OmnipayCard;
use OmnipayModule\Entities\OmnipayPaymentResponseDetails\OmnipayPaymentResponseDetails;
use PaymentModule\Contracts\IPaymentResponse;
use Repositories\TransactionRepository;
use SagePayToken\Token\Exception\FailedResponse;
use Models\OldModels\Transaction as OldTransaction;
use Utils\NetteSmartObject;

/**
 * @deprecated use TransactionPaymentLogger
 */
class TransactionService extends NetteSmartObject
{
    /**
     * @var TransactionRepository
     */
    private $repository;

    /**
     * @param TransactionRepository $repository
     */
    public function __construct(TransactionRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * @param Transaction $transaction
     */
    public function save(Transaction $transaction)
    {
        $this->repository->saveEntity($transaction);
    }

    /**
     * @param Customer $customer
     * @return array
     */
    public function getTransactions(Customer $customer)
    {
        return $this->repository->getTransactions($customer);
    }

    /**
     * @param Customer $customer
     * @param string $error
     */
    public function saveFailedTransaction(Customer $customer, $error)
    {
        $typeId = $customer->isOmnipay() ? Transaction::TYPE_OMNIPAY : Transaction::TYPE_SAGEPAY;

        $transaction = new Transaction($customer, $typeId);
        $transaction->setStatusId(Transaction::STATUS_FAILED);
        $transaction->setError($error);
        $this->save($transaction);
    }

    /**
     * @param Customer $customer
     * @param Token $token
     * @param Exception $e
     */
    public function saveFailedSageTransactionWithToken(Customer $customer, Token $token, Exception $e)
    {
        $transaction = new Transaction($customer, Transaction::TYPE_SAGEPAY);
        $transaction->setStatusId(Transaction::STATUS_FAILED);
        $transaction->setError($e->getMessage());
        $transaction->setToken($token);
        $transaction->setCardHolder($token->getCardHolder());
        $transaction->setCardNumber($token->getCardNumber());
        $transaction->setCardType($token->getCardType());
        $transaction->setCardExpiryDate($token->getCardExpiryDate());
        if ($e instanceof FailedResponse && $summary = $e->getSummary()) {
            $transaction->setTransactionIds($summary);
        }
        $this->save($transaction);
    }

    /**
     * @param Customer $customer
     * @param IPaymentResponse $paymentResponse
     * @param Order $order
     * @return Transaction
     */
    public function saveTransactionFromPaymentResponse(
        Customer $customer,
        IPaymentResponse $paymentResponse,
        Order $order,
        bool $persistTransaction = true
    ): Transaction
    {
        $type = $customer->isOmnipay() ? Transaction::TYPE_OMNIPAY : Transaction::TYPE_SAGEPAY;
        $transaction = new Transaction($customer, $type);
        $transaction->setOrderCode($paymentResponse->getSageVpsTxId());
        $transaction->setCardHolder($paymentResponse->getCardHolder());
        $transaction->setDetails(serialize($paymentResponse->getSagePaymentInfo()));
        $transaction->setCardNumber($paymentResponse->getCardNumber());
        $transaction->setCardType($paymentResponse->getCardType());
        $transaction->setRequestType(Transaction::PAYMENT_TYPE_PAYMENT);
        $transaction->setVendorTXCode($paymentResponse->getSageVendorTxCode());
        $transaction->setVpsAuthCode($paymentResponse->getSageTxAuthNo());
        $transaction->setSecurityKey($paymentResponse->getSageSecurityKey());

        if ($paymentResponse->getParentTransaction()){
            $transaction->setParentTransaction($paymentResponse->getParentTransaction());
        }

        $transaction->setOrder($order);

        if ($persistTransaction) {
            $this->repository->saveEntity($transaction);
        }

        return $transaction;
    }

    public function saveTransactionFromOmnipayPayment(
        Customer $customer,
        OmnipayPaymentResponseDetails $omnipayPaymentResponseDetails,
        Order $order,
    ): Transaction
    {
        $omnipayPaymentResponseDetails->isDirectDebit()
            ? $transaction = new Transaction($customer, Transaction::TYPE_OMNIPAY_DIRECT_DEBIT)
            : $transaction = new Transaction($customer, Transaction::TYPE_OMNIPAY);

        try {
            $charges = $omnipayPaymentResponseDetails->getCharges();
        } catch (\Exception $e) {
            $charges = [];
        }

        if (!empty($charges)) {
            $paymentDetails = $charges[0]->getPaymentDetails();

            $transaction->setCardNumber($paymentDetails->getCardNumber());
            $transaction->setCardType($paymentDetails->getCardType());
            $transaction->setCardHolder($paymentDetails->getCardHolder());
        }

        $transaction->setOrderCode($omnipayPaymentResponseDetails->getId());
        $transaction->setRequestType(Transaction::PAYMENT_TYPE_PAYMENT);
        $transaction->setOrder($order);

        match ($omnipayPaymentResponseDetails->getStatus()) {
            OmnipayPaymentResponseDetails::PAYMENT_PROCESSING => $transaction->setStatusId(OldTransaction::STATUS_PROCESSING),
            OmnipayPaymentResponseDetails::PAYMENT_SUCCEEDED => $transaction->setStatusId(OldTransaction::STATUS_SUCCEEDED),
            default => $transaction->setStatusId(OldTransaction::STATUS_FAILED),
        };

        return $this->repository->saveEntity($transaction);
    }

    public function getTransactionByOrderCode(string $orderCode): ?Transaction
    {
        return $this->repository->findOneByOrderCode($orderCode);
    }

    public function updateTransactionStatusId(Transaction $transaction, string $status): Transaction
    {
        $transaction->setStatusId($status);
        $this->repository->saveEntity($transaction);

        return $transaction;
    }

    public function updateTransactionError(Transaction $transaction, ?string $errorMessage = 'Unknown error'): Transaction
    {
        $transaction->setError($errorMessage);
        $this->repository->saveEntity($transaction);

        return $transaction;
    }

    public function saveFailedTransactionWithOrder(
        Customer $customer,
        Order $order,
        string $errorMessage,
        ?OmnipayCard $card = null
    ): void
    {
        $typeId = $customer->isOmnipay() ? Transaction::TYPE_OMNIPAY : Transaction::TYPE_SAGEPAY;

        $transaction = new Transaction($customer, $typeId);
        $transaction->setOrder($order);
        $transaction->setStatusId(Transaction::STATUS_FAILED);
        $transaction->setRequestType(Transaction::PAYMENT_TYPE_PAYMENT);

        if (!empty($card)) {
            $errorMessage = \sprintf("%s - %s", $errorMessage, $card->getId());
            $transaction->setCardNumber($card->getCardNumber());
            $transaction->setCardType($card->getCardType());
            $transaction->setCardExpiryDate($card->getCardExpiryDate());
        }

        $transaction->setError($errorMessage);
        $this->save($transaction);
    }

    public function getTransactionByOrderId(int $orderId): ?Transaction
    {
        return $this->repository->findOneBy(['order' => $orderId]);
    }
}
