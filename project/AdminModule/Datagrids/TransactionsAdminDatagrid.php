<?php

namespace AdminModule\Datagrids;

use DataGrid\src\DataGrid\Columns\DataGridColumn_Abstract;
use DataGrid\src\DataGrid\DataGrid;
use Framework\FApplication;
use Legacy\Nette\Web\Html;
use Models\OldModels\Transaction;

class TransactionsAdminDatagrid extends DataGrid
{
    public function getEmail(DataGridColumn_Abstract $column, $text, $row)
    {
        return $this->Callback_failedTransaction($column, $row->getCustomer() ? $row->getCustomer()->getEmail() : 'n/a', $row);
    }

    public function Callback_failedTransaction(DataGridColumn_Abstract $column, $text, $row)
    {
        $text = Html::el('span')
                ->setText($text);
        if ($row->getStatusId() == Transaction::STATUS_FAILED) {
            $text->style('color: red;');
        }
        return $text;
    }

    protected function init()
    {
        $router = FApplication::$router;

        $this->disableOrder();

        $this->addTextColumn('transactionId', 'Id', 60)
                ->addCallback(array($this, 'Callback_failedTransaction'));
        $this->addTextColumn('statusId', 'Status', 70)
                ->addCallback(array($this, 'Callback_failedTransaction'))
                ->replacement(Transaction::$statuses);
        $this->addTextColumn('typeId', 'Type', 50)
                ->addCallback(array($this, 'Callback_failedTransaction'))
                ->replacement(Transaction::$types);
        $this->addTextColumn('cardHolder', 'Customer')
                ->addCallback(array($this, 'getEmail'));
        $this->addDateColumn('dtc', 'Created', 120, 'd/m/Y @ H:i')
                ->addCallback(array($this, 'Callback_failedTransaction'));

        $action = $this->addActionColumn('actions', 'Actions', 60);
        $action->getHeaderPrototype()->class('center');

        // view
        $label = Html::el('a')
                ->setText('View');
        $url = $router->link('view');
        $action->addAction('view', $label, $url);

        $this->addTextFilter('email', 'Email:', 'c');
        $this->addSelectFilter('statusId', 'Status:', Transaction::$statuses, 't');
        $this->addSelectFilter('typeId', 'Type:', Transaction::$types, 't');
    }
}
