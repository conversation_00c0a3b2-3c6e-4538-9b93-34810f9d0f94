<?php

namespace AdminModule\forms\Product\Properties;

use BankingModule\Entities\CompanyCustomer;
use Framework\FNode;
use Framework\Forms\Controls\AsmSelect;
use Framework\Forms\Controls\Checkbox;
use Framework\Forms\Controls\CmsFckArea;
use Framework\Forms\Controls\CmsFile;
use Framework\Forms\Controls\CmsImg;
use Framework\Forms\Controls\FControl;
use Framework\Forms\Controls\Select;
use Framework\Forms\FForm;
use Framework\Forms\Validators\Email;
use Models\Products\BasketProduct;
use Models\Products\Product;
use OfferModule\Mappers\ToolkitOfferMapper;
use ToolkitOfferModule\Repositories\ToolkitOfferRepository;

class ProductPropertiesForm extends FForm
{
    public const ASSOCIATED_IMAGE_PAGE = 1346;
    public const ATTACHMENTS_COUNT = 5;
    public const ATTACHMENT_FILE_PAGE = 575;
    public const UPDATE_IMAGE_PAGE = 966;
    public const PAGES_FOLDER = 266;

    /**
     * @var IProductPropertiesFormDelegate
     */
    protected $delegate;

    /**
     * @var Product
     */
    protected $node;

    /**
     * @var array
     */
    protected $products;

    /**
     * @var array
     */
    protected $serviceTypes;

    /**
     * @var array
     */
    protected $serviceDurations;

    /**
     * @var array
     */
    protected $renewalDtStartBehaviorOptions;

    /**
     * @var ToolkitOfferRepository
     */
    private $toolkitOfferRepository;

    /**
     * @param IProductPropertiesFormDelegate $delegate
     * @param BasketProduct                  $node
     * @param array                          $products
     * @param array                          $serviceTypes
     * @param array                          $serviceDurations
     * @param ToolkitOfferRepository         $toolkitOfferRepository
     */
    public function startup(
        IProductPropertiesFormDelegate $delegate,
        BasketProduct $node,
        array $products,
        array $serviceTypes,
        array $serviceDurations,
        array $renewalDtStartBehaviorOptions,
        ToolkitOfferRepository $toolkitOfferRepository,
    ) {
        $this->delegate = $delegate;
        $this->node = $node;
        $this->products = $products;
        $this->serviceTypes = $serviceTypes;
        $this->serviceDurations = $serviceDurations;
        $this->renewalDtStartBehaviorOptions = $renewalDtStartBehaviorOptions;
        $this->toolkitOfferRepository = $toolkitOfferRepository;

        $this->buildForm();
        $this->onValid = [$this, 'process'];
        $this->start();
    }

    public function process()
    {
        try {
            $node = $this->node;
            $data = $this->getValues();

            $node->onlyOneItem = $data['onlyOneItem'];
            $node->maxQuantityOne = $data['maxQuantityOne'];
            $node->conditionedById = $data['conditionedById'];
            $node->requiredCompanyNumber = $data['requiredCompanyNumber'];

            $node->requiredIncorporatedCompanyNumber = $data['requiredIncorporatedCompanyNumber'];
            $node->onlyOurCompanies = $data['onlyOurCompanies'];
            $node->responsibleEmails = $data['responsibleEmails'];
            $node->sageNominalCode = $data['sageNominalCode'];
            $node->notApplyVat = $data['notApplyVat'];
            $node->nonVatableValue = $data['nonVatableValue'];
            $node->nonVatableDescription = $data['nonVatableDescription'];
            $node->emailText = $data['emailText'];
            $node->basketText = $data['basketText'];
            $node->lockCompany = $data['lockCompany'];
            $node->upgradeDescription = $data['upgradeDescription'];
            $node->upgradeImageId = $data['upgradeImageId'];
            $node->associatedImageId = $data['associatedImageId'];
            $node->associatedText = $data['associatedText'];
            $node->associatedDescription = $data['associatedDescription'];
            $node->associatedIconClass = $data['associatedIconClass'];

            // attachments
            $node->emailCmsFileAttachments = [];
            for ($i = 0; $i < self::ATTACHMENTS_COUNT; ++$i) {
                if ($data['attachment' . $i]) {
                    $node->emailCmsFileAttachments[] = $data['attachment' . $i];
                }
            }
            $node->serviceTypeId = $data['serviceTypeId'];
            $node->setDuration($data['serviceDuration']);
            $node->renewalProductId = $data['renewalProductId'];
            $node->optionalRenewalProductId = $data['optionalRenewalProductId'];
            $node->renewalDtStartBehavior = $data['renewalDtStartBehavior'];
            $node->isAutoRenewalAllowed = $data['isAutoRenewalAllowed'];
            $node->setVoServiceEligible($data['isVoServiceEligible']);
            $node->setVoServiceDurationInMonths($data['voServiceDurationInMonths']);
            $node->isIdCheckRequired = $data['isIdCheckRequired'];
            $node->bankingEnabled = $data['bankingEnabled'];
            $node->bankingOptions = $data['bankingOptions'] ?: [];
            $node->bankingRequired = $data['bankingRequired'];
            $node->setPrintedCertificateOptionEnabled($data['printedCertificateOptionEnabled']);
            $node->setRemovableFromBasket($data['removableFromBasket']);
            $node->setRemoveFromBasketConfirmation($data['removeFromBasketConfirmation']);
            $node->setAvailableForPayByPhone($data[BasketProduct::PROPERTY_AVAILABLE_FOR_PAY_BY_PHONE]);
            $node->setToolkitOfferTypes($data['toolkitOfferTypes'] ?? []);

            $node->setIsInitialProduct($data['isInitialProduct']);
            $node->setIsRenewalProduct($data['isRenewalProduct']);
            $node->setShowInMyServicesPage($data['showInMyServicesPage'] ?? false);

            $node->setRegistrationReviewEnabled($data['registrationReviewEnabled']);

            $node->requiredCorePackage = $data['requiredCorePackage'];

            $node->setQuotas($data['quotas'] ?? 0);

            if (Product::hasMailboxSettings($node->getNodeName())) {
                $node->setMailboxAcceptanceStatutory($data['mailboxAcceptStatutory']);
                $node->setMailboxAcceptanceNonStatutory($data['mailboxAcceptNonStatutory']);
                $node->setMailboxAcceptanceParcel($data['mailboxAcceptParcel']);
                $node->setMailboxQuotaStatutoryScanned($data['mailboxQuotaStatutoryScanned']);
                $node->setMailboxQuotaStatutoryCollect($data['mailboxQuotaStatutoryCollect']);
                $node->setMailboxQuotaStatutoryPost($data['mailboxQuotaStatutoryPost']);
                $node->setMailboxQuotaNonStatutoryScanned($data['mailboxQuotaNonStatutoryScanned']);
                $node->setMailboxQuotaNonStatutoryCollect($data['mailboxQuotaNonStatutoryCollect']);
                $node->setMailboxQuotaNonStatutoryPost($data['mailboxQuotaNonStatutoryPost']);
                $node->setMailboxQuotaParcelCollect($data['mailboxQuotaParcelCollect']);
                $node->setMailboxQuotaParcelPost($data['mailboxQuotaParcelPost']);
                $node->setMailboxExtraQuotaPostItemFee($data['mailboxExtraQuotaPostItemFee']);
                $node->setMailboxExtraQuotaParcelFee($data['mailboxExtraQuotaParcelFee']);
                $node->setMailboxHandlingFeePostItem($data['mailboxHandlingFeePostItem']);
                $node->setMailboxHandlingFeeParcel($data['mailboxHandlingFeeParcel']);
                $node->setMailboxForwardingFeePostItem($data['mailboxForwardingFeePostItem']);
                $node->setMailboxForwardingFeeParcel($data['mailboxForwardingFeeParcel']);
                $node->setMailboxSettingPostItem($data['mailboxSettingPostItem']);
                $node->setMailboxSettingParcel($data['mailboxSettingParcel']);
                $node->setMailboxPayToReleaseFeePostItem($data['mailboxPayToReleaseFeePostItem']);
                $node->setMailboxPayToReleaseFeeParcel($data['mailboxPayToReleaseFeeParcel']);
            }

            $node->customerType = $data['customerType'];

            $node->save();

            $this->clean();
            $this->delegate->productPropertiesFormSucceeded($this);
        } catch (\Exception $e) {
            $this->delegate->productPropertiesFormFailed($this, $e);
        }
    }

    public function Validator_CommaEmails($control, $error, $params)
    {
        $value = $control->getValue();
        if (!empty($value)) {
            $validator = new Email($error, $params, $control->owner, $control);
            $emails = explode(',', $value);
            foreach ($emails as $key => $email) {
                $isValid = $validator->isValid(trim($email));
                if ($isValid == false) {
                    return $error;
                }
            }
        }

        return true;
    }

    public function Validator_serviceRequiredElement(FControl $control, $error, $params)
    {
        $value = $control->getValue();
        list($serviceDurationControl) = $params;
        $serviceDuration = $serviceDurationControl->getValue();
        if (!$value && $serviceDuration) {
            return $error;
        }

        return true;
    }

    /**
     * @param Select $control
     * @param string $error
     * @param array  $params
     *
     * @return bool
     */
    public function Validator_voServiceRequired(Select $control, $error, array $params)
    {
        $durationInMonths = $control->getValue();
        /** @var Checkbox $isVoServiceEligibleControl */
        $isVoServiceEligibleControl = $params['isVoServiceEligible'];
        $isVoServiceEligible = $isVoServiceEligibleControl->getValue();
        if ($isVoServiceEligible && !$durationInMonths) {
            return $error;
        }

        return true;
    }

    private function buildForm()
    {
        $node = $this->node;

        $this->addFieldset('Banking');
        $this->addCheckbox('bankingEnabled', 'Banking: ', 1)
            ->setValue($node->bankingEnabled);
        $this->addCheckbox('bankingRequired', 'Banking is required: ', 1)
            ->setValue($node->bankingRequired);
        $this->add(AsmSelect::class, 'bankingOptions', 'Options:')
            ->title('--- Select --')
            ->setValue($node->bankingOptions)
            ->setOptions(CompanyCustomer::$availableBankingOptions);

        $this->addFieldset('Incorporation Certificate');
        $this->addCheckbox('printedCertificateOptionEnabled', 'Ask how to receive Incorporation Certificate: ', 1)
            ->setValue($node->printedCertificateOptionEnabled)
            ->setDescription('(Will prompt the user to ask if they want the certificate sent digitally or physically)');

        $this->addFieldset('Basket');
        $this->addCheckbox('removableFromBasket', 'Can be removed from basket: ', 1)
            ->setValue($node->isRemovableFromBasket());
        $this->addArea('removeFromBasketConfirmation', 'Remove from basket confirmation: ')
            ->cols(50)->rows(3)
            ->setDescription('(Confirmation message will appear before removing product from basket. No confirmation popup if empty)')
            ->setValue($node->getRemoveFromBasketConfirmation());

        $this->addFieldset('Service');
        $this->addSelect('serviceTypeId', 'Service Type: ', $this->serviceTypes)
            ->setFirstOption('--- None ---')
            ->setValue($this->node->serviceTypeId);
        $this->addSelect('serviceDuration', 'Service Duration:', $this->serviceDurations)
            ->setFirstOption('--- Select ---')
            ->setValue($this->node->getDuration());
        $this->addSelect('renewalProductId', 'Renewal Product:', $this->products)
            ->setFirstOption('--- Select ---')
            ->setValue($this->node->renewalProductId)
            ->addRule([$this, 'Validator_serviceRequiredElement'], 'Required!', [$this['serviceDuration']]);
        $this->addSelect('optionalRenewalProductId', 'Optional Renewal Product:', $this->products)
            ->setFirstOption('--- Select ---')
            ->setValue($this->node->optionalRenewalProductId);
        $this->addSelect('renewalDtStartBehavior', 'Renewal Start Date Behaviour:', $this->renewalDtStartBehaviorOptions)
            ->setFirstOption('--- Select ---')
            ->setValue($this->node->renewalDtStartBehavior);
        $this->addCheckbox('isAutoRenewalAllowed', 'Auto Renewal Allowed:', 1)
            ->setDescription('(Service can be set to be renewed automatically)')
            ->setValue($node->isAutoRenewalAllowed);

        $this->addCheckbox('isInitialProduct', 'Is Initial Product:', 1)
            ->setValue($node->isInitialProduct());
        $this->addCheckbox('isRenewalProduct', 'Is Renewal Product:', 1)
            ->setValue($node->isRenewalProduct());

        if (!$node->isRenewalProduct()) {
            $this->addCheckbox('showInMyServicesPage', 'Show in ‘My Services’ page:', 1)
                ->setValue($node->isShowInMyServicesPage());
        }

        $this->addFieldset('ID check');
        $this->addCheckbox('isIdCheckRequired', 'ID check required:', 1)
            ->setValue($node->isIdCheckRequired);

        // properties
        $this->addFieldset('Properties');
        $this->addSelect('conditionedById', 'Product Required:', $this->products)
            ->setDescription('(The product selected from the drop down list must be in the basket for a user to add this product)')
            ->setFirstOption('--- Select ---')
            ->setValue($node->conditionedById);
        $this->addCheckbox('onlyOneItem', 'Only 1 Item:', 1)
            ->setDescription('(Absolutely only 1 of this product in the basket)')
            ->setValue($node->onlyOneItem);
        $this->addCheckbox('maxQuantityOne', 'Max Quantity 1:', 1)
            ->setDescription('(The product can be in the basket multiple times, but the quantity of each can only be 1)')
            ->setValue($node->maxQuantityOne);
        if (!empty($node->getQuotas())) {
            $this->addText('quotas', 'Quotas:')
                ->setDescription('(The Mail Forwarding quota amount for this product.)')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Quotas value has to be number!')
                ->setValue($node->getQuotas());
        }
        $this->addSelect('customerType', 'Customer Type:', BasketProduct::CUSTOMER_TYPE_NAMES)
            ->setDescription('(The customer type that can add this product to the basket)')
            ->setValue($node->customerType);

        // Mailbox Properties
        if (Product::hasMailboxSettings($node->getNodeName())) {
            $this->addFieldset('[Mailbox] Acceptance (Defines the types of post we accept for this package.)');
            $this->addCheckbox('mailboxAcceptStatutory', 'Statutory:', 1)
                ->setValue((bool) $node->getMailboxAcceptanceStatutory());
            $this->addCheckbox('mailboxAcceptNonStatutory', 'Non-statutory:', 1)
                ->setValue((bool) $node->getMailboxAcceptanceNonStatutory());
            $this->addCheckbox('mailboxAcceptParcel', 'Parcel:', 1)
                ->setValue((bool) $node->getMailboxAcceptanceParcel());

            $this->addFieldset('[Mailbox] Quotas (Defines the quota amount before the extra quota fee start getting charged for post-items and Parcel.)');
            $this->addText('mailboxQuotaStatutoryScanned', 'Statutory Scanned:')
                ->setDescription('(-1 means unlimited, 0 means we always charge the extra quota fee, any other number is the quota amount before the extra quota fee starts getting charged.)')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Quota value has to be a number!')
                ->setValue($node->getMailboxQuotaStatutoryScanned());
            $this->addText('mailboxQuotaStatutoryCollect', 'Statutory Collect:')
                ->setDescription('(-1 means unlimited, 0 means we always charge the extra quota fee, any other number is the quota amount before the extra quota fee starts getting charged.)')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Quota value has to be a number!')
                ->setValue($node->getMailboxQuotaStatutoryCollect());
            $this->addText('mailboxQuotaStatutoryPost', 'Statutory Post:')
                ->setDescription('(-1 means unlimited, 0 means we always charge the extra quota fee, any other number is the quota amount before the extra quota fee starts getting charged.)')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Quota value has to be a number!')
                ->setValue($node->getMailboxQuotaStatutoryPost());
            $this->addText('mailboxQuotaNonStatutoryScanned', 'Non-statutory Scanned:')
                ->setDescription('(-1 means unlimited, 0 means we always charge the extra quota fee, any other number is the quota amount before the extra quota fee starts getting charged.)')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Quota value has to be a number!')
                ->setValue($node->getMailboxQuotaNonStatutoryScanned());
            $this->addText('mailboxQuotaNonStatutoryCollect', 'Non-statutory Collect:')
                ->setDescription('(-1 means unlimited, 0 means we always charge the extra quota fee, any other number is the quota amount before the extra quota fee starts getting charged.)')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Quota value has to be a number!')
                ->setValue($node->getMailboxQuotaNonStatutoryCollect());
            $this->addText('mailboxQuotaNonStatutoryPost', 'Non-statutory Post:')
                ->setDescription('(-1 means unlimited, 0 means we always charge the extra quota fee, any other number is the quota amount before the extra quota fee starts getting charged.)')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Quota value has to be a number!')
                ->setValue($node->getMailboxQuotaNonStatutoryPost());
            $this->addText('mailboxQuotaParcelCollect', 'Parcel Collect:')
                ->setDescription('(-1 means unlimited, 0 means we always charge the extra quota fee, any other number is the quota amount before the extra quota fee starts getting charged.)')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Quota value has to be a number!')
                ->setValue($node->getMailboxQuotaParcelCollect());
            $this->addText('mailboxQuotaParcelPost', 'Parcel Post:')
                ->setDescription('(-1 means unlimited, 0 means we always charge the extra quota fee, any other number is the quota amount before the extra quota fee starts getting charged.)')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Quota value has to be a number!')
                ->setValue($node->getMailboxQuotaParcelPost());
            $this->addText('mailboxExtraQuotaPostItemFee', 'Extra Quota Post Item Fee:')
                ->setDescription('(The fee charged for each item when the quota is exceeded for post-items. (£))')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Fee value has to be a number!')
                ->setValue($node->getMailboxExtraQuotaPostItemFee());
            $this->addText('mailboxExtraQuotaParcelFee', 'Extra Quota Parcel Fee:')
                ->setDescription('(The fee charged for each item when the quota is exceeded for parcels. (£))')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Fee value has to be a number!')
                ->setValue($node->getMailboxExtraQuotaParcelFee());

            $this->addFieldset('[Mailbox] Handling Fees (Defines the price for the handling fee of post-items and parcels.)');
            $this->addText('mailboxHandlingFeePostItem', 'Handling Fee Post Item:')
                ->setDescription('(The handling fee charged for each post-item received. (£))')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Fee value has to be a number!')
                ->setValue($node->getMailboxHandlingFeePostItem());
            $this->addText('mailboxHandlingFeeParcel', 'Handling Fee Parcel:')
                ->setDescription('(The handling fee charged for each parcel received. (£))')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Fee value has to be a number!')
                ->setValue($node->getMailboxHandlingFeeParcel());

            $this->addFieldset('[Mailbox] Forwarding Fees (Defines the price for the forwarding fee of post-items and parcels.)');
            $this->addText('mailboxForwardingFeePostItem', 'Forwarding Fee Post Item:')
                ->setDescription('(The forwarding fee charged for each post-item received. (£))')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Fee value has to be a number!')
                ->setValue($node->getMailboxForwardingFeePostItem());
            $this->addText('mailboxForwardingFeeParcel', 'Forwarding Fee Parcel:')
                ->setDescription('(The forwarding fee charged for each parcel received. (£))')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Fee value has to be a number!')
                ->setValue($node->getMailboxForwardingFeeParcel());

            $this->addFieldset('[Mailbox] Pay To Release Fee (Defines the price for the pay to view fee of post-items and parcels.)');
            $this->addText('mailboxPayToReleaseFeePostItem', 'Pay To Release Fee Post Item:')
                ->setDescription('(The pay to release fee charged for each post-item received. (£))')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Fee value has to be a number!')
                ->setValue($node->getMailboxPayToReleaseFeePostItem());
            $this->addText('mailboxPayToReleaseFeeParcel', 'Pay To Release Fee Parcel:')
                ->setDescription('(The pay to release fee charged for each parcel received. (£))')
                ->size(3)
                ->addRule(FForm::NUMERIC, 'Fee value has to be a number!')
                ->setValue($node->getMailboxPayToReleaseFeeParcel());

            $this->addFieldset('[Mailbox] Settings (Defines the default behavior of forwarding when a Post-Item or Parcel is Released.)');
            $this->addSelect('mailboxSettingPostItem', 'Post Item:', [1 => 'Scan Only', 2 => 'Scan & Collect', 3 => 'Scan & Post'])
                ->setValue($node->getMailboxSettingPostItem());
            $this->addSelect('mailboxSettingParcel', 'Parcel:', [1 => 'Collect', 2 => 'Post'])
                ->setValue($node->getMailboxSettingParcel());
        }

        // company number
        $this->addFieldset('Company number');
        $this->addCheckbox('requiredCompanyNumber', 'Company Required:', 1)
            ->setDescription('(The user will be required to choose a company from their account or manually enter the company number that is to be associated with this product)')
            ->setValue($node->requiredCompanyNumber);

        $this->addCheckbox('onlyOurCompanies', 'On Account:', 1)
            ->setDescription('(This product can only be purchased if the user has the company on their account)')
            ->setValue($node->onlyOurCompanies);

        $this->addCheckbox('requiredIncorporatedCompanyNumber', 'Incorporated On Account:', 1)
            ->setDescription('(This product can only be purchased if the user incorporated the company with us.)')
            ->setValue($node->requiredIncorporatedCompanyNumber);

        $this->addFieldset('Requires Core Package');
        $this->addCheckbox('requiredCorePackage', 'Core Package Required:', 1)
            ->setDescription('(This product can only be added to a basket where there is already a Core Package.)')
            ->setValue($node->requiredCorePackage);

        // other
        $this->addFieldset('Other');
        $this->addArea('responsibleEmails', 'Email CMS: ')
            ->cols(50)->rows(3)
            ->setDescription('(Email addresses entered in this field will be sent an email notifcation of every purchase of this product. Multiple email address must be comma seperated eg. <EMAIL>, <EMAIL>)')
            ->setValue($node->responsibleEmails)
            ->addRule([$this, 'Validator_CommaEmails'], 'Emails are not valid!');
        $this->addText('sageNominalCode', 'Sage nominal code:')
            ->setDescription('(Used by the finance team to help prepare the tax returns. Unique reference within Sage software.)')
            ->size(30)
            ->setValue($node->sageNominalCode);
        $this->addCheckbox('notApplyVat', 'No VAT:', 1)
            ->setDescription('(If ticked, VAT will not be applied to this product.)')
            ->setValue($node->notApplyVat);
        $this->addText('nonVatableValue', 'Non Vatable Value:')
            ->setDescription('(VAT will not be applied to the amount entered here. The standard VAT rate will be applied to the remainder of the product price.)')
            ->size(3)
            ->addRule(FForm::NUMERIC, 'Not vatable value has to be number!')
            ->setValue($node->nonVatableValue);
        $this->addText('nonVatableDescription', 'Non Vatable Description:')
            ->setDescription('(Used to display disbursements on invoices.)')
            ->size(75)
            ->setValue($node->nonVatableDescription);
        $this->add(CmsFckArea::class, 'emailText', 'Email Text:')
            ->setSize(500, 200)
            ->setToolbar('Basic')
            ->setValue($node->emailText);
        $this->addArea('basketText', 'Basket text:')->class('paulo-markdown-editor')->cols(40)->rows(5)
            ->setValue($node->basketText);
        $this->addCheckbox('lockCompany', 'Lock Company:', 1)
            ->setDescription('(Package: customer can not do anything with company)')
            ->setValue($node->lockCompany);

        $this->addFieldset('Associated Product Image');
        $this->add(CmsImg::class, 'associatedImageId', 'Associated Product Image')
            ->setNodeId(self::ASSOCIATED_IMAGE_PAGE)
            ->setValue($node->associatedImageId);
        $this->addArea('associatedText', 'Associated Product Text:')->cols(40)->rows(5)
            ->setValue($node->associatedText);
        $this->addArea('associatedDescription', 'Associated Product Description:')->cols(40)->rows(5)
            ->setValue($node->associatedDescription);
        $this->addText('associatedIconClass', 'Icon class')
            ->setDescription('(Font awesome class used as product icon (for example "fa-folder-open"))')
            ->setValue($node->associatedIconClass);

        // attachment
        $this->addFieldset('Email Attachments	');
        for ($i = 0; $i < self::ATTACHMENTS_COUNT; ++$i) {
            $this->add(CmsFile::class, 'attachment' . $i, 'Attachment ' . ($i + 1) . ':')
                ->setNodeId(self::ATTACHMENT_FILE_PAGE);
            // set value
            if (isset($node->emailCmsFileAttachments[$i])) {
                $fileId = $node->emailCmsFileAttachments[$i];
                $this['attachment' . $i]->setValue($fileId);
            }
        }

        // upgrade page popup
        $this->addFieldset('Upgrade Page Popup');
        $this->add(CmsFckArea::class, 'upgradeDescription', 'Upgrade Description:')
            ->setSize(500, 200)
            ->setValue($node->upgradeDescription);
        $this->add(CmsImg::class, 'upgradeImageId', 'Upgrade Image')
            ->setNodeId(self::UPDATE_IMAGE_PAGE)
            ->setValue($node->upgradeImageId);

        $this->addFieldset('VO Service');
        $this->addCheckbox('isVoServiceEligible', 'Enabled', 1)
            ->setDescription('(Enabling this will automatically create this service on VO after purchase)')
            ->setValue($node->isVoServiceEligible());
        $this->addSelect('voServiceDurationInMonths', 'Duration:', BasketProduct::$voServiceDurationsInMonths)
            ->setFirstOption('--- Select ---')
            ->addRule([$this, 'Validator_voServiceRequired'], 'Required!', ['isVoServiceEligible' => $this['isVoServiceEligible']])
            ->setValue($node->getVoServiceDurationInMonths());

        $this->addFieldset('Pay by phone');
        $this->addSelect(
            BasketProduct::PROPERTY_AVAILABLE_FOR_PAY_BY_PHONE,
            'Available for purchase by phone?',
            [
                BasketProduct::AVAILABLE_FOR_PAY_BY_PHONE_NOT_AVAILABLE => 'Not available',
                BasketProduct::AVAILABLE_FOR_PAY_BY_PHONE_COMPANY_FORMATION => "Yes, under 'Company Formation Products'",
                BasketProduct::AVAILABLE_FOR_PAY_BY_PHONE_RENEWALS => "Yes, under 'Renewals'",
                BasketProduct::AVAILABLE_FOR_PAY_BY_PHONE_OTHER_PRODUCTS => "Yes, under 'Other products'",
                BasketProduct::AVAILABLE_FOR_PAY_BY_PHONE_ASSOCIATED_PRODUCTS => "Yes, under 'Associated products'",
            ]
        )
            ->setValue($node->getAvailableForPayByPhone());

        $this->addFieldset('Toolkit offers:');

        $types = $this->toolkitOfferRepository->getAvailableOffers();
        $this->add(AsmSelect::class, 'toolkitOfferTypes', 'Toolkit offers:')
            ->title('--- Select --')
            ->setValue($node->getToolkitOfferTypes())
            ->setOptions(ToolkitOfferMapper::formatChoices($types));

        $this->addFieldset('Registration review');
        $this->addCheckbox('registrationReviewEnabled', 'Enabled', 1)
            ->setDescription('(Enabling this will show a possibility to purchase registration review on incorporation summary)')
            ->setValue($node->isRegistrationReviewEnabled());

        // action
        $this->addFieldset('Action');
        $this->addSubmit('submit', 'Submit')
            ->onclick('changes = false')
            ->class('btn');
    }

    /**
     * @return array
     */
    private static function getInfoPagesDropdown()
    {
        static $dropdown = null;
        if ($dropdown === null) {
            $pages = FNode::getChildsNodes(self::PAGES_FOLDER);
            /* @var $page FNode */
            foreach ($pages as $pageId => $page) {
                $dropdown[$pageId] = $page->getLngTitle();
            }
        }

        return $dropdown;
    }
}
