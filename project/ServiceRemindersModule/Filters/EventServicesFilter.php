<?php

namespace ServiceRemindersModule\Filters;

use Entities\Service;
use Models\View\ServiceView;
use ServiceRemindersModule\Deciders\AddressEligibilityDecider;
use ServiceRemindersModule\Entities\Reminder;
use ServiceRemindersModule\Entities\ReminderService;
use Services\EventService;

class EventServicesFilter
{
    /**
     * @var EventService
     */
    private $eventService;

    public function __construct(EventService $eventService)
    {
        $this->eventService = $eventService;
    }

    /**
     * @param string $eventKey
     * @param ReminderService[] $services
     * @return array
     */
    public function filter(string $eventKey, array $services): array
    {
        $filteredServices = [];
        
        
        foreach ($services as $service) {
            if ($this->eventService->eventOccurred($eventKey, $service->getId())) {
                $filteredServices[] = $service;
            }
        }
        
        return $filteredServices;
    }
}