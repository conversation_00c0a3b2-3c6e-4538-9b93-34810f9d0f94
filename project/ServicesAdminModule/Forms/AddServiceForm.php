<?php

namespace ServicesAdminModule\Forms;

use BootstrapModule\Singletons\StaticContainer;
use Entities\Company;
use ServicesAdminModule\Providers\AddServiceFormChoicesProvider;
use ServiceActivatorModule\Validators\NotDowngradeForbidden;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\DateType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class AddServiceForm extends AbstractType
{
    const DURATION_1_MONTH = '+1 month';
    const DURATION_3_MONTHS = '+3 months';
    const DURATION_6_MONTHS = '+6 months';
    const DURATION_9_MONTHS = '+9 months';
    const DURATION_12_MONTHS = '+12 months';

    const REASON_GOODWILL = 'goodwill';
    const REASON_NON_STANDARD = 'nonStandard';

    /**
     * @param FormBuilderInterface $builder
     * @param array $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        if (!empty($options['subscriber']))
            $builder->addEventSubscriber($options['subscriber']);

        $choices = [];
        if (!empty($options['choicesProvider']))
            $choices = array_flip($options['choicesProvider']->getProducts($options['company']));

        $builder
            ->add(
                'productName',
                ChoiceType::class,
                [
                    'label' => 'Service Offer',
                    'choices' => $choices,
                    'placeholder' => '--- Select product ---',
                    'constraints' => [
                        new NotDowngradeForbidden(['company' => $options['company']]),
                    ]
                ]
            )
            ->add(
                'reason',
                ChoiceType::class,
                [
                    'choices' => array_flip([
                        self::REASON_GOODWILL => 'Gesture of Goodwill',
                        self::REASON_NON_STANDARD => 'Non-Standard payment method was used',
                    ]),
                    'expanded' => TRUE,
                ]
            )
            ->add(
                'paymentDate',
                DateType::class,
                [
                    'label' => 'Payment Date',
                    'widget' => 'single_text',
                    'format' => 'yyyy-MM-dd',
                    'invalid_message' => 'Please provide a valid date',
                    'required' => FALSE,
                ]
            )
            ->add(
                'totalAmount',
                NumberType::class,
                [
                    'label' => 'Total Amount',
                    'required' => FALSE
                ]
            )
            ->add(
                'reference',
                TextType::class,
                [
                    'required' => FALSE
                ]
            )
            ->add(
                'previousProductId',
                HiddenType::class,
                [
                    'mapped' => FALSE,
                ]
            )
            ->add(
                'previousStartDate',
                HiddenType::class,
                [
                    'mapped' => FALSE,
                ]
            )
            ->add(
                'add',
                SubmitType::class,
                [
                    'label' => 'Add Service',
                ]
            );
    }

    /**
     * {@inheritdoc}
     */
    public function configureOptions(OptionsResolver $resolver)
    {
        $resolver->setDefaults(
            [
                'validation_groups' => function(FormInterface $form) {
                    /** @var AddServiceFormData $data */
                    $data = $form->getData();

                    return ['Default', $data->getReason()];
                },
                'subscriber' => null,
                'choicesProvider' => null,
                'company' => null,
            ]
        );
    }

    /**
     * @return null
     */
    public function getName()
    {
        return NULL;
    }
}
