<?xml version="1.0"?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="BusinessServicesModule\ApiClient\BusinessServicesApiClient" id="business_services_module.crunch_module.api_client">
            <argument id="business_services_module.http_client" type="service"/>
            <argument id="business_services_module.providers.mediator_provider" type="service"/>
            <argument id="business_services_module.crunch_module.resolvers.response_resolver" type="service"/>
            <argument id="datastore.experiments" type="service"/>
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="business_services_module.serializer" type="service"/>
            <argument id="big_query_module.repositories.big_query_repository" type="service"/>
            <argument id="big_query_module.factories.big_query_factory" type="service"/>
            <argument>%env.environment%</argument>
        </service>

        <service class="BusinessServicesModule\CrunchModule\Commands\SubmitLeadsCommand" id="business_services_module.crunch_module.commands.submit_leads_command">
            <argument id="business_services_module.repositories.lead_repository" type="service"/>
            <argument id="business_services_module.crunch_module.factories.leads_to_process_criteria_factory" type="service" />
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="business_services_module.crunch_module.factories.lead_data_create_factory" type="service"/>
            <argument id="business_services_module.crunch_module.api_client" type="service" />
            <argument id="marketing_module.emailers.company_emailer" type="service" />
            <argument id="business_services_module.crunch_module.providers.email_provider" type="service" />
            <argument id="services.event_service" type="service" />

            <tag name="cron.command" command-name="cron:business_services:crunch:submit_leads" action="sendLeads"/>
        </service>

        <service class="BusinessServicesModule\CrunchModule\Factories\LeadDataCreateFactory" id="business_services_module.crunch_module.factories.lead_data_create_factory"/>

        <service class="BusinessServicesModule\CrunchModule\Factories\LeadsToProcessCriteriaFactory" id="business_services_module.crunch_module.factories.leads_to_process_criteria_factory">
            <argument id="business_services_module.repositories.array_offer_repository" type="service"/>
        </service>

        <service class="BusinessServicesModule\CrunchModule\Resolvers\ResponseResolver" id="business_services_module.crunch_module.resolvers.response_resolver"/>

        <service class="BusinessServicesModule\CrunchModule\Providers\OfferProvider" id="business_services_module.crunch_module.providers.offer_provider">
            <argument id="business_services_module.repositories.array_offer_repository" type="service"/>
            <tag name="business_services_module.offers_provider"/>
        </service>

        <service class="BusinessServicesModule\CrunchModule\Providers\EmailProvider" id="business_services_module.crunch_module.providers.email_provider">
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
        </service>

    </services>
</container>