<?php


namespace BusinessServicesModule\MettleModule\Facades;

use HttpClient\ClientInterface;
use HttpClient\Exceptions\RequestException;
use HttpClient\Requests\JsonRequestBody;
use HttpClient\Requests\Request;
use HttpClient\Requests\RequestInterface;
use HttpClient\Responses\ResponseInterface;
use HttpClient\Responses\Response;

class SubmitLeadsFacade
{
    public const ENDPOINT = 'company-leads/partners/madesimple/leads';

    /**
     * @var string
     */
    private $token;

    /**
     * @var ClientInterface
     */
    private $httpClient;

    public function __construct(string $token, ClientInterface $httpClient)
    {
        $this->token = $token;
        $this->httpClient = $httpClient;
    }

    /**
     * @throws RequestException
     */
    public function sendlead(array $data): ResponseInterface
    {
        return $this->httpClient->sendRequest(
            $this->createRequest($data)
        );
    }

    private function createRequest(array $data): Request
    {
        $header = [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => sprintf('Bearer %s', $this->token)
        ];

        return new Request(
            self::ENDPOINT,
            RequestInterface::POST,
            $header,
            new JsonRequestBody($data)
        );
    }

}