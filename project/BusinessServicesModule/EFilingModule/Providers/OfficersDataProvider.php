<?php

namespace BusinessServicesModule\EFilingModule\Providers;

use Entities\CompanyHouse\FormSubmission\CompanyIncorporation;
use Entities\CompanyHouse\FormSubmission\CompanyIncorporation\Member;

class OfficersDataProvider
{
    /**
     * @param CompanyIncorporation $incorporation
     * @return array
     */
    public function getData(CompanyIncorporation $incorporation): array
    {
        $officers = [];

        foreach ($this->getOfficers($incorporation) as $officer) {
            $officers[] = $this->getOfficerData($officer);
        }

        return $officers;
    }

    /**
     * @param CompanyIncorporation $incorporation
     * @return Member[]
     */
    private function getOfficers(CompanyIncorporation $incorporation): array
    {
        return array_merge(
            $incorporation->getAppointments()->toArray(),
            $incorporation->getPscs()->toArray(),
            $incorporation->getSubscribers()->toArray()
        );
    }

    /**
     * @param Member $officer
     * @return array
     */
    private function getOfficerData(Member $officer): array
    {
        return [
            'name' => $officer->getFullName(),
            'position' => $officer->getTypeName(),
            'addr1' => $officer->getPremise(),
            'addr2' => $officer->getStreet(),
            'country' => $officer->getCountry(),
            'locality' => $officer->getCounty(),
            'postcode' => $officer->getPostcode(),
            'isCorporate' => $officer->isCorporate(),
            'isNominee' => $officer->getNominee()
        ];
    }
}
