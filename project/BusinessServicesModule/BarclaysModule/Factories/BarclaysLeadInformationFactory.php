<?php

declare(strict_types=1);

namespace BusinessServicesModule\BarclaysModule\Factories;

use BusinessServicesModule\BarclaysModule\Dto\BarclaysLeadInformation;
use BusinessServicesModule\BarclaysModule\Services\BarclaysLeadService;
use Entities\Company;
use Services\EventService;

readonly class BarclaysLeadInformationFactory
{
    public function __construct(
        private BarclaysLeadService $barclaysLeadService,
        private EventService $eventService,
    ) {}

    public function create(Company $company): BarclaysLeadInformation
    {
        return new BarclaysLeadInformation(
            $company->getId(),
            $company->isBasicPackage(),
            $this->barclaysLeadService->hasLead($company),
            $this->barclaysLeadService->isEligible($company),
            $this->eventService->eventOccurred(BarclaysLeadInformation::BARCLAYS_LEAD_REFUSED_BY_CUSTOMER_EVENT, $company->getId()),
        );
    }
}
