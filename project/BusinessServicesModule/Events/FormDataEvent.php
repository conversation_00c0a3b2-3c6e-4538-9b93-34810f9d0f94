<?php

namespace BusinessServicesModule\Events;

use BusinessServicesModule\Dto\AdditionalInformationData;
use Symfony\Contracts\EventDispatcher\Event;

class FormDataEvent extends Event
{
    /**
     * @var AdditionalInformationData
     */
    private $data;

    public function __construct(AdditionalInformationData $data)
    {
        $this->data = $data;
    }

    public function getData(): AdditionalInformationData
    {
        return $this->data;
    }
}
