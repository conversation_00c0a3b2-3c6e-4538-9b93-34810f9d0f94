<?php

namespace LoginModule\Authenticators;

use Models\OldModels\Customer;
use Models\OldModels\CustomerActionLogger;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use UserModule\Contracts\ICustomer;
use UserModule\Domain\Credentials;
use UserModule\Events\AuthenticationEvent;
use UserModule\Services\IAuthenticator;
use UserModule\Services\ITokenAuthenticator;

class OneTimePasswordAuthenticator implements IAuthenticator, ITokenAuthenticator
{
    /**
     * @var EventDispatcherInterface
     */
    private $dispatcher;

    public function __construct(EventDispatcherInterface $dispatcher)
    {
        $this->dispatcher = $dispatcher;
    }

    public function authenticate(Credentials $credentials, bool $isFirebase = false): ?ICustomer
    {
        $token = $credentials->getToken();
        if ($token === NULL || !$token->isValid()) {
            return NULL;
        }
        
        if (Customer::isSignedIn()) {
            Customer::signOut();
        }

        $customer = $token->getCustomer();
        Customer::signIn($customer->getId());
        CustomerActionLogger::logEntity($customer, CustomerActionLogger::LOG_IN_OTP);
        $this->dispatcher->dispatch(
            new AuthenticationEvent($customer, Credentials::CONTEXT_ONE_TIME_LOGIN),
            'authentication.login'
        );

        return $customer;
    }

    public function useCredentials(Credentials $credentials): void
    {
    }

    public function destroy(): void
    {
        Customer::signOut();
    }
}
