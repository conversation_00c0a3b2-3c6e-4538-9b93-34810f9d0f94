{extends '@structure/layout.tpl'}

{block content}
    <div class="width100 bg-white padcard-mobile" style="border-bottom: 2px solid #ddd;">
        <div class="container">
            {if $hasToShowProgressBar}
                <div class="row">
                    <div class="col-md-12">
                        <ul class="progress-indicator">
                            <li class="completed"><span>Search</span><span class="bubble"></span></li>
                            <li class="completed"><span>Select</span><span class="bubble"></span></li>
                            <li class="active"><span>Buy</span><span class="bubble"></span></li>
                            <li><span>Complete</span><span class="bubble"></span></li>
                        </ul>
                    </div>
                </div>
            {/if}
            {include 'NotificationModule/Templates/oldNotifications.tpl' items = $flashMessageNotifications}
            <div class="row">
                <div class="col-xs-12">
                    <h1>Create an account</h1>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12 col-md-8">
                    <div class="brick-desktop brick-desktop--lg btm20">
                        <p>To proceed, you only need to provide an email address and a password.</p>

                        {$formHelper->setTheme($form,['horizontal.html.twig','labels/size_3.html.twig'])}
                        {$formHelper->start($form, ['attr' => ['id' => 'new_account_form', 'novalidate' => 'novalidate']]) nofilter}

                        <div class="form-group {if !$form['email']->vars['valid']}has-error{/if}">
                            {$formHelper->label($form['email']) nofilter}
                            <div class="col-sm-9">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-user fa-fw"></i></span>
                                    {$formHelper->widget($form['email']) nofilter}
                                </div>
                            </div>
                            <div class="col-sm-offset-3 col-sm-9">
                                {$formHelper->errors($form['email']) nofilter}
                            </div>
                        </div>

                        <div class="col-xs-12 col-sm-offset-3 col-sm-9">
                            <div class="row">
                                {ui name="mailcheck_notification_form" emailFieldId="new_account_form_email" submitButtonName="register" customClasses="btm20"}
                            </div>
                        </div>

                        <div class="form-group {if !$form['password']->vars['valid']}has-error{/if}">
                            {$formHelper->label($form['password']) nofilter}
                            <div class="col-sm-9">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-lock fa-fw"></i></span>
                                    {$formHelper->widget($form['password']) nofilter}
                                </div>
                            </div>
                            <div class="col-sm-offset-3 col-sm-9">
                                {$formHelper->errors($form['password']) nofilter}
                            </div>
                        </div>

                        <div class="login-footer form-group">
                            <div class="col-xs-12 col-sm-offset-3 col-sm-9">
                                <div class="checkbox">
                                    <label><input type="checkbox" id="show-password">Show password</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-xs-12 col-sm-offset-3 col-sm-9">
                                <button class="btn btn-lg btn-default" type="submit" name="register"
                                        rv-on-click="msgMailChecker.suggestEmail"
                                        rv-disabled="msgMailChecker.hasSuggestions">
                                    Create account
                                </button>
                            </div>
                        </div>
                        <div class="form-group margin0">
                            <div class="col-xs-12 col-sm-offset-3 col-sm-9">
                                <p class="small">
                                    By creating an account you agree with our <a href="/privacy-policy.html">Privacy
                                        Policy</a> and <a href="/general-terms-conditions.html">Terms of Use</a>.
                                </p>
                            </div>
                        </div>

                        {$formHelper->end($form) nofilter}
                    </div>
                </div>
            </div>
            {feature type='simple_login'}
                <div class="row">
                    <div class="col-xs-12 col-md-8">
                        {include file="UserModule/Templates/customer-firebase-login.tpl" className="brick-desktop brick-desktop--lg btm20" }
                    </div>
                </div>
            {/feature}
            <div class="row">
                <div class="col-xs-12 col-md-8">
                    <div class="brick-desktop brick-desktop--lg btm20">
                        <h3 class="top0">Already have an account?</h3>
                        <a href="{url route='login'}" class="btn btn-secondary btn-lg">Log in</a>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="devModal" tabindex="-1" role="dialog" aria-labelledby="devModalLabel" style="z-index: 100000;">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                        aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="devModalLabel">dev.companiesmadesimple.com</h4>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info"> Hey pal, you're on DEV! You need to use an
                                @madesimplegroup.com email (even if it's fake), otherwise it won't work.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        {feature type='simple_login'}
        {if $failLogin}
        Login.LoginController.firebaseCreate(true);
        {else}
        Login.LoginController.firebaseCreate()
        {/if}

        {/feature}
        var formId = '#new_account_form';
        Login.LoginController.bind(formId, '#show-password');
        rivets.bind($(formId), { msgMailChecker: msgMailChecker });

        $(document).ready(function() {
            if (
                $(location).attr('hostname') === "dev.companiesmadesimple.com"
//                || $(location).attr('hostname') === "dev.cms"
            ) {
                $("button[name='register']").click(function(event) {
                    var value = $("#new_account_form_email").val();
                    if (value != '' && value.indexOf('@madesimplegroup.com') == -1) {
                        event.preventDefault();
                        $("#devModal").modal('show');
                    }
                });
            }
        });
    </script>
{/block}
