<?php

namespace ServiceModule\Services;

use Config\Constants\EventLocator;
use Dispatcher\Events\OrderEvent;
use Dispatcher\Events\PaymentEvent;
use OmnipayModule\ApiClient\OmnipayApiClient;
use OmnipayModule\Dto\OmnipayPaymentResponse;
use OmnipayModule\Entities\OmnipayPaymentResponseDetails\OmnipayPaymentResponseDetails;
use PaymentModule\Contracts\IPaymentResponse;
use ServiceModule\Dto\CustomerRenewalData;
use ServiceModule\Facades\SagepayOpayoCommonFacade;
use Services\Emailers\PaymentEmailer;
use Services\ProductService;
use Services\TransactionService;
use Symfony\Component\EventDispatcher\EventDispatcher;

class OmnipayAutoRenewalService
{
    public function __construct(
        private OmnipayApiClient $omnipayApiClient,
        private EventDispatcher $dispatcher,
        private ProductService $productService,
        private PaymentEmailer $paymentEmailer,
        private SagepayOpayoCommonFacade $sagepayOpayoCommonFacade,
        private RenewalsService $renewalsService,
        private TransactionService $transactionService,
    ) {
    }

    public function processSuccessfulPayment(
        OmnipayPaymentResponse $response,
        CustomerRenewalData $customerRenewalData,
    ): void {
        if ($response->isStripe()) {
            $this->processSuccessfulStripePayment(
                $response->getOriginalResponse(),
                $customerRenewalData,
            );
        }

        if ($response->isOpayo()) {
            $this->processSuccessfulOpayoPayment(
                $response->getOriginalResponse(),
                $customerRenewalData,
            );
        }
    }

    public function processFailedRenewal(CustomerRenewalData $customerRenewalData): void
    {
        foreach ($customerRenewalData->getCompanies() as $company) {

            $services = $customerRenewalData->getEmailServicesByCompany($company);

            if (empty($services)) {
                continue;
            }

            $this->renewalsService->sendFailedRenewalEmail(
                $customerRenewalData->getCustomer(),
                $company,
                $customerRenewalData->getEmailServicesByCompany($company),
            );
        }
    }

    private function processSuccessfulStripePayment(
        OmnipayPaymentResponseDetails $paymentResponse,
        CustomerRenewalData $customerRenewalData,
    ): void {
        $customer = $customerRenewalData->getCustomer();
        $basket = $customerRenewalData->getBasket();
        $order = $customerRenewalData->getOrder();
        $services = $customerRenewalData->getServices();
        $newServices = $customerRenewalData->getNewServices();

        $transaction = $this->transactionService->saveTransactionFromOmnipayPayment($customer, $paymentResponse, $order);
        $this->omnipayApiClient->saveOrder($paymentResponse->getId(), $order->getId(), $transaction->getId());

        $this->productService->saveServices($newServices);
        $this->renewalsService->processServices($customer, $newServices);

        $this->paymentEmailer->orderConfirmationInternalEmail($customer, $basket, $order->getId());

        $this->dispatcher->dispatch(
            new PaymentEvent($customer, $basket),
            EventLocator::PAYMENT_RECURRING_COMPLETED
        );
        $this->dispatcher->dispatch(new OrderEvent($order), 'events.order.completed');

        $this->renewalsService->notifyServices(
            $services,
            EventLocator::COMPANY_SERVICES_AUTO_RENEWAL_SUCCEDED
        );

        foreach ($customerRenewalData->getCompanies() as $company) {
            $this->renewalsService->sendSuccessfulRenewalEmail(
                $customer,
                $company,
                $customerRenewalData->getServicesByCompany($company)
            );
        }
    }

    private function processSuccessfulOpayoPayment(
        IPaymentResponse $paymentResponse,
        CustomerRenewalData $customerRenewalData,
    ): void {
        $this->sagepayOpayoCommonFacade->handlePostPaymentOperations(
            $customerRenewalData->getCustomer(),
            $customerRenewalData->getBasket(),
            $customerRenewalData->getOrder(),
            $paymentResponse,
            $customerRenewalData
        );

        $this->productService->saveServices($customerRenewalData->getNewServices());
        $this->renewalsService->processServices(
            $customerRenewalData->getCustomer(),
            $customerRenewalData->getNewServices()
        );
    }
}
