<?php

namespace CompanyFormationModule\Dto;

class Step
{
    public const STEP_MAPPING = [
        'welcome' => 'Welcome',
        'company_formation' => 'Company Details',
        'appointments' => 'Company Appointments',
        'business_services' => 'Business Services',
        'call_and_messages' => 'Call & Messages',
        'memorandum_and_articles' => 'M&A',
        'memorandum_and_articles_exception' => 'M&A',
        'about_yourself' => 'Account Holder Information',
        'summary' => 'Submission Review',
    ];

    private const ROUTE_MAPPING = [
        self::STEP_MAPPING['welcome'] => 'company_incorporation_module.welcome',
        self::STEP_MAPPING['company_formation'] => 'company_incorporation_module.company_formation',
        self::STEP_MAPPING['appointments'] => 'company_incorporation_module.appointments',
        self::STEP_MAPPING['business_services'] => 'company_incorporation_module.business_services',
        self::STEP_MAPPING['call_and_messages'] => 'company_incorporation_module.call_and_messages',
        self::STEP_MAPPING['memorandum_and_articles'] => 'company_incorporation_module.memorandum-and-articles',
        self::STEP_MAPPING['about_yourself'] => 'company_incorporation_module.about_yourself',
        self::STEP_MAPPING['summary'] => 'company_incorporation_module.summary',
    ];

    public function __construct(
        private readonly string $title,
        private readonly string $url,
        private readonly bool $hidden,
        private readonly bool $valid,
        private readonly bool $enabled,
        private readonly bool $blocked,
        private readonly array $invalidConditions,
        private readonly array $subSteps,
        private readonly bool $showOnWizard = true
    ) {
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getUrl(): string
    {
        return $this->url;
    }

    public function isValid(): bool
    {
        return $this->valid;
    }

    public function isHidden(): bool
    {
        return $this->hidden;
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function isBlocked(): bool
    {
        return $this->blocked;
    }

    public function isAccessible(): bool
    {
        return $this->isEnabled() && !$this->isBlocked();
    }

    public function getInvalidConditions(): array
    {
        return $this->invalidConditions;
    }

    public function getSubSteps(): array
    {
        return $this->subSteps;
    }

    public function getSubStepsTitles(): array
    {
        return array_map(function($entry) {
            return $entry->getTitle();
        }, $this->subSteps);
    }

    public function isShowOnWizard(): bool
    {
        return $this->showOnWizard;
    }

    public function getRoute(): string
    {
        return self::ROUTE_MAPPING[$this->getTitle()];
    }

    public function getKey(): string
    {
        return array_search($this->getTitle(), self::STEP_MAPPING);
    }
}
