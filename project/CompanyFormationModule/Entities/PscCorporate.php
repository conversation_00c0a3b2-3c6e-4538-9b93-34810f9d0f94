<?php

namespace CompanyFormationModule\Entities;

use CompaniesHouseModule\Entities\Address;
use CompaniesHouseModule\Entities\CorporateDetails;
use CompaniesHouseModule\Entities\ICorporateDetails;
use CompaniesHouseModule\Entities\NatureOfControl;
use Doctrine\ORM\Mapping as Orm;

/**
 * @Orm\Entity
 */
class PscCorporate extends Member implements ICorporateDetails, IPsc
{
    /**
     * @var CorporateDetails
     * @Orm\Embedded(columnPrefix=false, class="CompaniesHouseModule\Entities\CorporateDetails")
     */
    private $corporateDetails;

    /**
     * @var NatureOfControl
     * @Orm\Embedded(columnPrefix=false, class="CompaniesHouseModule\Entities\NatureOfControl")
     */
    private $natureOfControl;

    protected $type = 'PSC';

    protected $corporate = 1;

    public function __construct(
        CorporateDetails $corporateDetails,
        Address $address,
        NatureOfControl $natureOfControl
    ) {
        $this->corporateDetails = $corporateDetails;
        $this->natureOfControl = $natureOfControl;
        $this->address = $address;
    }

    public function getCorporateDetails(): CorporateDetails
    {
        return $this->corporateDetails;
    }

    public function setCorporateDetails(CorporateDetails $corporateDetails)
    {
        $this->corporateDetails = $corporateDetails;
    }

    public function getNatureOfControl(): NatureOfControl
    {
        return $this->natureOfControl;
    }

    public function setNatureOfControl(NatureOfControl $natureOfControl): IPsc
    {
        $this->natureOfControl = $natureOfControl;
        return $this;
    }

    public function getName(): string
    {
        return $this->corporateDetails->getCorporateName();
    }

    /**
     * @return string
     */
    public function getCorporateName(): string
    {
        return (string)$this->corporateDetails->getCorporateName();
    }

    /**
     * @return string
     */
    public function getPostcode(): string
    {
        return $this->address->getPostcode();
    }
}
