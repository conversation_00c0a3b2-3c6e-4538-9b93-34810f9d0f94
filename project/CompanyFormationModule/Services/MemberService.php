<?php

namespace CompanyFormationModule\Services;

use CompanyFormationModule\Entities\IMember;
use CompanyFormationModule\Entities\Member;
use CompanyFormationModule\Repositories\MemberRepository;
use Entities\Company;
use Entities\CompanyHouse\Helper\Authentication;
use UserModule\Contracts\ICustomer;

class MemberService
{
    /**
     * @var MemberRepository
     */
    private $repository;

    public function __construct(MemberRepository $repository)
    {
        $this->repository = $repository;
    }

    public function optionalMember(int $memberId): ?Member
    {
        return $this->repository->getMemberById($memberId);
    }

    public function optionalCustomerMember(ICustomer $customer, int $memberId): ?Member
    {
        $entity = $this->repository->getMemberById($memberId);
        if (!$entity) {
            return NULL;
        }

        $formSubmission = $entity->getFormSubmission();

        $company = $formSubmission->getCompany();

        $companyCustomer = $company->getCustomer();

        if (!$companyCustomer->isEqual($customer)) {
            return NULL;
        }

        return $entity;
    }

    public function getCompanyMembers(Company $company): array
    {
        return $this->repository->getCompanyMembers($company);
    }

    public function getConsolidatedMembers(Company $company): array
    {
        return $this->repository->getConsolidatedMembers($company);
    }

    public function deleteMember(IMember $member): void
    {
        $this->repository->removeEntity($member);
    }

    public function getMemberDetailsFromCustomerOtherCompanies(Company $company, string $memberName, bool $isCorporate): array
    {
        return $this->repository->getMemberDetailsFromCustomerOtherCompanies($company, $memberName, $isCorporate);
    }

    public function getPreviousShareholderAuthentication(Company $company): ?Authentication
    {
        $result = $this->repository->getPreviousShareholderAuthentication($company);
        return $result['authentication'] ?? NULL;
    }

    public function companyHasShareholders(Company $company): bool
    {
        return !empty($this->repository->getShareholders($company));
    }
}