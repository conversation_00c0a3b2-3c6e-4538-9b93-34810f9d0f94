<?php

namespace Factories;

use Entities\Customer;
use Entities\Service;
use Exception;
use LoggableModule\Services\LoggableService;
use Models\View\ServiceView;
use OmnipayModule\Providers\OmnipayCardProvider;
use Repositories\ServiceSettingsRepository;
use ServiceModule\Deciders\LatePaymentFeeDecider;
use Services\ServiceService;

class ServiceViewFactory
{
    /**
     * @var ServiceSettingsRepository
     */
    private $serviceSettingsRepository;

    /**
     * @var LatePaymentFeeDecider
     */
    private $latePaymentFeeDecider;

    /**
     * @var OmnipayCardProvider
     */
    private $omnipayCardProvider;

    /**
     * @var ServiceService
     */
    private $serviceService;

    /**
     * @var LoggableService
     */
    private $loggableService;

    /**
     * @param ServiceSettingsRepository $serviceSettingsRepository
     */
    public function __construct(
        ServiceSettingsRepository $serviceSettingsRepository,
        LatePaymentFeeDecider $latePaymentFeeDecider,
        OmnipayCardProvider $omnipayCardProvider,
        ServiceService $serviceService,
        LoggableService $loggableService = null,
    )
    {
        $this->serviceSettingsRepository = $serviceSettingsRepository;
        $this->latePaymentFeeDecider = $latePaymentFeeDecider;
        $this->omnipayCardProvider = $omnipayCardProvider;
        $this->serviceService = $serviceService;
        $this->loggableService = $loggableService;
    }

    /**
     * @param array $services
     * @param bool $isIdValid
     * @return ServiceView
     */
    public function create(array $services, bool $isIdValid = null)
    {
        $activePaymentMethod = null;

        try {
            if (!empty($services) && isset($services[0])) {
                /** @var Service $service */
                $service = $services[0];

                if ($customer = $service->getCustomer())
                    $activePaymentMethod = $this->getActivePaymentMethod($customer);
            }
        } catch (Exception $e) {
            // Keep $activePaymentMethod as null
        }

        return new ServiceView(
            $services,
            $this->serviceSettingsRepository,
            $this->latePaymentFeeDecider,
            $this->serviceService,
            $activePaymentMethod,
            $isIdValid,
            $this->loggableService
        );
    }

    private function getActivePaymentMethod(Customer $customer)
    {
        if ($customer->isOmnipay()) {
            return $this->omnipayCardProvider->getActivePaymentMethod($customer->getId());
        }

        return $customer->getActiveToken();
    }
}
