<?php

declare(strict_types=1);

namespace OrderModule\DataAccessors\MySQL;

use Doctrine\DBAL\Exception;
use Entities\Customer;
use Entities\Order as OrderEntity;
use OrderModule\Dto\Order;
use OrderModule\Dto\Orders;
use OrderModule\DataAccessors\OrderDataAccessorInterface;
use OrderModule\Transformers\Order as OrderTransformer;
use Repositories\OrderRepository;

readonly class OrderDataAccessor implements OrderDataAccessorInterface
{
    use ExceptionTrait;

    public function __construct(
        private OrderTransformer $transformer,
        private OrderRepository $repository,
    ) {
    }

    public function create(Order $order): Order
    {
        return $this->persist(
            (new OrderEntity($order->customer))
            ->setRealSubTotal($order->realSubTotal)
            ->setDiscount($order->discount)
            ->setVat($order->vat)
            ->setCredit($order->credit)
            ->setTotal($order->total)
            ->setCustomerName($order->customerName)
            ->setCustomerAddress($order->customerAddress)
            ->setCustomerPhone($order->customerPhone)
            ->setCustomerEmail($order->customerEmail)
            ->setVoucherId($order->voucherId)
            ->setVoucherName($order->voucherName)
            ->setDescription($order->description)
            ->setStatusId($order->statusId->value)
            ->setIsRefunded($order->isRefunded)
            ->setRefundValue($order->refundValue)
            ->setRefundCreditValue($order->refundCreditValue)
            ->setRefundCustomerSupportLogin($order->refundCustomerSupportLogin)
            ->setPaymentMediumId($order->paymentMediumId->value)
            ->setAgent($order->agent)
            ->setDtc(new \DateTime())
            ->setDtm(new \DateTime())
        );
    }

    public function findById(int $id): ?Order
    {
        return $this->processResult(
            $this->repository->find($id)
        );
    }

    private function persist(OrderEntity $entity): Order
    {
        try {
            $this->repository->saveEntity($entity);
        } catch (\Throwable $e) {
            $this->throwException($e, self::class);
        }

        return $this->transformer->makeDTO($entity);
    }

    private function processResult(?OrderEntity $entity): ?Order
    {
        return $entity ? $this->transformer->makeDTO($entity) : null;
    }

    private function processResults(?array $entities): ?Orders
    {
        return !empty($entities) ? $this->transformer->makeDTOs($entities) : null;
    }

    public function findByCustomer(Customer $customer): ?Orders
    {
        return $this->processResults(
            $this->repository->findBy(['customer' => $customer->getId()])
        );
    }

    /**
     * @throws Exception
     */
    public function getFailedOrdersPaidWithDirectDebit(): ?Orders
    {
        return $this->processResults(
            $this->repository->getDirectDebitOrdersWithOnlyFailedTransactions()
        );
    }
}
