{extends '@structure/layout.tpl'}

{block content}
    <div class="width100 bg-white padcard">
        <div class="container">
            <div class="row btm20">
                <div class="col-xs-12 tax-assist-alert">
                     {include 'NotificationModule/Templates/oldNotifications.tpl' items = $flashMessageNotifications}
                </div>
                <div class="col-xs-12{if isset($introductionStrip.imgFeature)} col-md-7{/if}">
                    <h1>{$introductionStrip.title nofilter}</h1>
                </div>
                {if isset($introductionStrip.imgFeature)}
                    <div class="col-xs-12 col-md-4 pad8 hidden-xs">
                        <img class="img-responsive center-block" src="{$introductionStrip.imgFeature nofilter}" alt="{$introductionStrip.imgFeatureDescription nofilter}">
                    </div>
                {/if}
            </div>

            <div class="row">
                <div class="col-xs-12 col-md-7">
                    <p class="lead">{$introductionStrip.lead nofilter}</p>
                    <div class="top30">
                        {foreach $introductionStrip.copy as $copy}
                            <div class="media">
                                <div class="media-left">
                                    <i class="fa {$copy.icon nofilter} fa-2x fa-fw blue" aria-hidden="true"></i>
                                </div>
                                <div class="media-body">
                                    <h4 class="media-heading">{$copy.title nofilter}</h4>
                                    <p>{$copy.description nofilter}</p>
                                </div>
                            </div>
                        {/foreach}
                    </div>
                </div>
                <div class="col-xs-12 col-md-5 top20 btm20" id="tax-assist-contact-block">
                    <div class="bordercell padcard">
                        <p class="text-center btm20">{$formStrip.title nofilter}</p>
                        <p class="text-center btm20">{$formStrip.description nofilter}</p>
                        {$formHelper->setTheme($form, ['horizontal.html.twig', 'labels/required.html.twig', 'labels/size_4.html.twig'])}
                        {$formHelper->start($form,['attr' => ['id' => 'tax_assist_form']]) nofilter}
                        {$formHelper->errors($form) nofilter}
                        {$formHelper->row($form['firstName']) nofilter}
                        {$formHelper->row($form['lastName']) nofilter}
                        {$formHelper->row($form['email']) nofilter}
                        {$formHelper->row($form['phone']) nofilter}
                        {$formHelper->row($form['postcode']) nofilter}
                        {$formHelper->row($form['companyName']) nofilter}
                        <div class="form-group">
                            <div class="col-sm-offset-4 col-sm-8">
                                <button type="submit" class="btn btn-default">Submit</button>
                            </div>
                        </div>
                        {$formHelper->end($form) nofilter}
                        <script type="text/javascript">
                            msgValidation('#tax_assist_form');
                        </script>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="width100 bg-grey1 padcard">
        <div class="container">
            <div class="row">
                <div class="col-xs-12">
                    <h3>{$howItWorks.title nofilter}</h3>
                    {$howItWorks.description nofilter}
                </div>
            </div>
        </div>
    </div>

{/block}
