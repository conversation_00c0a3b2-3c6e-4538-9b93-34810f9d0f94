<?php

namespace Models\View;

use AdminModule\Controlers\OrdersAdminControler;
use AllowDynamicProperties;
use CompanyModule\Contracts\IService;
use DateTime;
use Entities\Payment\Token;
use Entities\Service;
use FeatureModule\Feature;
use Framework\FUser;
use LoggableModule\Services\LoggableService;
use OmnipayModule\Entities\OmnipayCardResponseDetails\OmnipayCard;
use Services\ServiceService;
use Utils\NetteSmartObject;
use function FunctionalModule\Monads\Maybe\maybeInstance;
use function FunctionalModule\Transformations\toCallable;
use Models\Products\Product;
use Repositories\ServiceSettingsRepository;
use ServiceModule\Deciders\LatePaymentFeeDecider;
use UserModule\Contracts\ICustomer;
use function Widmogrod\Functional\invoke;
use function Widmogrod\Monad\Maybe\just;
use function Widmogrod\Monad\Maybe\maybeNull;
use Legacy\Nette\Web\Html;
use Framework\FApplication;

class ServiceView extends NetteSmartObject implements IService
{
    const ALLOWED_EDITING_PERIOD = '18 months';

    /**
     * @var Service[]
     */
    private $services = [];

    /**
     * @var ServiceSettingsRepository
     */
    private $serviceSettingsRepository;

    /**
     * @var LatePaymentFeeDecider
     */
    private $latePaymentFeeDecider;

    /**
     * @var LoggableService|null
     */
    private $loggableService;

    /**
     * @var ServiceService
     */
    private $serviceService;

    /**
     * @var Token|null
     */
    private $activePaymentMethod;

    /**
     * @var bool
     */
    private $companyIdValid;

    /**
     * @TODO it should be a simple object without external dependencies ?
     * Just provide services with same serviceTypeId
     */
    public function __construct(
        array $services,
        ServiceSettingsRepository $serviceSettingsRepository,
        LatePaymentFeeDecider $latePaymentFeeDecider,
        ?ServiceService $serviceService = null,
        ?Token $activePaymentMethod = null,
        ?bool $companyIdValid = null,
        ?LoggableService $loggableService = null
    )
    {
        $this->services = $services;
        $this->serviceSettingsRepository = $serviceSettingsRepository;
        $this->latePaymentFeeDecider = $latePaymentFeeDecider;
        $this->serviceService = $serviceService;
        $this->activePaymentMethod = $activePaymentMethod;
        $this->companyIdValid = $companyIdValid;
        $this->loggableService = $loggableService;
    }

    /**
     * @return NULL|int
     */
    public function getId()
    {
        $service = $this->getLatest();

        return $service ? $service->getId() : NULL;
    }

    /**
     * @return bool
     */
    public function isCancelled()
    {
        $service = $this->getLatest();
        if (!$service) {
            return FALSE;
        }

        $setting = $this->serviceSettingsRepository->getSettingsByService($service);
        if (!$setting) {
            return FALSE;
        }

        return $setting->isServiceCancelled();
    }

    public function getLatestChange(string $property): ?string
    {
        return $this->loggableService->getLatestPropertyChangeString(Service::class, $this->getId(), $property);
    }


    /**
     * @return bool
     */
    public function isOnHold()
    {
        $now = new DateTime();
        $dtStart = $this->getDtStart();

        return !$dtStart || $dtStart > $now;
    }

    /**
     * @return bool
     */
    public function isActive()
    {
        return $this->isActiveOn(new DateTime);
    }

    /**
     * @param DateTime $date
     * @return bool
     */
    public function isActiveOn(DateTime $date)
    {
        $activeEnd = clone $date;
        $activeEnd->modify('-28 days');

        $dtStart = $this->getDtStart();
        $dtExpires = $this->getDtExpires();
        if ($this->isOneOff()) {
            $active = $dtStart && $dtStart <= $date;

            return $dtExpires ? $dtExpires > $date && $active : $active;
        }

        return $dtStart && $dtExpires && $dtStart <= $date && $dtExpires >= $activeEnd;
    }

    public function isOnHoldOn(DateTime $date): bool
    {
        if (!$this->hasDates()) {
            return !($this->isOneOff() && !empty($this->getDtStart()));
        }

        return $this->getDtStart() > $date && $this->getDtExpires() > $date;
    }

    /**
     * @return bool
     */
    public function isDue()
    {
        if ($this->isOneOff()) {
            return FALSE;
        }
        $now = new DateTime();
        $dueEnd = new DateTime('+28 days');
        $dueEnd->setTime(23, 59, 59);
        $dtExpires = $this->getDtExpires();

        return $this->isActive() && $dtExpires >= $now && $dtExpires <= $dueEnd;
    }

    /**
     * @return int|NULL
     */
    public function getDaysToExpire()
    {
        $now = new DateTime();
        $dtExpires = $this->getDtExpires();

        return $dtExpires ? $dtExpires->diff($now)->format("%a") : NULL;
    }

    /**
     * @return bool
     */
    public function isOverdue()
    {
        if ($this->isOneOff()) {
            return FALSE;
        }
        $now = new DateTime();
        $overdueEnd = new DateTime('-28 days');
        $dtExpires = $this->getDtExpires();

        return $this->isActive() && $dtExpires < $now && $dtExpires >= $overdueEnd;
    }

    /**
     * @return bool
     */
    public function hasActiveNonOverdueServiceOfTheSameType()
    {
        $services =  $this->getService()->getCompany()->getServicesByType($this->getService()->getServiceTypeId());
        $now = new DateTime;

        foreach ($services as $service) {
            if ($service->getDtStart() <= $now && $service->getDtExpires() >= $now) {
                return TRUE;
            }
        }

        return FALSE;
    }

    /**
     * @return bool
     */
    public function isExpired()
    {
        return $this->isExpiredOn(new DateTime);
    }

    /**
     * @param DateTime $date
     * @return bool
     */
    public function isExpiredOn(DateTime $date)
    {
        $suspendStart = clone $date;
        $suspendStart->modify('-28 days');
        $dtExpires = $this->getDtExpires();

        if ($this->isOneOff()) {
            return $dtExpires && $dtExpires < new DateTime();
        }

        return $dtExpires && $dtExpires < $suspendStart;
    }

    public function isExpiringAfter(DateTime $date): bool
    {
        return $this->hasDates() && ($this->isActiveOn($date) || $this->isOnHoldOn($date));
    }

    /**
     * @return bool
     */
    public function isGraceExpired()
    {
        return $this->isGraceExpiredOn(new DateTime);
    }

    /**
     * @param DateTime $date
     * @return bool
     */
    public function isGraceExpiredOn(DateTime $date)
    {
        $suspendStart = clone $date;
        $suspendStart->modify('-42 days');
        $dtExpires = $this->getDtExpires();

        if ($this->isOneOff()) {
            return $dtExpires && $dtExpires < new DateTime();
        }

        return $dtExpires && $dtExpires < $suspendStart;
    }

    /**
     * @param DateTime $date
     * @return bool
     */
    public function isOverdueOn(DateTime $date)
    {
        if ($this->isOneOff()) {
            return FALSE;
        }

        return $this->isActiveOn($date) && $date >= $this->getDtExpires();
    }

    /**
     * @return bool
     */
    public function canToggleAutoRenewal()
    {
        return $this->canToggleAutoRenewalOn(new DateTime);
    }

    /**
     * @param DateTime $date
     * @return bool
     */
    public function canToggleAutoRenewalOn(DateTime $date)
    {
        return $this->isEnabled() && $this->isActiveOn($date) && !$this->isLastDayActiveOn($date);
    }

    /**
     * @return bool
     */
    public function isLastDayActive()
    {
        return $this->isLastDayActiveOn(new DateTime);
    }

    /**
     * @param DateTime $date
     * @return bool
     */
    public function isLastDayActiveOn(DateTime $date)
    {
        $expiredDay = clone $date;
        $expiredDay->modify('+1 day');

        return $this->isActiveOn($date) && $this->isExpiredOn($expiredDay);
    }

    /**
     * @return bool
     */
    public function expiresToday()
    {
        if ($this->isOneOff()) {
            return FALSE;
        }
        $now = new DateTime();
        $now->setTime(23, 59, 59);
        $dtExpires = $this->getDtExpires();

        return $this->isActive() && $dtExpires == $now;
    }

    /**
     * @return bool
     */
    public function isUpgraded()
    {
        $service = $this->getService();

        return $service && $service->isUpgraded();
    }

    /**
     * @return bool
     */
    public function isDowngraded()
    {
        $service = $this->getService();

        return $service && $service->isDowngraded();
    }

    /**
     * @return string|NULL
     */
    public function getStatusId()
    {
        // service.statusId = UPGRADED
        if ($this->isUpgraded()) {
            return Service::STATUS_UPGRADED;
        // service.statusId = DOWNGRADED
        } elseif ($this->isDowngraded()) {
            return Service::STATUS_DOWNGRADED;
        // serviceSetting.autoRenewal = OFF &AND& serviceSetting.emailReminders = OFF
        } elseif ($this->isCancelled()) {
            return Service::STATUS_CANCELLED;
        // service.dtStart = NULL |OR| service.dtStart > TODAY
        } elseif ($this->isOnHold()) {
            return Service::STATUS_ON_HOLD;
        // service.dtExpires IN THE FUTURE OR TODAY &AND& service.dtExpires IN 28 DAYS OR LESS
        } elseif ($this->isDue() && !$this->isAutoRenewalEnabled()) {
            if (!$this->expiresToday()) {
                return Service::STATUS_SOON_DUE;
            } else {
                return Service::STATUS_EXPIRES_TODAY;
            }
        // service.dtExpires IN THE PAST &AND& MORE THAN 28 DAYS HAVE PASSED SINCE service.dtExpires
        } elseif (($this->isExpired() && !$this->isOverdue()) || ($this->isOverdue() && $this->hasActiveNonOverdueServiceOfTheSameType())) {
            return Service::STATUS_SUSPENDED;
        // service.dtExpires IN THE PAST &AND& LESS THAN 28 DAYS HAVE PASSED SINCE service.dtExpires
        } elseif ($this->isOverdue()) {
            return Service::STATUS_OVERDUE;
        // service.dtExpires IS IN THE FUTURE &AND& service.dtExpires IN MORE THAN 28 DAYS
        } elseif ($this->isActive()) {
            if ($this->isIdInvalid()) {
                return Service::STATUS_ID_PROOF_PENDING;
            }
            return Service::STATUS_ACTIVE;
        }

        return NULL;
    }

    /**
     * @return string
     */
    public function getStatus()
    {
        $statusId = $this->getStatusId();
        $status = (isset(Service::$statuses[$statusId])) ? Service::$statuses[$statusId] : NULL;
        if ($statusId == Service::STATUS_SOON_DUE) {
            $days = $this->getDaysToExpire();
            $daysString = "$days day" . ($days > 1 ? 's' : '');
            $status = sprintf($status, $daysString);
        }

        return $status;
    }

    public function isEditDateAllowed(): bool
    {

        if (!Feature::isEnabled('role_admin')) {
            return false;
        }

        if ($this->getService()->getDtc() < (new DateTime())->modify(sprintf("-%s", self::ALLOWED_EDITING_PERIOD))) {
            return false;
        }

        return true;
    }

    /**
     * @return string
     */
    public function getStatusCSSClass()
    {
        if ($this->isCancelled()) {
            return 'fa fa-times-circle em13 grey2';
        }

        if ($this->isDue() && !$this->isAutoRenewalEnabled()) {
            return 'fa fa-exclamation-triangle em13 yellow';
        }

        if ($this->isActive()) {
            if ($this->isOverdue()) {
                return 'fa fa-exclamation-circle em13 red';
            }

            if ($this->isIdInvalid()) {
                return 'fa fa-exclamation-triangle em13 yellow';
            }

            return 'fa fa-check-circle em13 green1';
        }

        if ($this->isExpired()) {
            return 'fa fa-times-circle em13 grey2';
        }

        return '';
    }

    /**
     * @return DateTime|NULL
     */
    public function getDtStart()
    {
        $dtStart = NULL;
        foreach ($this->services as $service) {
            $serviceDtStart = $service->getDtStart();
            if (!empty($serviceDtStart)) {
                if (empty($dtStart) || $dtStart > $serviceDtStart) {
                    $dtStart = $serviceDtStart;
                }
            }

        }

        return $dtStart;
    }

    /**
     * @return DateTime|NULL
     */
    public function getDtExpires()
    {
        $dtExpires = NULL;
        foreach ($this->services as $service) {
            $serviceDtExpires = $service->getDtExpires();
            if (!empty($serviceDtExpires)) {
                if (empty($dtExpires) || $dtExpires < $serviceDtExpires) {
                    $dtExpires = $serviceDtExpires;
                }
                $dtExpires->setTime(23, 59, 59);
            }
        }

        return $dtExpires;
    }

    /**
     * @return bool
     */
    public function isOneOff()
    {
        foreach ($this->services as $service) {
            if ($service->isOneOff()) {
                return TRUE;
            }
        }

        return FALSE;
    }

    /**
     * @return bool
     */
    public function isRenewable()
    {
        foreach ($this->services as $service) {
            if ($service->isRenewable()) {
                return TRUE;
            }
        }

        return FALSE;
    }

    /**
     * @return string|NULL
     */
    public function getServiceTypeId()
    {
        foreach ($this->services as $service) {
            return $service->getServiceTypeId();
        }

        return NULL;
    }

    /**
     * @return bool
     */
    public function hasRenewalProduct()
    {
        $service = $this->getLatest();

        return $service ? $service->hasRenewalProduct() : FALSE;
    }

    /**
     * @return NULL|Product
     */
    public function getRenewalProduct()
    {
        $service = $this->getLatest();
        return $service ? $service->getRenewalProduct() : NULL;
    }

    public function hasOptionalRenewalProduct()
    {
        $service = $this->getLatest();

        return $service && $service->getProduct()->hasOptionalRenewalProduct();
    }

    /**
     * @return NULL|Product
     */
    public function getOptionalRenewalProduct()
    {
        $service = $this->getLatest();

        return $service ? $service->getProduct()->getOptionalRenewalProduct() : NULL;
    }

    /**
     * @return int|null
     */
    public function getRenewalProductId()
    {
        $service = $this->getLatest();

        return $service ? $service->getRenewalProductId() : NULL;
    }

    /**
     * @return float|NULL
     */
    public function getRenewalPrice()
    {
        if ($this->hasRenewalProduct()) {
            return $this->getRenewalProduct()->getRenewalEmailPrice($this->getCustomer(), $this->getDtExpires());
        }
        return NULL;
    }

    /**
     * @return bool
     */
    public function hasParent()
    {
        $service = $this->getLatest();

        return $service ? $service->hasParent() : FALSE;
    }

    /**
     * @return bool
     */
    public function getParent()
    {
        $service = $this->getLatest();

        return $service ? $service->getParent() : NULL;
    }

    /**
     * @return NULL|Service[]
     */
    public function getChildren()
    {
        $service = $this->getLatest();

        return $service ? $service->getChildren() : NULL;
    }

    /**
     * @return Service
     */
    public function getService()
    {
        $service = $this->getLatest();

        return $service ?: null;
    }

    public function getServiceName(): ?string
    {
        $service = $this->getLatest();

        return $service ? $service->getServiceName() : null;
    }

    public function getServiceTitleName(): ?string
    {
        $service = $this->getLatest();

        return $service ? $service->getProductTitle() : null;
    }

    public function getServiceType(): string
    {
        $latest = $this->getLatest();
        if ($latest) {
            return $latest->getServiceType();
        }

        return '';
    }

    /**
     * @return NULL|Service
     */
    private function getLatest()
    {
        $dtExpires = $latestService = NULL;
        foreach ($this->services as $service) {
            $serviceDtExpires = $service->getDtExpires();
            if (!empty($serviceDtExpires)) {
                if (empty($dtExpires) || $dtExpires < $serviceDtExpires) {
                    $dtExpires = $serviceDtExpires;
                    $latestService = $service;
                }
            }
        }

        return empty($latestService) ? end($this->services) : $latestService;
    }

    /**
     * @return bool
     */
    public function isPackageType()
    {
        foreach ($this->services as $service) {
            if ($service->isPackageType()) {
                return TRUE;
            }
        }
    }

    /**
     * @return bool
     */
    public function isAutoRenewalEnabled()
    {
        $service = $this->getLatest();
        if (!$service) {
            return FALSE;
        }
        $setting = $this->serviceSettingsRepository->getSettingsByService($service);
        if (!$setting) {
            return FALSE;
        }
        return $setting->isAutoRenewalEnabled();
    }

    /**
     * @return bool
     */
    public function hasEmailReminders()
    {
        return maybeInstance(Service::class, $this->getLatest())
            ->bind(function(Service $service) { return maybeNull($this->serviceSettingsRepository->getSettingsByService($service)); })
            ->map(invoke('hasEmailReminders'))
            ->orElse(toCallable(just(false)))
            ->extract();
    }

    /**
     * @return bool
     */
    public function isAutoRenewalAllowed()
    {
        $latest = $this->getLatest();

        return $latest ? $latest->getProduct()->isAutoRenewalAllowed : FALSE;
    }

    /**
     * @return bool
     */
    public function isEnabled()
    {
        $latest = $this->getLatest();

        return $latest ? $latest->isEnabled() : FALSE;
    }

    /**
     * @return bool
     */
    public function canReceiveReminders()
    {
        $service = $this->getLatest();

        return $service ? (!$service->isOneOff() && $this->hasEmailReminders() && !$this->isAutoRenewalEnabled()) : FALSE;
    }

    /**
     * @return bool
     */
    public function isActionRequired()
    {
        if ($this->isCancelled()) {
            return FALSE;
        }

        if (
            $this->isOverdue()
            || ($this->isDue() && !$this->isAutoRenewalEnabled())
            || ($this->isAddressType() && $this->isActive() && $this->isIdInvalid())
        ) {
            return TRUE;
        }

        return FALSE;
    }

    /**
     * @return bool|null
     */
    public function isSuspended()
    {
        return $this->isExpired() || $this->isCancelled();
    }

    /**
     * @return ICustomer|null
     */
    public function getCustomer()
    {
        return $this->getService() ? $this->getService()->getCompany()->getCustomer() : NULL;
    }

    /**
     * @return int|null
     */
    public function getCompanyId()
    {
        return $this->getService() ? $this->getService()->getCompany()->getId() : NULL;
    }

    /**
     * @return bool
     */
    public function isAddressType(): bool
    {
        $service = $this->getLatest();
        return $service->isAddressType() || $service->hasChildrenOfAddressType();
    }

    /**
     * @return bool
     */
    public function isIdInvalid(): bool
    {
        return $this->companyIdValid === FALSE;
    }

    public function hasLatePaymentFee(): bool
    {
        return $this->latePaymentFeeDecider->isEligible($this);
    }

    public function getStatusTitle(string $status): string
    {
        $tooltips = [
            'On Hold' => 'Service purchased, but not started',
            'Active' => 'Service active and payment up to date',
            'Overdue' => 'Service limited and payment overdue (28 days grace period)',
            'Expired' => 'Service expired',
            'Downgraded' => 'Service downgraded to different service',
            'Upgraded' => 'Service upgraded to different service',
        ];

        return $tooltips[$status] ?? '';
    }

    public function callBackAutoRenewal()
    {
        $service = $this->getService();

        $el = Html::el();
        $bold = $el->create('b');
        $link = $el->create('a');
        if ($this->isAutoRenewalEnabled()) {
            $bold->setText('On ');
            if (!$this->isExpired() && $service->isEnabled()) {
                $link
                    ->href(
                        '',
                        [
                            'company_id' => $service->getCompany()->getId(),
                            'service_id' => $service->getId(),
                            'renewal' => 'disable',
                        ]
                    )
                    ->setText('Turn Off');
            }
        } else {
            $bold->setText('Off ');
            if ($this->activePaymentMethod && ($this->canToggleAutoRenewal() || $this->isOnHold()) && $service->isEnabled()) {
                $confirmation = "Do you want to enable auto-renewal?\nPayment method to be used: {$this->activePaymentMethod->getCardTypeText()} ending in {$this->activePaymentMethod->getCardNumber()}.";
                $confirmation .= $this->expiresToday() || $this->isOverdue() ? "\nWe'll process this payment overnight and email the customer once confirmed." : '';

                $link
                    ->href(
                        '',
                        [
                            'company_id' => $service->getCompany()->getId(),
                            'service_id' => $service->getId(),
                            'renewal' => 'enable',
                        ]
                    )
                    ->setText('Turn On')
                    ->{'data-confirmation'} = $confirmation;
            }
        }

        return $el;
    }

    public function callBackReminders()
    {
        $remindersEnabled = $this->hasEmailReminders();
        $service = $this->getService();

        $el = Html::el();
        $bold = $el->create('b');
        $link = $el->create('a');
        if ($remindersEnabled) {
            $bold->setText('On ');

            if ($service->isEnabled()) {
                $link->href(
                    '',
                    [
                        'company_id' => $service->getCompany()->getId(),
                        'service_id' => $service->getId(),
                        'reminders' => 'disable',
                    ]
                )->setText('Turn Off');
            }
        } else {
            $bold->setText('Off ');

            if ($service->isEnabled()) {
                $link->href(
                    '',
                    [
                        'company_id' => $service->getCompany()->getId(),
                        'service_id' => $service->getId(),
                        'reminders' => 'enable',
                    ]
                )->setText('Turn On');
            }
        }
        return $el;
    }

    public function callbackOrderLink()
    {
        $service = $this->getService();
        $orderId = $service->getOrder() ? $service->getOrder()->getId() : NULL;
        if (!$orderId) {
            return Html::el();
        }
        $el = Html::el();
        $el->create('a')
            ->href(
                FApplication::$router->link(sprintf('%s view', OrdersAdminControler::ORDERS_PAGE)),
                ["order_id" => $orderId]
            )
            ->setText('View Order');
        return $el;
    }

    public function callbackServiceName()
    {
        $service = $this->getService();
        $order = $service->getOrder();
        $suffix = ($order && $order->isComplimentaryOrder()) ? ' (Complimentary)' : '';

        return $service->getServiceName() . $suffix;
    }

    public function callbackClaimService(): Html
    {
        $el = Html::el();

        if ($this->serviceService) {
            $service = $this->getService();

            $lastService = $this->serviceService->getLastService(
                $service->getCompany(),
                $service->getServiceTypeId()
            );

            $isLastService = $lastService && $lastService->getId() === $service->getId();

            if ($isLastService && $service->isExpired() && $service->isEnabled() && $this->activePaymentMethod) {
                $el->create('input')
                    ->type('checkbox')
                    ->name('serviceIds[]')
                    ->value($service->getId())
                    ->for(
                        sprintf(
                            '%s ending in %s',
                            ucfirst($this->activePaymentMethod->getCardTypeText()),
                            $this->activePaymentMethod->getCardNumber()
                        )
                    );
            }
        }

        return $el;
    }

    private function hasDates(): bool
    {
        return !empty($this->getDtStart()) && !empty($this->getDtExpires());
    }
}

