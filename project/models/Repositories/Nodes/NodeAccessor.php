<?php

namespace Repositories\Nodes;

use <PERSON><PERSON>\Connection;
use Exceptions\Technical\NodeException;
use Framework\FNode;
use ReflectionClass;
use Utils\NetteSmartObject;

class NodeAccessor extends NetteSmartObject
{
    private Connection $connection;

    public function __construct(Connection $connection)
    {
        $this->connection = $connection;
    }

    public function getConnection(): Connection
    {
        return $this->connection;
    }

    /**
     * @param int $nodeId
     * @return FNode|NULL
     */
    public function getNodeById($nodeId)
    {
        $node = NULL;

        try {
            $node = $this->tryGetNodeById($nodeId);
        } catch (NodeException $e) {
            $node = NULL;
        }

        return $node;
    }

    /**
     * @param int $nodeId
     * @return FNode|NULL
     */
    public function tryGetNodeById($nodeId)
    {
        $node = NULL;

        $controllerName = $this->getAdminController($nodeId);
        if ($controllerName) {
            $className = $this->getClassName($controllerName);
            $classNameWithNamespace = sprintf('Models\Products\%s', $className);

            if (!class_exists($className) && class_exists($classNameWithNamespace))
                $className = $classNameWithNamespace;

            $node = new $className($nodeId);
        }

        return $node;
    }

    /**
     * @param string $name
     * @return FNode|NULL
     */
    public function getNodeByName(string $name): ?FNode
    {
        $nodeId = $this->connection->select('node_id')
            ->from(TBL_NODES)
            ->where('name = %s', $name)
            ->fetchSingle();

        return $this->getNodeById($nodeId);
    }

    /**
     * @param string $name
     * @param mixed $value
     * @return array
     */
    public function getNodesIdsByProperty($name, $value)
    {
        return $this->connection->select('nodeId')
            ->from(TBL_PROPERTIES)
            ->where('name = %s', $name)
            ->and('value = %s', $value)
            ->fetchPairs();
    }

    /**
     * @param int $nodeId
     * @return string
     */
    private function getAdminController($nodeId)
    {
        $class_name = $this->connection
            ->select('admin_controler')
            ->from(TBL_NODES)
            ->where('node_id=%i', $nodeId)
            ->fetchSingle();

        return sprintf("AdminModule\Controlers\%s", $class_name);
    }

    /**
     * @param string $controllerName
     * @return string
     */
    private function getClassName($controllerName)
    {
        $class = new ReflectionClass($controllerName);
        $handleObjectProperty = $class->getStaticPropertyValue('handleObject', NULL);
        return $handleObjectProperty ? $handleObjectProperty : 'Framework\FNode';
    }
}
