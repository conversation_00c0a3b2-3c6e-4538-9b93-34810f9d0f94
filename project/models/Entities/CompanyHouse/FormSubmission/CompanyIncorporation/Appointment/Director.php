<?php

namespace Entities\CompanyHouse\FormSubmission\CompanyIncorporation\Appointment;

use Doctrine\ORM\Mapping as Orm;
use Entities\CompanyHouse\FormSubmission\CompanyIncorporation\Appointment;
use Entities\CompanyHouse\Helper\Address;
use Entities\CompanyHouse\Helper\BaseOfficer;
use Entities\CompanyHouse\Helper\PersonDirector;

/**
 * @deprecated use CompanyFormationModule\Entities\DirectorCorporate | CompanyFormationModule\Entities\Person (refactor needed https://gitlab.com/madesimplegroup/cms/issues/26)
 * @Orm\Entity
 */
class Director extends Appointment
{
    const TYPE_NAME = 'director';

    /**
     * we need this for old system
     *
     * @var string
     */
    private $type = self::TYPE_DIRECTOR;

    /**
     * @var string
     * @Orm\Column(name="nationality", type="string", length=20, nullable=true)
     */
    private $nationality;

    /**
     * @var string
     * @Orm\Column(name="occupation", type="string", length=35, nullable=true)
     */
    private $occupation;

    /**
     * @var string
     * @Orm\Column(name="country_of_residence", type="string", length=50, nullable=true)
     */
    private $countryOfResidence;

    /**
     * @var Address
     * (NOTE: no ORM annotation here)
     */
    private $residentialAddress;

    /**
     * @param BaseOfficer $officer
     */
    public function __construct(BaseOfficer $officer)
    {
        parent::__construct($officer);
        $this->extractPersonDirector();
    }

    /**
     * @return BaseOfficer
     */
    public function getOfficer()
    {
        return $this->officer;
    }

    /**
     * @return Address
     */
    public function getResidentialAddress()
    {
        return $this->residentialAddress;
    }

    /**
     * @param Address $residentialAddress
     */
    public function setResidentialAddress($residentialAddress)
    {
        $this->residentialAddress = $residentialAddress;
    }

    /**
     * @param BaseOfficer $officer
     */
    public function setOfficer(BaseOfficer $officer)
    {
        $this->officer = $officer;
    }

    /**
     * @Orm\PreFlush
     * set all properties when this object is to
     * save so that doctrine can easily save these scalar values
     */
    public function extractPersonDirector()
    {
        if ($this->officer instanceof PersonDirector) {
            $this->dob = $this->officer->getDob();
            $this->nationality = $this->officer->getNationality();
            $this->occupation = $this->officer->getOccupation();
            $this->countryOfResidence = $this->officer->getCountryOfResidence();
            $this->extractResidentalAddress($this->officer->getAddress());
        }
    }

    /**
     * @param Address $residentialAddress
     */
    public function extractResidentalAddress($residentialAddress)
    {
        $this->setResidentialPremise($residentialAddress->getPremise());
        $this->setResidentialStreet($residentialAddress->getStreet());
        $this->setResidentialPostTown($residentialAddress->getPostTown());
        $this->setResidentialPostcode($residentialAddress->getPostcode());
        $this->setResidentialThoroughfare($residentialAddress->getThoroughfare());
        $this->setResidentialCounty($residentialAddress->getCounty());
        $this->setResidentialCountry($residentialAddress->getCountry());
        $this->setResidentialSecureAddressInd($residentialAddress->getSecureAddressInd());
    }

    /**
     * @Orm\PostLoad
     * When the row is hydrated into this class,
     * PersonDirector is not set because that isn't mapped.
     * so simply, map it manually
     */
    public function hydratePersonDirector()
    {
        if (!$this->getCorporate()) {
            $address = $this->getHydratedAddress();
            $this->officer = new PersonDirector(
                $this->forename,
                $this->surname,
                $this->dob,
                $this->nationality,
                $this->occupation,
                $this->countryOfResidence,
                $address,
                $this->residentialAddress
            );
        }
    }

    /**
     * @return Address
     */
    public function getHydratedAddress()
    {
        $residentialAddress = new Address(
            $this->getResidentialPremise(),
            $this->getResidentialStreet(),
            $this->getResidentialPostTown(),
            $this->getResidentialPostcode(),
            $this->getResidentialCountry()
        );
        $residentialAddress->setThoroughfare($this->getResidentialThoroughfare());
        $residentialAddress->setCounty($this->getResidentialCounty());
        $residentialAddress->setSecureAddressInd($this->getResidentialSecureAddressInd());

        return $residentialAddress;
    }
}
