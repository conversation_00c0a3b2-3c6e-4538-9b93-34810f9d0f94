<?php

namespace CompaniesHouseModule\FormSubmissions\BaseTypes;

use CompaniesHouseModule\Dto\AddressData;
use PeopleWithSignificantControl\Forms\Old\Address as FormAddress;

class CompanyAddress
{
    /**
     * @var string
     */
    private $premise;

    /**
     * @var string
     */
    private $street;

    /**
     * @var string
     */
    private $thoroughfare;

    /**
     * @var string
     */
    private $postTown;

    /**
     * @var string
     */
    private $county;

    /**
     * @var string
     */
    private $country;

    /**
     * @var string
     */
    private $otherForeignCountry;

    /**
     * @var string
     */
    private $postcode;

    /**
     * @var string
     */
    private $careofName;

    /**
     * @var string
     */
    private $poBox;

    /**
     * @param string $premise
     * @param string $street
     * @param string $thoroughfare
     * @param string $postTown
     * @param string $county
     * @param string $postcode
     * @param string $country
     * @param string $otherForeignCountry
     * @param string $careofName
     * @param string $poBox
     */
    public function __construct(
        string $premise,
        string $street,
        string $postTown,
        string $postcode,
        string $county = NULL,
        string $thoroughfare = NULL,
        string $country = NULL,
        string $otherForeignCountry = NULL,
        string $careofName = NULL,
        string $poBox = NULL
    ) {
        $this->premise = $premise;
        $this->street = $street;
        $this->thoroughfare = $thoroughfare;
        $this->postTown = $postTown;
        $this->county = $county;
        $this->postcode = $postcode;
        $this->country = $country;
        $this->otherForeignCountry = $otherForeignCountry;
        $this->careofName = $careofName;
        $this->poBox = $poBox;
    }

    /**
     * @param AddressData $address
     * @return static
     */
    public static function fromAddressData(AddressData $address)
    {
        return new static(
            $address->getPremise(),
            $address->getStreet(),
            $address->getPostTown(),
            $address->getPostcode(),
            $address->getCounty(),
            $address->getThoroughfare(),
            $address->isNonForeignCountry() ? $address->getCountry() : NULL,
            !$address->isNonForeignCountry() ? $address->getCountry() : NULL
        );
    }

    /**
     * @param FormAddress $address
     * @return CompanyAddress
     */
    public static function fromAddress(FormAddress $address): CompanyAddress
    {
        return new static(
            $address->getPremise(),
            $address->getStreet(),
            $address->getPostTown(),
            $address->getPostcode(),
            $address->getCounty(),
            $address->getThoroughfare(),
            $address->isNonForeignCountry() ? $address->getCountry() : NULL,
            !$address->isNonForeignCountry() ? $address->getCountry() : NULL
        );
    }

    /**
     * @param FormAddress $address
     * @return CompanyAddress
     */
    public static function fromServiceAddress(FormAddress $address): CompanyAddress
    {
        return new static(
            $address->getPremise(),
            $address->getStreet(),
            $address->getPostTown(),
            $address->getPostcode(),
            $address->getCounty(),
            $address->getThoroughfare(),
            $address->isNonForeignCountry() ? $address->getCountry() : NULL,
            !$address->isNonForeignCountry() ? $address->getCountry() : NULL
        );
    }

    /**
     * @return string
     */
    public function getCareofName(): ?string
    {
        return $this->careofName;
    }

    /**
     * @return string
     */
    public function getPoBox(): ?string
    {
        return $this->poBox;
    }

    /**
     * @return string
     */
    public function getPremise(): string
    {
        return $this->premise;
    }

    /**
     * @return string
     */
    public function getStreet(): string
    {
        return $this->street;
    }

    /**
     * @return string
     */
    public function getThoroughfare(): ?string
    {
        return $this->thoroughfare;
    }

    /**
     * @return string
     */
    public function getPostTown(): string
    {
        return $this->postTown;
    }

    /**
     * @return string
     */
    public function getCounty(): ?string
    {
        return $this->county;
    }

    /**
     * @return string
     */
    public function getCountry(): ?string
    {
        return $this->country;
    }

    /**
     * @return string
     */
    public function getOtherForeignCountry(): ?string
    {
        return $this->otherForeignCountry;
    }

    /**
     * @return string
     */
    public function getPostcode(): string
    {
        return $this->postcode;
    }
}
