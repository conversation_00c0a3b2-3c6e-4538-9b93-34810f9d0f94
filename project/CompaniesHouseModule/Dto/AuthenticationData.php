<?php

namespace CompaniesHouseModule\Dto;

use Entities\CompanyHouse\Helper\Authentication;

class AuthenticationData
{
    /**
     * @var string
     */
    private $birthTown;

    /**
     * @var string
     */
    private $telephone;

    /**
     * @var string
     */
    private $mumsMaidenName;
    
    public static function default(): self
    {
        $self = new self;
        $self->setBirthTown(NULL);
        $self->setTelephone(NULL);
        $self->setMumsMaidenName(NULL);
        return $self;
    }
    

    public static function fromAuthentication(Authentication $authentication): self
    {
        $self = new self;
        $self->setBirthTown($authentication->getBirthTown());
        $self->setTelephone($authentication->getTelephone());
        $self->setMumsMaidenName($authentication->getMumsMaidenName());
        return $self;
    }

    public function getBirthTown(): ?string
    {
        return $this->birthTown;
    }

    public function setBirthTown(?string $birthTown): void
    {
        $this->birthTown = $birthTown;
    }

    public function getTelephone(): ?string
    {
        return $this->telephone;
    }

    public function setTelephone(?string $telephone): void
    {
        $this->telephone = $telephone;
    }

    public function getMumsMaidenName(): ?string
    {
        return $this->mumsMaidenName;
    }

    public function setMumsMaidenName(?string $mumsMaidenName): void
    {
        $this->mumsMaidenName = $mumsMaidenName;
    }
}