<?php

namespace CompaniesHouseModule\Dto;

use CompaniesHouseModule\Entities\Address;
use MsgDomain\Address\Postcode;
use Models\Products\RegisterOffice;

class NotifyServiceAddressData
{
    /**
     * @var bool
     */
    private $msgServiceAddress;

    /**
     * @var AddressData
     */
    private $address;

    public static function default(): self
    {
        $self = new self();
        $self->setAddress(new AddressData());
        return $self;
    }

    public static function fromAddress(Address $address): self
    {
        $self = new self();
        if ($self->isOurPostcode($address->getPostcode())) {
            $self->setMsgServiceAddress(TRUE);
        }
        $self->setAddress(AddressData::fromAddress($address));
        return $self;
    }

    public function isMsgServiceAddress(): ?bool
    {
        return $this->msgServiceAddress;
    }

    public function hasMsgServiceAddressPostCode(): ?bool
    {
        if($this->address->getPostcode()){
            return $this->isOurPostCode($this->address->getPostcode());
        }
        return false;
    }

    public function setMsgServiceAddress(?bool $msgServiceAddress): void
    {
        $this->msgServiceAddress = $msgServiceAddress;
    }

    public function getAddress(): ?AddressData
    {
        return $this->address;
    }

    public function setAddress(AddressData $address): void
    {
        $this->address = $address;
    }

    private function isOurPostCode(string $postcode): bool
    {
        return (new Postcode(RegisterOffice::POSTCODE))->isMatching($postcode);
    }
}
