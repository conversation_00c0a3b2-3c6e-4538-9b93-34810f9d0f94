<?php

namespace CompanyImportModule\Facades;

use CompaniesHouse\Entities\CompanyDetails\CompanyDetails;
use CompanySyncModule\Services\ISynchronization;
use CompanySyncModule\Services\SynchronizationService;
use Entities\Company;
use Entities\Customer;
use Exceptions\Technical\CompanyException;
use JMS\Serializer\Exception\UnsupportedFormatException;
use Legacy\Nette\Web\SessionNamespace;
use Libs\Basket;
use SerializingModule\SerializerInterface;
use Services\CompanyService;

class CompanyImportFacade
{

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var SessionNamespace
     */
    private $orderSummarySession;

    /**
     * @var SerializerInterface
     */
    private $serializer;

    /**
     * @var SynchronizationService
     */
    private $synchronizationService;

    public function __construct(
        CompanyService $companyService,
        SessionNamespace $orderSummarySession,
        SerializerInterface $serializer,
        SynchronizationService $synchronizationService
    )
    {
        $this->companyService = $companyService;
        $this->orderSummarySession = $orderSummarySession;
        $this->serializer = $serializer;
        $this->synchronizationService = $synchronizationService;
    }

    public function createImportedCompanies(Customer $customer, Basket $basket): array
    {
        if ($basket->isEmpty()) return [];

        $notImportedCompanies = [];

        foreach ($basket->getItems() as $itemId => $item) {
            if ($item->isLinkedToNewCompany()) {
                $company = $this->companyService->getCompanyByCompanyNumber($item->companyNumber);

                if ($company) {
                    if ($company->getCustomer()->isEqual($customer)) {
                        $item->setCompanyId($company->getCompanyId());
                    } else {
                        $notImportedCompanies[$itemId] = $company;
                    }
                } else {
                    $companyDetails = $this->orderSummarySession->importedCompanies[$item->getCompanyNumber()];

                    if (empty($companyDetails)) {
                        throw CompanyException::importedCompanyDetailsMissing($item->companyNumber);
                    }

                    try {
                        /** @var CompanyDetails $unserializedData */
                        $unserializedData = $this->serializer->unserialize(
                            $companyDetails,
                            CompanyDetails::class
                        );

                        $importedCompany = new Company($customer, $unserializedData->getCompanyName());
                        $importedCompany->setCompanyNumber($unserializedData->getCompanyNumber());
                        $this->companyService->saveCompany($importedCompany);

                        $this->synchronizationService->synchronizeImportedCompanyById($importedCompany->getId(), ISynchronization::TYPE_PUBLIC);
                        $item->setCompanyId($importedCompany->getCompanyId());
                    } catch (UnsupportedFormatException $exception) {
                        throw CompanyException::importedCompanyDetailsMissing($item->getCompanyNumber());
                    }
                }
            }
        }

        return $notImportedCompanies;
    }
}