<customer-availability-login>
    {$formHelper->setTheme($form, 'horizontal.html.twig')}
    {$formHelper->start($form, ['attr' => ['id' => 'login_form', 'rv-on-submit' => 'useCredentials']]) nofilter}

    <p class="fs-4 fw-semibold">Create Account or Log In</p>
    <p>We'll use your email address to send your company details and purchase confirmation</p>

    {$formHelper->errors($form) nofilter}

    <div id="dev-email-reminder" class="alert alert-info hidden">Hey pal, you're on DEV! You need to use an @madesimplegroup.com email (even if it's fake), otherwise it won't work.</div>
    <div class="form-group{if !$form['email']->vars['valid']} has-error{/if}">
        <span class="fw-semibold">
            {$formHelper->label($form['email']) nofilter}
        </span>
        <div class="col-sm-6 col-xs-12 has-feedback">
            {$formHelper->widget($form['email'], ['attr' => ['rv-on-blur' => 'msgMailChecker.suggestEmail', 'rv-on-keydown' => 'msgMailChecker.hideSuggestion']]) nofilter}
            <div class="has-success{if empty($emailConfirmed)} hidden{/if}" rv-show="isEmailLocked < emailConfirmed requiresToLogin">
                <span class="glyphicon glyphicon-ok form-control-feedback"></span>
            </div>
            <span class="text-danger fw-bold mb-4">
                {$formHelper->errors($form['email']) nofilter}
            </span>
        </div>

        <div class="col-sm-4 col-xs-12{if empty($emailConfirmed)} hidden{/if}" rv-show="isEmailLocked < emailConfirmed requiresToLogin">
            <p class="payment__email--other mt-2">
                <a rv-on-click="useAnotherEmail" href="javascript:;" class="text-primary text-decoration-underline fw-semibold">Use another email</a>
            </p>
        </div>
            {ui name="mailcheck_notification_form" emailFieldId="login_availability_form_email" submitButtonName="check" customClasses="top10 col-sm-offset-2"}
        <div class="col-sm-6 col-sm-offset-2 col-xs-12 top10{if empty($emailConfirmed) || !empty($requiresToLogin)} hidden{/if}" rv-show="canUseCredentials < emailConfirmed requiresToLogin">
            <p>Your account has been created!<br>Please enter your payment details below:</p>
        </div>
    </div>

    <div class="modal fade" id="devModal" tabindex="-1" role="dialog" aria-labelledby="devModalLabel" style="z-index: 100000;">
        <div class="modal-dialog" role="document">
            <div class="modal-content p-2">
                <div class="modal-header">
                    <a class="modal-title fw-semibold fs-5" id="devModalLabel">dev.companiesmadesimple.com</h4>
                    <i type="button" class="close fa-solid fa-xmark fa-lg text-dark" data-bs-dismiss="modal"></i>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info"> Hey pal, you're on DEV! You need to use an @madesimplegroup.com email (even if it's fake), otherwise it won't work.</div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group{if !empty($requiresToLogin)} hidden{/if} mb-4" rv-hide="requiresToLogin">
        <div class="float-sm-start" rv-on-click="setIsLoading">
            <button type="submit" id="submit-check"
                class="btn btn-default cms-btn mt-4 checkoutStepClick log-in-button w-100 fw-semibold availability-login-button" data-checkout-step="2" name="check" value="1"
                rv-disabled="hasToDisableLoginButton < emailConfirmed msgMailChecker.hasSuggestions"
                rv-on-click="msgMailChecker.suggestEmail">
                Create account or log in <i class="fa fa-arrow-right-long ms-2"></i>
                <div id="spinner" class="spinner-border  spinner-border-sm d-none ms-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </button>
        </div>
    </div>
    <div class="{if empty($requiresToLogin)}hidden{/if}" rv-show="requiresToLogin">
        <div class="form-group{if !$form['password']->vars['valid']} has-error{/if}">
            <span class="fw-semibold">
                {$formHelper->label($form['password']) nofilter}
            </span>
            <div class="col-sm-6">
                {$formHelper->widget($form['password']) nofilter}
                {$formHelper->errors($form['password']) nofilter}
            </div>
        </div>
        <div class="col-sm-6">
            <div rv-hide="sendingEmail">
                <div class="hidden" rv-show="emailSent">New password sent</div>
                <div class="payment__email--other mt-2">
                    <a href="#" rv-on-click="showForgottenEmailForm" data-bs-toggle="modal" data-bs-target="#modal-forgotten-password" class="text-primary text-decoration-underline fw-semibold">I've forgotten my password</a>
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="float-sm-start">
                <button type="submit" name="login" value="1" class="btn btn-default cms-btn my-4 checkoutStepClick log-in-button w-100 fw-semibold availability-login-button">
                    Create account or log in
                    <i class="fa fa-arrow-right-long ms-2"></i>
                </button>
            </div>
        </div>
    </div>
    {$formHelper->widget($form['context']) nofilter}
    {$formHelper->end($form) nofilter}

<div class="modal fade" id="modal-forgotten-password" rv-on-hidden.bs.modal="refreshForgottenEmailForm" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" style="z-index: 100000;">
    <div class="modal-dialog" role="document">
        <div class="modal-content p-2">
            <div class="modal-header">
                <a class="modal-title fw-semibold fs-5" id="myModalLabel">Forgotten Password</a>
                <i type="button" class="close fa-solid fa-xmark fa-lg text-dark" data-bs-dismiss="modal"></i>
            </div>

            <div class="modal-body">
                <div class="alert alert-warning top10 forgotten-password-success" rv-show="isForgottenPasswordSuccessShown" role="alert">Email sent, please check your inbox.</div>

                <div class="forgotten-password-container" rv-hide="isForgottenPasswordSuccessShown">
                    <p>Enter your email address below and we'll send your password reset instructions.</p>
                    {$formHelper->setTheme($forgottenPasswordForm, 'cms.html.twig')}
                    {$formHelper->start($forgottenPasswordForm, ['attr' => ['id' => 'forgotten_password_form', 'rv-on-submit' => 'onForgottenFormSubmit']]) nofilter}
                        {$formHelper->row($forgottenPasswordForm['email'], ['attr' => ['class' => 'mt-2', 'rv-value' => 'forgottenPasswordEmail', 'rv-on-blur' => 'msgMailChecker.suggestEmail', 'rv-on-keydown' => 'msgMailChecker.hideSuggestion']]) nofilter}
                        <div class="float-sm-start mt-4">
                            <button type="submit" class="btn btn-default cms-btn w-100 availability-login-button">
                                Email my instructions
                                <i class="fa fa-arrow-right-long ms-2"></i>
                                <span rv-show="isLoading">
                                    <div class="spinner-border spinner-border-sm ms-3" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </span>
                            </button>
                        </div>
                         {ui name="button" size="s" text="Email my instructions" loading="expand-right" classAttr="btm10 forgotten-password-submit d-none"}
                    {$formHelper->end($forgottenPasswordForm) nofilter}
                </div>
                <div class="row w-100">
                    <div class="col-12">
                        <hr>
                        <p class="mb-4">About spam filters</p>
                        <p>If you don't get the email within a few minutes please check your spam folder. The email will be <NAME_EMAIL></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</customer-availability-login>

<style>
    #login_availability_form_email-error, #login_availability_form_password-error {
        color: #C00000;
        font-weight: 600;
    }
</style>

<script type="text/javascript">
    const emailInput = $('#login_availability_form_email');

    function trimEmailInput() {
        emailInput.val(emailInput.val().trim());
    }

    emailInput.on('change', trimEmailInput);
    $('#submit-check').on('click', trimEmailInput);

    $(document).ready(function() {
        const submitButton = $("#submit-check");
        const spinner = $("#spinner");

        let intervalId = null;
        const varName = function(){
            if(window.loginAvailability.isLoading) {
                spinner.show();
                submitButton.prop('disabled', true);
            } else {
                spinner.hide();
                submitButton.prop('disable', false);
                clearInterval(intervalId);
            }
        };

        submitButton.click(function() {
            intervalId = setInterval(varName, 500);
        });

        if ($(location).attr('hostname') === "dev.companiesmadesimple.com") {
            $("#dev-email-reminder").removeClass('hidden');
            submitButton.click(function(event) {
                let value = $("#login_availability_form_email").val();
                if (value != '' &&  value.indexOf('@madesimplegroup.com') == -1) {
                    event.preventDefault();
                    $("#devModal").modal('show');
                }
            });
        }
    });

    window.loginAvailability = customerLogin.bind(
            {$emailConfirmed|json nofilter},
            {$requiresToLogin|json nofilter},
            '{url route="login_availability"}',
            msgMailChecker,
            {literal}{loginFormId: '#login_form', forgottenPasswordFormId: '#forgotten_password_form'  }{/literal}
    );
</script>

