<?php

namespace ToolkitOfferModule\Emailers;

use BusinessServicesModule\Entities\Lead;
use CompanyFormationModule\Repositories\SicCodesRepository;
use EmailModule\IEmail;
use EmailModule\IEmailGateway;
use EmailModule\IEmailLog;
use Entities\Company;
use Entities\Customer;
use InvalidArgumentException;
use ToolkitOfferModule\Entities\CompanyToolkitOffer;
use ToolkitOfferModule\Helpers\EmailPlaceholderHelper;

class ToolkitOfferEmailer
{
    /**
     * @var IEmailGateway
     */
    private $gateway;

    /**
     * @var EmailFactory
     */
    private $emailFactory;

    /**
     * @var EmailPlaceholderHelper
     */
    private $placeholderHelper;

    /**
     * @var SicCodesRepository
     */
    private $sicCodesRepository;

    public function __construct(
        IEmailGateway $gateway,
        EmailFactory $emailFactory,
        EmailPlaceholderHelper $placeholderHelper,
        SicCodesRepository $sicCodesRepository
    )
    {
        $this->gateway = $gateway;
        $this->emailFactory = $emailFactory;
        $this->placeholderHelper = $placeholderHelper;
        $this->sicCodesRepository = $sicCodesRepository;
    }

    /**
     * @param Customer $customer
     * @email cms/Offers/Toolkit/TaxAssist/taxassist
     */
    public function sendTaxAssistEmail(Customer $customer)
    {
        $email = $this->emailFactory->getTaxAssistEmail($customer);
        $this->send($customer, $email);
    }

    /**
     * @param Customer $customer
     */
    public function sendFreeAgentEmail(Customer $customer)
    {
        $email = $this->emailFactory->getFreeAgentEmail();
        $this->send($customer, $email);

    }

    /**
     * @param Customer $customer
     * @email cms/Offers/Toolkit/Ebook/ebook
     */
    public function sendMakeMoreProfitGuideEmail(Customer $customer)
    {
        $email = $this->emailFactory->getEbookEmail($customer);
        $this->send($customer, $email);
    }

    /**
     * @email cms/Offers/Toolkit/Ebook/ebook
     */
    public function sendEbookEmail(Customer $customer): ?IEmailLog
    {
        $email = $this->emailFactory->getEbookEmail($customer);
        return $this->send($customer, $email);
    }

    /**
     * @param Customer $customer
     * @email cms/Offers/Toolkit/Customers-Facebook-Community/customers-facebook-community
     */
    public function sendFacebookGroupEmail(Customer $customer): ?IEmailLog
    {
        $email = $this->emailFactory->getFacebookGroupEmail( $customer);
        return $this->send($customer, $email);
    }

    /**
     * @param Customer $customer
     */
    public function sendWorkHubPassEmail(Customer $customer): ?IEmailLog
    {
        $email = $this->emailFactory->getWorkHubPassEmail();
        return $this->send($customer, $email);
    }

    /**
     * @param CompanyToolkitOffer $offer
     * @throws InvalidArgumentException
     */
    public function sendEmailOffer(CompanyToolkitOffer $offer)
    {
        $company = $offer->getCompany();
        $customer = $company->getCustomer();
        $data = $this->getDataForEmail($company, $customer);
        $emailSent = NULL;

        $customerEmail = $this->emailFactory->getEmailByType($offer->getType(), $data);
        if ($customerEmail) {
            $emailSent = $this->send($customer, $customerEmail);
        }
        $leadEmail = $this->emailFactory->getLeadEmailByType($offer->getType(), $data);
        if ($leadEmail) {
            $emailSent = $this->sendLead($leadEmail, $company, $customer);
        }
        return $emailSent;

    }

    /**
     * @param Customer $customer
     * @param IEmail $email
     */
    private function send(Customer $customer, IEmail $email): ?IEmailLog
    {
        $email->setTo($customer->getEmail());
        return $this->gateway->send($email, $customer);
    }

    private function sendLead(IEmail $email, Company $company, Customer $customer)
    {
        $email->replaceAllPlaceHolders($this->placeholderHelper->getAll($company, $customer));

        return $this->gateway->send($email, $company->getCustomer());
    }

    private function getDataForEmail(Company $company, Customer $customer): array
    {
        return [
            'company' => $company,
            'companyName' => $company->getCompanyName(),
            'companyNumber' => $company->getCompanyNumber(),
            'customer' => $customer,
            'firstName' => $customer->getFirstName(),
            'fullName' => $customer->getFullName(),
            'email' => $customer->getEmail(),
            'phone' => $customer->getPhone(),
            'companySicCodes' => $this->sicCodesRepository->getCompanySicCodesWithDesctiption($company),
        ];
    }
}
