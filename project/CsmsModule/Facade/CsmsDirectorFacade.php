<?php

namespace CsmsModule\Facade;

use CsmsModule\ApiClient\CsmsApiClient;
use CsmsModule\ApiClient\ICsmsApiClient;
use CsmsModule\Dto\CsmsUkCompanyData;
use CsmsModule\Dto\CsmsUkDirectorData;
use CsmsModule\Exceptions\CsmsDirectorException;
use CsmsModule\Forms\CsmsUkDirectorNameSearchForm;
use CsmsModule\Views\CsmsUkCompanyView;
use CsmsModule\Views\CsmsUkDirectorView;
use Exception;
use HttpClient\Requests\RequestOptions;
use HttpModule\Responses\FileResponse;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Response;
use Utils\Date;

class CsmsDirectorFacade
{
    /**
     * @var ICsmsApiClient
     */
    private $apiClient;

    public function __construct(ICsmsApiClient $apiClient) {
        $this->apiClient = $apiClient;
    }

    /**
     * @throws CsmsDirectorException
     */
    public function getUkDirectorSearchResult(FormInterface $ukDirectorSearchForm): array
    {
        $formData = $ukDirectorSearchForm->getData() ?? [];

        if (empty($formData)) {
            throw CsmsDirectorException::emptyFormData();
        }

        if (!$formData['directorName'] && !$formData['directorNumber']) {
            throw CsmsDirectorException::emptyNameAndNumber();
        }

        $ukDirectorsResult = $this->searchUkDirector($formData);

        try {
            $results = [];
            foreach ($ukDirectorsResult['data'] as $directorData) {
                $ukDirectorData = CsmsUkDirectorData::fromArray($directorData);
                $ukDirectorView = new CsmsUkDirectorView($ukDirectorData, null);
                $results[] = $ukDirectorView;
            }
            $ukDirectorsResult['data'] = $results;
        } catch (Exception $e) {
            throw new CsmsDirectorException($e->getMessage(), $e->getCode());
        }

        return  $ukDirectorsResult;
    }

    /**
     * @throws CsmsDirectorException
     */
    private function searchUkDirector(array $formData): array
    {
        $searchOptions = $this->getSearchOptions($formData);
        $response = $this->apiClient->sendPostRequest(CsmsApiClient::DIRECTOR_SEARCH, $searchOptions);

        if ($response->getCode() >= Response::HTTP_BAD_REQUEST) {
            throw new CsmsDirectorException($response->getBody(), $response->getCode());
        }

        $responseData = json_decode($response->getBody(), true) ?? [];

        if (empty($responseData) || empty($responseData['data'])) {
            throw CsmsDirectorException::emptyResponseData();
        }

        return $responseData;
    }

    private function getSearchOptions(array $formData): array
    {
        return [
            'directorName' => $formData['directorName'],
            'directorNumber' => $formData['directorNumber'],
            'directorBirth' => $this->getDirectorBirthFromFormData($formData),
            'page' => intval($formData['page']) ?? CsmsUkDirectorNameSearchForm::DEFAULT_PAGE,
            'limit' => intval($formData['limit']) ?? CsmsUkDirectorNameSearchForm::DEFAULT_LIMIT
        ];
    }

    private function getDirectorBirthFromFormData(array $formData): ?string
    {
        if ($formData['directorBirth'] instanceof Date) {
            return $formData['directorBirth']->format('Y-m-d');
        }
        return null;
    }

    /**
     * @throws CsmsDirectorException
     */
    public function getDirectorReport(string $directorNumber): FileResponse
    {
        $data = [
            'directorNumber' => $directorNumber
        ];

        $response = $this->apiClient->sendGetRequest(CsmsApiClient::DIRECTOR_RETRIEVE, $data);

        if ($response->getCode() >= Response::HTTP_BAD_REQUEST) {
            throw new CsmsDirectorException($response->getBody(), $response->getCode());
        }

        $responseData = json_decode($response->getBody(), true) ?? [];

        if (empty($responseData) || empty($responseData['pdf'])) {
            throw CsmsDirectorException::emptyResponseData();
        }

        $content = base64_decode($responseData['pdf']);

        if (!$content) {
            throw CsmsDirectorException::invalidDirectorReportContent();
        }

        return new FileResponse($content, $responseData['fileName'], Response::HTTP_OK);
    }
}