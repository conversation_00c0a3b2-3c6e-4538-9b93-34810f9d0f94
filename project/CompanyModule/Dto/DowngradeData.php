<?php

namespace CompanyModule\Dto;

use DateTime;
use Entities\CompanyHouse\FormSubmission\CompanyIncorporation;

class DowngradeData
{
    /**
     * @var string|null
     */
    private $package;

    /**
     * @var DateTime|null
     */
    private $end;

    public function __construct(DateTime $end)
    {
        $this->end = $end;
    }

    public function getPackage(): ?string
    {
        return $this->package;
    }

    public function setPackage(?string $package): void
    {
        $this->package = $package;
    }

    public function getEnd(): ?DateTime
    {
        return $this->end;
    }

    public function setEnd(?DateTime $end): void
    {
        $this->end = $end;
    }
}
