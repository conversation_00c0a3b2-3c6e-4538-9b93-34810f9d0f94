<?php

namespace CompanyModule\Entities\Settings;

use Doctrine\ORM\Mapping as Orm;
use Entities\Company;
use Utils\Date;

/**
 * @Orm\Entity
 */
class MailForwardingQuotaSetting extends CompanySetting
{
    public const STATUTORY_SCANNED_QUOTAS_KEY = 'statutory-scanned';
    public const STATUTORY_COLLECT_QUOTAS_KEY = 'statutory-collect';
    public const STATUTORY_POST_QUOTAS_KEY = 'statutory-post';
    public const NON_STATUTORY_SCANNED_QUOTAS_KEY = 'non-statutory-scanned';
    public const NON_STATUTORY_COLLECT_QUOTAS_KEY = 'non-statutory-collect';
    public const NON_STATUTORY_POST_QUOTAS_KEY = 'non-statutory-post';
    public const PARCEL_COLLECT_QUOTAS_KEY = 'parcel-collect';
    public const PARCEL_POST_QUOTAS_KEY = 'parcel-post';
    public const QUOTA_KEYS = [
        self::STATUTORY_SCANNED_QUOTAS_KEY,
        self::STATUTORY_COLLECT_QUOTAS_KEY,
        self::STATUTORY_POST_QUOTAS_KEY,
        self::NON_STATUTORY_SCANNED_QUOTAS_KEY,
        self::NON_STATUTORY_COLLECT_QUOTAS_KEY,
        self::NON_STATUTORY_POST_QUOTAS_KEY,
        self::PARCEL_COLLECT_QUOTAS_KEY,
        self::PARCEL_POST_QUOTAS_KEY,
    ];
    private const EXPIRES_KEY = 'expires';
    private const TYPE_KEY = 'type';
    private const TOKEN_ID_KEY = 'token_id';

    public function __construct(
        Company $company,
        $type = null,
        $tokenId = null,
        int $statutoryScannedQuotas = 0,
        int $statutoryCollectQuotas = 0,
        int $statutoryPostQuotas = 0,
        int $nonStatutoryScannedQuotas = 0,
        int $nonStatutoryCollectQuotas = 0,
        int $nonStatutoryPostQuotas = 0,
        int $parcelCollectQuotas = 0,
        int $parcelPostQuotas = 0,
        $expires = null
    ) {
        $expires = $expires ?? $this->getFormattedExpiryDate();
        parent::__construct($company, [
            self::TYPE_KEY => $type,
            self::TOKEN_ID_KEY => $tokenId,
            self::STATUTORY_SCANNED_QUOTAS_KEY => $statutoryScannedQuotas,
            self::STATUTORY_COLLECT_QUOTAS_KEY => $statutoryCollectQuotas,
            self::STATUTORY_POST_QUOTAS_KEY => $statutoryPostQuotas,
            self::NON_STATUTORY_SCANNED_QUOTAS_KEY => $nonStatutoryScannedQuotas,
            self::NON_STATUTORY_COLLECT_QUOTAS_KEY => $nonStatutoryCollectQuotas,
            self::NON_STATUTORY_POST_QUOTAS_KEY => $nonStatutoryPostQuotas,
            self::PARCEL_COLLECT_QUOTAS_KEY => $parcelCollectQuotas,
            self::PARCEL_POST_QUOTAS_KEY => $parcelPostQuotas,
            self::EXPIRES_KEY => $expires,
        ]);
    }

    public function addQuotasByType(int $amount, string $type, string $handlingOption): int
    {
        return $this->updateQuotasByTypeAndHandlingOption(
            $this->getQuotasByTypeAndHandlingOption($type, $handlingOption) + $amount,
            $type,
            $handlingOption
        );
    }

    public function updateQuotasByTypeAndHandlingOption(int $newAmount, string $type, string $handlingOption): int
    {
        $key = $type . '-' . $handlingOption;
        $this->isValidQuotaKey($key);

        $this->value[$key] = $newAmount;

        return $newAmount;
    }

    public function subtractQuotasByType(int $quotas, string $type, string $handlingOption): int
    {
        $key = $type . '-' . $handlingOption;
        $this->isValidQuotaKey($key);

        $result = $this->getQuotasByTypeAndHandlingOption($key, $handlingOption) - $quotas;
        if ($result < 0) {
            $result = 0;
        }

        return $this->updateQuotasByTypeAndHandlingOption($result, $type, $handlingOption);
    }

    public function getQuotasByTypeAndHandlingOption(string $type, string $handlingOption): int
    {
        $key = $type . '-' . $handlingOption;
        $this->isValidQuotaKey($key);

        return $this->value[$key] ?? 0;
    }

    public function getFormattedExpiryDate(): string
    {
        return (new Date('+1 month'))->format('Y-m-d');
    }

    private function isValidQuotaEntry(array $quotaEntry): bool
    {
        foreach (self::QUOTA_KEYS as $key) {
            if (!isset($quotaEntry[$key])) {
                return false;
            }
        }

        return new Date($quotaEntry[self::EXPIRES_KEY]) > new Date();
    }

    private function isValidQuotaKey(string $key): void
    {
        if (!in_array($key, self::QUOTA_KEYS)) {
            throw new \InvalidArgumentException(
                sprintf(
                    'Invalid quota key. Got %s, expected one of %s',
                    $key,
                    implode(', ', self::QUOTA_KEYS)
                )
            );
        }
    }
}
