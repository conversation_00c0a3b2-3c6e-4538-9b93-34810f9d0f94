<?php

declare(strict_types=1);

namespace CompanyModule\Entities\Settings;

use Doctrine\ORM\Mapping as Orm;
use Entities\Company;
use MailScanModule\Dto\MailroomPostItemData;

/**
 * @Orm\Entity
 */
class PostItemHandlingSetting extends CompanySetting
{
    public const KEY_POST_ITEM_HANDLING = 'post_item_handling';
    public const KEY_PARCEL_HANDLING = 'parcel_handling';
    public const VALUE_ITEM_SCAN_ONLY = 1;
    public const VALUE_ITEM_SCAN_AND_COLLECT = 2;
    public const VALUE_ITEM_SCAN_AND_POST = 3;
    public const VALUE_PARCEL_COLLECT = 1;
    public const VALUE_PARCEL_POST = 2;

    public const POST_ITEM_SCAN_ONLY = 'POST_ITEM_SCAN_ONLY';
    public const POST_ITEM_SCAN_AND_COLLECT = 'POST_ITEM_SCAN_AND_COLLECT';
    public const POST_ITEM_SCAN_AND_POST = 'POST_ITEM_SCAN_AND_POST';
    public const PARCEL_COLLECT = 'PARCEL_COLLECT';
    public const PARCEL_POST = 'PARCEL_POST';

    public function __construct(
        Company $company,
        int $postItemHandlingMethod,
        int $parcelHandlingMethod,
    ) {
        parent::__construct($company, [
            self::KEY_POST_ITEM_HANDLING => $postItemHandlingMethod,
            self::KEY_PARCEL_HANDLING => $parcelHandlingMethod,
        ]);
    }

    public function getHandlingSettingByType(string $type): int
    {
        return $this->value[$type] ?? self::VALUE_ITEM_SCAN_ONLY;
    }

    public function getHandlingSetting(MailroomPostItemData $postItem): string
    {
        return match (
            [
                $this->getHandlingSettingByType(
                    $postItem->isParcel()
                        ? self::KEY_PARCEL_HANDLING
                        : self::KEY_POST_ITEM_HANDLING
                ),
                $postItem->isParcel()
            ]) {
            [self::VALUE_ITEM_SCAN_ONLY, false] => self::POST_ITEM_SCAN_ONLY,
            [self::VALUE_ITEM_SCAN_AND_COLLECT, false] => self::POST_ITEM_SCAN_AND_COLLECT,
            [self::VALUE_ITEM_SCAN_AND_POST, false] => self::POST_ITEM_SCAN_AND_POST,
            [self::VALUE_PARCEL_COLLECT, true] => self::PARCEL_COLLECT,
            [self::VALUE_PARCEL_POST, true] => self::PARCEL_POST,
            default => throw new \InvalidArgumentException(\sprintf('Invalid handling setting for post item %s', $postItem->getId())),
        };
    }

    public function setHandlingSettingByType(string $type, int $value): void
    {
        $this->value[$type] = $value;
    }
}
