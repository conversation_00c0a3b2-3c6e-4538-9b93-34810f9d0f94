<?php

declare(strict_types=1);

namespace MailScanModule\Deciders;

use Entities\Service;
use Framework\FNode;
use MailScanModule\Exceptions\NotAMailboxProductException;
use Models\Products\Product;

class MailboxTierDecider
{
    public const NO_SERVICE = 0;
    public const TIER_1 = 1;
    public const TIER_2 = 2;
    public const TIER_3 = 3;
    public const TIER_4 = 4;
    public const MAXIMUM_TIER = 4;

    public const MAILBOX_LITE = 'Mailbox Lite';
    public const MAILBOX_STANDARD = 'Mailbox Standard';
    public const MAILBOX_PREMIUM = 'Mailbox Premium';
    public const MAILBOX_BUSINESS = 'Mail Forwarding service';

    public const MAILBOX_BUSINESS_ADDRESS_PRODUCTS = [
        Product::PRODUCT_MAILBOX_BUSINESS_ADDRESS_INITIAL,
        Product::PRODUCT_MAILBOX_BUSINESS_ADDRESS_INITIAL_UPSELL,
        Product::PRODUCT_MAILBOX_BUSINESS_ADDRESS_RENEWAL,
    ];

    public function __construct(
    ) {
    }

    /**
     * @param Service $service
     *
     * @return bool
     */
    public function isTier1(Service $service): bool
    {
        return $service->getServiceTypeId() === Service::TYPE_REGISTERED_OFFICE
            && !$service->isMailboxBusinessAddressService();
    }

    /**
     * @param Service $service
     *
     * @return bool
     */
    public function isTier2(Service $service): bool
    {
        return $service->isMailboxStandardService();
    }

    /**
     * @param Service $service
     *
     * @return bool
     */
    public function isTier3(Service $service): bool
    {
        return $service->isMailboxPremiumService();
    }

    /**
     * @param Service $service
     *
     * @return bool
     */
    public function isTier4(Service $service): bool
    {
        return $service->isMailboxBusinessAddressService();
    }

    /**
     * @throws NotAMailboxProductException
     */
    public function determineMailboxTier(Service $service): ?int
    {
        return match (true) {
            $this->isTier4($service) => self::TIER_4,
            $this->isTier3($service) => self::TIER_3,
            $this->isTier2($service) => self::TIER_2,
            $this->isTier1($service) => self::TIER_1,
            default => null,
        };
    }

    /**
     * @throws NotAMailboxProductException
     */
    public function getTierName(Service|FNode $service): ?string
    {
        return $this->getNameFromTier($this->determineMailboxTier($service));
    }

    public function getNameFromTier(int $tier): ?string
    {
        return match ($tier) {
            self::TIER_1 => self::MAILBOX_LITE,
            self::TIER_2 => self::MAILBOX_STANDARD,
            self::TIER_3 => self::MAILBOX_PREMIUM,
            self::TIER_4 => self::MAILBOX_BUSINESS,
            default => null,
        };
    }
}
