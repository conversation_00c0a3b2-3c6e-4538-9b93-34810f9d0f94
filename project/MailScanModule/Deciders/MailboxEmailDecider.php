<?php

declare(strict_types=1);

namespace MailScanModule\Deciders;

use MailScanModule\Dto\MailboxEmailData;
use MailScanModule\Enums\StatusEnum;
use MailScanModule\Providers\MailboxEmailNameProvider;

readonly class MailboxEmailDecider
{
    public const PARCEL_WAITING_PAYMENT_REMINDER_DAYS = 3;
    public const PARCEL_RELEASED_COLLECT_REMINDER_DAYS = 3;

    public function reminderShouldBeSent(MailboxEmailData $emailData, ?string $emailName): bool
    {
        if (
            $emailData->itemStatus === StatusEnum::STATUS_TO_BE_SECURELY_DESTROYED->value
            || $emailData->itemStatus === StatusEnum::STATUS_SECURELY_DESTROYED->value
        ) {
            return false;
        }

        if (is_null($emailName)) {
            return false;
        }

        if (!$this->emailHasReminderTemplate($emailName)) {
            return false;
        }

        if ($emailData->lastEmailSent !== $emailName) {
            return false;
        }

        $days = match ($emailName) {
            MailboxEmailNameProvider::PARCEL_WAITING_PAYMENT_POST => self::PARCEL_WAITING_PAYMENT_REMINDER_DAYS,
            MailboxEmailNameProvider::PARCEL_RELEASED_COLLECT => self::PARCEL_RELEASED_COLLECT_REMINDER_DAYS,
            default => null,
        };

        if (is_null($days)) {
            return false;
        }

        return $this->hasDate($emailData->itemReceivedDate, $days);
    }

    private function hasDate(?\DateTime $date, int $days): bool
    {
        if (is_null($date)) {
            return false;
        }

        return new \DateTime() > (clone $date)->modify("$days days");
    }

    private function emailHasReminderTemplate(string $emailName): bool
    {
        return in_array($emailName, MailboxEmailNameProvider::REMINDER_EMAILS)
            || key_exists($emailName, MailboxEmailNameProvider::REMINDER_EMAILS);
    }
}
