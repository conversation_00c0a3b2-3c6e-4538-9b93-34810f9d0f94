<?php

declare(strict_types=1);

namespace MailScanModule\Commands;

use BasketModule\Basket;
use CompanyModule\Entities\Settings\PostItemHandlingSetting;
use CompanyModule\Facades\MailForwardingFacade;
use CompanyModule\Facades\PostItemHandlingFacade;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;
use Entities\Service;
use Exceptions\Business\BasketException;
use Exceptions\Technical\NodeException;
use MailScanModule\ApiClient\IMailroomApiClient;
use MailScanModule\ApiClient\MailroomApiClient;
use MailScanModule\Deciders\MailboxTierDecider;
use MailScanModule\Deciders\ReleaseItemDecider;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Dto\PostItemBag;
use MailScanModule\Emailers\MailboxEmailer;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Enums\StatusEnum;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use MailScanModule\Services\PostItemService;
use Models\Products\BasketProduct;
use Models\Products\Product;
use PaymentModule\Exceptions\PaymentException;
use PaymentModule\Services\ChargeService;
use Psr\Log\LoggerInterface;
use Services\CompanyService;
use Services\NodeService;

class MailboxPostItemProcessor
{
    public const DEBUG_KEY_NO_COMPANY_ID = 'no-company-id';
    public const DEBUG_KEY_NO_ID_CHECK = 'no-id-check';
    public const DEBUG_KEY_SERVICE_OVERDUE = 'service-overdue';
    public const DEBUG_KEY_NO_SERVICE = 'no-service';
    public const DEBUG_KEY_UNPAID_CHARGES = 'unpaid-charges';
    public const DEBUG_KEY_RELEASED_WITH_CHARGES = 'released-with-charges';
    public const DEBUG_KEY_RELEASED = 'released';
    public const DEBUG_KEY_RELEASED_WITHOUT_CHARGES = 'released-without-charges';
    public const DEBUG_KEY_FAILED_TO_CHARGE = 'failed-to-charge';
    public const DEBUG_KEY_RTS = 'rts';
    public const DEBUG_KEY_WAITING_PAYMENT = 'waiting-payment';
    public const DEBUG_KEY_TOTAL = 'total';

    public const TEST_KEY_TOTAL_QUOTAS_AMOUNT = 'total-quotas-amount';
    public const TEST_KEY_COMPANIES_FROM_TIER_PREFIX = 'companies-processed-as-tier-';
    public const TEST_KEY_EXTRA_QUOTA_CHARGES = 'extra-quota-charges';
    public const TEST_KEY_FORWARDING_CHARGES = 'forwarding-charges';
    public const TEST_KEY_HANDLING_FEE_CHARGES = 'handling-fee-charges';

    public const TEST_KEYS = [
        self::TEST_KEY_TOTAL_QUOTAS_AMOUNT,
        self::TEST_KEY_COMPANIES_FROM_TIER_PREFIX,
        self::TEST_KEY_EXTRA_QUOTA_CHARGES,
        self::TEST_KEY_FORWARDING_CHARGES,
        self::TEST_KEY_HANDLING_FEE_CHARGES,
    ];

    protected array $processingTotals = [];
    private bool $retryPayments;
    private int $tryOne;
    private bool $dryRun;
    private bool $debug;

    public function __construct(
        //      @TODO: Use the AuditProcessScansService class to monitor results of the process.
        private readonly ChargeService $paymentService,
        private readonly CompanyService $companyService,
        private readonly IMailroomApiClient $mailroomApiClient,
        private readonly LoggerInterface $logger,
        private readonly MailboxTierDecider $mailboxTierDecider,
        private readonly MailForwardingFacade $mailForwardingFacade,
        private readonly NodeService $nodeService,
        private readonly MailboxEmailer $emailer,
        private readonly PostItemHandlingFacade $postItemHandlingFacade,
        private readonly ReleaseItemDecider $releaseItemDecider,
        private readonly PostItemService $postItemService,
    ) {
    }

    /**
     * @throws NodeException
     * @throws NonUniqueResultException
     * @throws BasketException
     */
    public function process(
        bool $retryPayments = false,
        int $tryOne = 0,
        bool $dryRun = true,
        bool $debug = false,
    ): array {
        $this->retryPayments = $retryPayments;
        $this->tryOne = $tryOne;
        $this->dryRun = $dryRun;
        $this->debug = $debug;

        $this->logger->info('Initiating MailboxPostItemProcessor command...');

        /** @var PostItemBag|null $itemBag */
        $itemBag = $this->mailroomApiClient->getUnprocessedPostItems();

        if (is_null($itemBag)) {
            $this->logger->info('No post items to process.');

            return $this->processingTotals;
        }

        $this->processingTotals = [
            self::DEBUG_KEY_TOTAL => $itemBag->countAllPostItems(),
        ];

        $this->logger->info(sprintf('Processing %s items...', $this->processingTotals[self::DEBUG_KEY_TOTAL]));

        $itemsPerCompany = $this->organizeItemsByCompanies($itemBag);

//        $this->logDebuggingInfo(
//            'Items per company',
//            [
//                '("company number": count)' => json_encode(
//                    $itemsPerCompany->map(
//                        fn ($items) => count($items)
//                    )->toArray()
//                ),
//            ],
//            true
//        );

        $this->processItems($itemsPerCompany);

        $this->logger->info(sprintf(
            'Processing totals: %s',
            json_encode(
                $this->processingTotals,
                JSON_PRETTY_PRINT
            )
        ));

        return $this->processingTotals;
    }

    /**
     * @throws NonUniqueResultException
     */
    public function isParcelTypeAndPostSetting(MailroomPostItemData $item, Company $company, int $tier, BasketProduct $product): bool
    {
        return $item->getType() === PostItemTypeEnum::TYPE_PARCEL->value
            && PostItemHandlingSetting::VALUE_PARCEL_POST ===
            $this->postItemHandlingFacade->getHandlingSettingByType(
                $company,
                $item,
                $product,
                $tier
            );
    }

    /**
     * @throws NonUniqueResultException
     * @throws NodeException
     * @throws BasketException
     * @throws \Exception
     */
    protected function processItems(Collection $itemsPerCompany): void
    {
        /** @var Collection $itemsCollection */
        foreach ($itemsPerCompany->getIterator() as $companyNumber => $itemsCollection) {
            /** @var MailroomPostItemData[] $items */
            $items = $itemsCollection->toArray();
            $company = $this->companyService->getCompanyByCompanyNumber($companyNumber);

            if (empty($company)) {
                $this->logger->error(
                    sprintf(
                        'Company %s not found in our system and an item should not have been matched for it. ' .
                        'Skipping this company.',
                        $companyNumber
                    )
                );
                $this->updateProcessingTotals(self::DEBUG_KEY_NO_COMPANY_ID, count($items));
                continue;
            }

            $this->logDebuggingInfo(
                'Processing items for company',
                [
                    'company_number' => $companyNumber,
                    'company_id' => $company->getId(),
                    'item_count' => count($items),
                ],
                true
            );

            if (!$this->releaseItemDecider->isIdCheckCompleted($company)) {
                $this->logger->info(
                    'ID check not completed for this company. Emailing the customer and Skipping this company.'
                );

                $this->emailer->sendOneEmailForEachItem(
                    $company,
                    $items,
                    MailboxEmailer::PROCESSING_STEP_NO_ID_CHECK,
                    $this->dryRun
                );

                $this->updateProcessingTotals(self::DEBUG_KEY_NO_ID_CHECK, count($items));
                continue;
            }

            $service = $company->getActiveOrLatestMailboxService();
            if ($service && $service->isOverdueAndExpired()) {
                $this->logger->info(sprintf(
                    'Mailbox service overdue for company %s. Emailing the customer and Skipping this company.',
                    $companyNumber
                ));
                $this->emailer->sendOneEmailForEachItem(
                    $company,
                    $items,
                    MailboxEmailer::PROCESSING_STEP_SERVICE_OVERDUE,
                    $this->dryRun
                );
                $this->updateProcessingTotals(self::DEBUG_KEY_SERVICE_OVERDUE, count($items));
                continue;
            }

            if (!$service || !$service->isActive()) {
                $this->logger->info(
                    'No active mailbox service found for this company. Emailing the customer and Skipping this company.',
                );
                $this->emailer->sendOneEmailForEachItem(
                    $company,
                    $items,
                    MailboxEmailer::PROCESSING_STEP_NO_MAILBOX_SERVICE,
                    $this->dryRun
                );
                $this->updateProcessingTotals(self::DEBUG_KEY_NO_SERVICE, count($items));
                continue;
            }

            $tier = $this->mailboxTierDecider->determineMailboxTier($service);
            $this->logDebuggingInfo(
                'Determined mailbox tier',
                [
                    'tier' => $tier,
                    'company_number' => $companyNumber,
                    'company_id' => $company->getId(),
                ],
                true
            );

            $this->updateProcessingTotals(self::TEST_KEY_COMPANIES_FROM_TIER_PREFIX . $tier);

            $product = $this->postItemService->getProductByTier($tier, $company);

            /** @var Collection|MailroomPostItemData[] $companyItemsToCharge */
            $companyItemsToCharge = new ArrayCollection();
            /** @var Collection|MailroomPostItemData[] $itemsToRts */
            $itemsToRts = new ArrayCollection();
            /** @var Collection|MailroomPostItemData[] $itemsToWaitForPayment */
            $itemsToWaitForPayment = new ArrayCollection();

            foreach ($items as $item) {
                if ($this->debug) {
                    $this->logItemInfo($item, $company, $tier, $product);
                }

                if ($item->hasUnpaidQuotaCharges()) {
                    $this->logger->info('This item has an unpaid charge.');
                    if ($this->retryPayments) {
                        $this->logger->info('Charging will be retried.');
                        $companyItemsToCharge->add($item);
                    } else {
                        $this->logger->info('Skipping this item.');
                        $this->updateProcessingTotals(self::DEBUG_KEY_UNPAID_CHARGES);
                    }
                    if ($this->tryOne) {
                        $this->logger->info('Try-one mode enabled. Continuing processing this item.');

                        return;
                    }
                    continue;
                }

                if (MailboxProductPropertyHelper::getAcceptanceByType($product, $item->getType())) {
                    if ($this->isParcelTypeAndPostSetting($item, $company, $tier, $product)) {
                        $itemsToWaitForPayment->add($item);
                        $item->setDesiredStatus(StatusEnum::STATUS_WAITING_PAYMENT->value);
                        $this->logger->info('This parcel will be added to wait for payment list.');
                        continue;
                    }

                    $this->logger->info('This item will be added to the list of items to be charged.');
                    $companyItemsToCharge->add($item);

                    if ($this->tryOne) {
                        $this->logger->info('Try-one mode enabled. Continuing processing this item.');

                        return;
                    }
                    continue;
                }

                if (PostItemTypeEnum::from($item->getType()) === PostItemTypeEnum::TYPE_PARCEL) {
                    $this->logger->info('This item will be added to the list of items to be RTS.');
                    $item->setDesiredStatus(StatusEnum::STATUS_TO_BE_RTS->value);
                    $itemsToRts->add($item);
                    continue;
                }

                $this->logger->info('This item will be added to the list of items to wait for payment.');
                $itemsToWaitForPayment->add($item);
            }

            $totalItemsToCharge = $companyItemsToCharge->count();
            if ($totalItemsToCharge === 0) {
                $this->logger->info(sprintf('No items to charge for company number %s.', $companyNumber));
            } elseif ($this->handleChargingAndQuotas($company, $companyItemsToCharge, $product, $tier)) {
                $this->handleReleasedPostItems($company, $companyItemsToCharge);
                $this->updateProcessingTotals(self::DEBUG_KEY_RELEASED, $totalItemsToCharge);
            } else {
                $this->logger->info('Failed to charge!');
                if (!$this->dryRun) {
                    $this->handleFailedChargedPostItems($company, $service, $companyItemsToCharge);
                }
                $this->updateProcessingTotals(self::DEBUG_KEY_FAILED_TO_CHARGE, $totalItemsToCharge);
            }

            if ($itemsToRts->isEmpty()) {
                $this->logger->info(sprintf('No items to RTS for company number %s.', $companyNumber));
            } else {
                $this->handleRts($company, $itemsToRts);
            }

            if ($itemsToWaitForPayment->isEmpty()) {
                $this->logger->info(
                    sprintf('No items to wait for payment for company number %s.', $companyNumber)
                );
            } else {
                $this->handleWaitForPayment($company, $product, $itemsToWaitForPayment);
            }

            if ($this->tryOne) {
                $this->logger->info('Try-one mode enabled. Continuing processing this item.');

                return;
            }

            $this->logger->info(sprintf('Finished processing post items for company number %s.', $companyNumber));
        }

        $this->logger->info('Finished MailboxPostItemProcessor command.');
    }

    private function organizeItemsByCompanies(PostItemBag $itemBag): Collection
    {
        $this->logger->info('Separating items by customer...');

        $itemsPerCompany = new ArrayCollection();
        $allPostItems = $itemBag->getAllPostItems();

        // Group items by company number using a more memory-efficient approach
        foreach ($allPostItems as $postItem) {
            $companyNumber = $postItem->getCompanyNumber();

            if (!$itemsPerCompany->containsKey($companyNumber)) {
                $itemsPerCompany->set($companyNumber, new ArrayCollection());
            }

            $itemsPerCompany->get($companyNumber)->add($postItem);
        }

        return $itemsPerCompany;
    }

    /**
     * @throws BasketException
     * @throws NodeException
     * @throws NonUniqueResultException
     */
    private function handleChargingAndQuotas(Company $company, Collection $companyItemsToCharge, BasketProduct $product, int $tier): bool
    {
        $this->logger->info('Charging items...');
        $basket = new Basket();
        /** @var MailroomPostItemData $chargeItem */
        foreach ($companyItemsToCharge as $chargeItem) {
            $processingMethod = $this->postItemHandlingFacade->getHandlingSettingByType($company, $chargeItem, $product, $tier);
            $processingMethodAsString = MailboxProductPropertyHelper::getProcessingMethodAsString($chargeItem->getType(), $processingMethod);
            $desiredStatus = MailboxProductPropertyHelper::DESIRED_STATUSES[$processingMethodAsString];
            $chargeItem->setDesiredStatus($desiredStatus);

            if ($chargeItem->hasUnpaidQuotaCharges()) {
                $this->logDebuggingInfo(
                    'This item has unpaid quota charges.',
                    [
                        'item_id' => $chargeItem->getId(),
                    ]
                );

                $handlingFee = floatval($chargeItem->getDetail('handling_fee'));
                if ($handlingFee > 0) {
                    $this->addProductToBasket($basket, Product::MAILBOX_POST_ITEM_HANDLING_FEE, $handlingFee);
                }

                $forwardingFee = floatval($chargeItem->getDetail('forwarding_fee'));
                if ($forwardingFee > 0) {
                    $this->addProductToBasket($basket, Product::MAILBOX_POST_ITEM_FORWARDING_FEE, $forwardingFee);
                }

                $extraQuotaFee = floatval($chargeItem->getDetail('extra_quota_fee'));
                if ($extraQuotaFee > 0) {
                    $this->addProductToBasket(
                        $basket,
                        Product::PRODUCT_EXTRA_QUOTA_MAIL_FORWARDING_SERVICE,
                        $extraQuotaFee
                    );
                }

                continue;
            }

            $extraQuotaFee = 0;
            if ($this->postItemService->hasUnlimitedQuotas($product, $chargeItem, $processingMethod)) {
                $this->logDebuggingInfo(
                    'Company service has unlimited quotas.',
                    [
                        'type' => $chargeItem->getType(),
                    ],
                    true
                );
            } elseif ($this->postItemService->isWithinQuotaMaximum($company, $product, $chargeItem, $processingMethod)) {
                $newQuotaAmount = $this->mailForwardingFacade->addQuotas(
                    $company,
                    1,
                    $chargeItem->getType(),
                    $processingMethodAsString,
                    !$this->dryRun
                );

                $this->logDebuggingInfo(
                    'Company service has enough quotas.',
                    [
                        'type' => $chargeItem->getType(),
                        'new_quota_amount' => $newQuotaAmount,
                    ],
                    true
                );

                $this->updateProcessingTotals(self::TEST_KEY_TOTAL_QUOTAS_AMOUNT);
            } else {
                $extraQuotaFee = MailboxProductPropertyHelper::getExtraQuotaFeeByFormat(
                    $product,
                    MailboxProductPropertyHelper::FORMATS[$chargeItem->getFormat()]
                );

                if ($extraQuotaFee > 0) {
                    $this->addProductToBasket(
                        $basket,
                        Product::PRODUCT_EXTRA_QUOTA_MAIL_FORWARDING_SERVICE,
                        $extraQuotaFee
                    );

                    $this->updateProcessingTotals(self::TEST_KEY_EXTRA_QUOTA_CHARGES);
                }
            }

            $handlingFee = MailboxProductPropertyHelper::getHandlingFeeByFormat(
                $product,
                MailboxProductPropertyHelper::FORMATS[$chargeItem->getFormat()]
            );

            if ($handlingFee > 0) {
                $this->addProductToBasket(
                    $basket,
                    Product::MAILBOX_POST_ITEM_HANDLING_FEE,
                    $handlingFee
                );

                $this->updateProcessingTotals(self::TEST_KEY_HANDLING_FEE_CHARGES);
            }

            $forwardingFee = 0;
            if (
                MailboxProductPropertyHelper::getProcessingMethodAsString($chargeItem->getType(), $processingMethod)
                === MailboxProductPropertyHelper::PROCESSING_METHOD_POST
            ) {
                $forwardingFee = MailboxProductPropertyHelper::getForwardingFeeByFormat(
                    $product,
                    MailboxProductPropertyHelper::FORMATS[$chargeItem->getFormat()]
                );
                if ($forwardingFee > 0) {
                    $this->addProductToBasket(
                        $basket,
                        Product::MAILBOX_POST_ITEM_FORWARDING_FEE,
                        $forwardingFee
                    );

                    $this->updateProcessingTotals(self::TEST_KEY_FORWARDING_CHARGES);
                }
            }

            $chargeItem->setDetail('handling_fee', number_format($handlingFee, 2));
            $chargeItem->setDetail('forwarding_fee', number_format($forwardingFee, 2));
            $chargeItem->setDetail('extra_quota_fee', number_format($extraQuotaFee, 2));
        }

        if ($basket->isEmpty()) {
            $this->logDebuggingInfo(
                'No charges for company.',
                [
                    'company_number' => $company->getCompanyNumber(),
                    'company_id' => $company->getCompanyId(),
                ],
                true
            );
            $this->updateProcessingTotals(self::DEBUG_KEY_RELEASED_WITHOUT_CHARGES);
            return true;
        }

        $basketTotal = $basket->getPrice('total', true, $company->getCustomer());

        if ($this->dryRun) {
            $this->logDebuggingInfo(
                '[DRY RUN MODE]: Would have charged company.',
                [
                    'company_number' => $company->getCompanyNumber(),
                    'company_id' => $company->getCompanyId(),
                    'item_count' => $companyItemsToCharge->count(),
                    'total' => $basketTotal,
                ],
                true
            );

            return true;
        }

        $this->logDebuggingInfo(
            'Charging company...',
            [
                'company_number' => $company->getCompanyNumber(),
                'company_id' => $company->getCompanyId(),
                'item_count' => $companyItemsToCharge->count(),
                'total' => $basketTotal,
            ],
            true
        );

        try {
            $paymentResponse = $this->paymentService->chargeBasket($company->getCustomer(), $basket, 'Mailbox Post Item Charges')->isSuccessful();
            $this->logDebuggingInfo(
                'Charged company.',
                [
                    'company_number' => $company->getCompanyNumber(),
                    'company_id' => $company->getCompanyId(),
                    'item_count' => $companyItemsToCharge->count(),
                    'total' => $basketTotal,
                ],
                true
            );
            $this->updateProcessingTotals(self::DEBUG_KEY_RELEASED_WITH_CHARGES);

            return (bool) $paymentResponse;
        } catch (PaymentException $e) {
            $this->logDebuggingInfo(
                'Failed to charge company.',
                [
                    'company_number' => $company->getCompanyNumber(),
                    'company_id' => $company->getCompanyId(),
                    'item_count' => $companyItemsToCharge->count(),
                    'total' => $basketTotal,
                    'error_message' => $e->getMessage(),
                ],
                true
            );

            return false;
        }
    }

    /**
     * @throws NodeException
     * @throws BasketException
     */
    private function addProductToBasket(Basket &$basket, string $productName, float $price): void
    {
        $this->logDebuggingInfo(
            'Adding product to basket.',
            ['product_name' => $productName, 'price' => $price]
        );
        $product = $this->nodeService->requiredProductByName($productName);
        $product->setAllPrices($price);
        $basket->add($product);
    }

    /**
     * @throws \Exception
     */
    private function handleReleasedPostItems(Company $company, Collection $items): void
    {
        $this->logDebuggingInfo(
            'Setting released statuses on the Mailroom database',
            [
                array_combine(
                    array_map(fn ($item) => $item->getId(), $items->toArray()),
                    array_map(fn ($item) => $item->getDesiredStatus(), $items->toArray())
                ),
            ],
            true
        );

        if (!$this->dryRun) {
            array_map(function (MailroomPostItemData $item) {
                $item->setStatus($item->getDesiredStatus());
            }, $items->toArray());

            $this->mailroomApiClient->setPostItemsAsReleased($items->toArray());
        }

        $this->emailer->sendOneEmailForEachItem(
            $company,
            $items->toArray(),
            MailboxEmailer::PROCESSING_STEP_RELEASED,
            $this->dryRun
        );
    }

    /**
     * @throws \Exception
     */
    private function handleFailedChargedPostItems(Company $company, Service $service, Collection $items): void
    {
        $this->logDebuggingInfo(
            'Setting charging attempts and fees on the Mailroom database',
            [
                'totalItems' => $items->count(),
            ],
            true
        );

        if (!$this->dryRun) {
            /** @var MailroomPostItemData $item */
            foreach ($items as $item) {
                $item->setDetail(
                    MailroomApiClient::CHARGING_ATTEMPTS_DETAIL_NAME,
                    (string) (intval($item->getDetail(MailroomApiClient::CHARGING_ATTEMPTS_DETAIL_NAME)) + 1)
                );

                $item->setDetail(
                    MailroomApiClient::HANDLING_FEE_DETAIL_NAME,
                    number_format(floatval($item->getDetail(MailroomApiClient::HANDLING_FEE_DETAIL_NAME)), 2)
                );

                $item->setDetail(
                    MailroomApiClient::FORWARDING_FEE_DETAIL_NAME,
                    number_format(floatval($item->getDetail(MailroomApiClient::FORWARDING_FEE_DETAIL_NAME)), 2)
                );

                $item->setDetail(
                    MailroomApiClient::EXTRA_QUOTA_FEE_DETAIL_NAME,
                    number_format(floatval($item->getDetail(MailroomApiClient::EXTRA_QUOTA_FEE_DETAIL_NAME)), 2)
                );
            }

            $this->mailroomApiClient->setFailedChargeAttempts($items->toArray());
        }

        $this->emailer->sendOneEmailForEachItem(
            $company,
            $items->toArray(),
            MailboxEmailer::PROCESSING_STEP_FAILED_TO_CHARGE,
            $this->dryRun
        );
    }

    /**
     * @throws \Exception
     */
    private function handleRts(Company $company, Collection $items): void
    {
        $this->logDebuggingInfo(
            'Setting items as to RTS on the Mailroom database',
            [
                'totalItems' => $items->count(),
            ],
            true
        );

        if (!$this->dryRun) {
            array_map(function (MailroomPostItemData $item) {
                $item->setStatus($item->getDesiredStatus());
            }, $items->toArray());

            $this->mailroomApiClient->setPostItemsAsRts($items->toArray());
        }

        $this->emailer->sendOneEmailForEachItem(
            $company,
            $items->toArray(),
            MailboxEmailer::PROCESSING_STEP_RTS,
            $this->dryRun
        );
        $this->updateProcessingTotals(self::DEBUG_KEY_RTS, $items->count());
    }

    /**
     * @throws \Exception
     */
    private function handleWaitForPayment(Company $company, BasketProduct $tierProduct, Collection $items): void
    {
        $this->logDebuggingInfo(
            'Setting items as waiting for payment on the Mailroom database',
            [
                'totalItems' => $items->count(),
            ],
            true
        );

        if (!$this->dryRun) {
            array_map(function (MailroomPostItemData $item) use ($tierProduct) {
                $item->setStatus(StatusEnum::STATUS_WAITING_PAYMENT->value);
                $item->setDetail(
                    MailroomApiClient::PAY_TO_RELEASE_FEE_DETAIL_NAME,
                    (string) MailboxProductPropertyHelper::getPayToReleaseFeeByFormat(
                        $tierProduct,
                        MailboxProductPropertyHelper::FORMATS[$item->getFormat()]
                    )
                );
            }, $items->toArray());

            $this->mailroomApiClient->setPostItemsAsWaitingForPayment($items->toArray());
        }

        $this->emailer->sendOneEmailForEachItem(
            $company,
            $items->toArray(),
            MailboxEmailer::PROCESSING_STEP_WAITING_FOR_PAYMENT,
            $this->dryRun
        );

        $this->updateProcessingTotals(self::DEBUG_KEY_WAITING_PAYMENT, $items->count());
    }

    /**
     * @throws NonUniqueResultException
     */
    private function logItemInfo(MailroomPostItemData $item, Company $company, ?int $tier, BasketProduct $product): void
    {
        $this->logDebuggingInfo(
            'Processing item',
            [
                'item_id' => $item->getId(),
                'item_type' => $item->getType(),
                'status' => $item->getStatus(),
                'dtc' => $item->getDtc()->format('Y-m-d H:i:s'),
                'details' => json_encode($item->getDetails()),
                'events' => json_encode($item->getEvents()),
            ]
        );

        $handlingSetting = $this->postItemHandlingFacade->getHandlingSettingByType($company, $item, $product, $tier);
        $this->logDebuggingInfo(
            'Company details',
            [
                'companyId' => $company->getId(),
                'companyNumber' => $company->getCompanyNumber(),
                'companyName' => $company->getCompanyName(),
                'tier' => $tier,
                'item_acceptance' => MailboxProductPropertyHelper::getAcceptanceByType($product, $item->getType()) ? 'yes' : 'no',
                'handlingSetting' => $handlingSetting,
                'quotas_spent' => $this->mailForwardingFacade->getQuotasByType(
                    $company,
                    $item->getType(),
                    MailboxProductPropertyHelper::getProcessingMethodAsString(
                        $item->getType(),
                        $handlingSetting
                    )
                ),
                'maximum_quotas' => MailboxProductPropertyHelper::getMaximumQuotaByTypeAndProcessingMethod(
                    $product,
                    $item->getType(),
                    $handlingSetting
                ),
            ]
        );
    }

    private function updateProcessingTotals(string $key, int $itemsCount = 1): void
    {
        if ($this->debug && in_array($key, self::TEST_KEYS)) {
            $this->processingTotals[$key] = ($this->processingTotals[$key] ?? 0) + $itemsCount;

            return;
        }

        $this->processingTotals[$key] = ($this->processingTotals[$key] ?? 0) + $itemsCount;
    }

    private function logDebuggingInfo(string $msg, ?array $data, ?bool $alwaysShow = false): void
    {
        if ($alwaysShow || $this->debug) {
            $this->logger->info(
                sprintf('%s - %s', $msg, json_encode($data))
            );
        }
    }
}
