<?php

declare(strict_types=1);

namespace MailScanModule\Commands;

use Exception;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use Models\Products\Package;
use Models\Products\Product;

class TestCaseBuilder
{
    /**
     * Build test cases from a CSV file (Completely made by an LLM)
     *
     * @param string $csvFile Path to the CSV file
     * @return array Array of test cases
     * @throws \Random\RandomException
     */
    public function buildFromCsv(string $csvFile): array
    {
        if (!file_exists($csvFile)) {
            throw new Exception("CSV file not found: {$csvFile}");
        }

        $testCases = [];
        $handle = fopen($csvFile, 'r');

        $headers = fgetcsv($handle);

        $companyNumberBase = random_int(10000000, 99990000);

        while (($data = fgetcsv($handle)) !== false) {
            $row = array_combine($headers, $data);

            foreach ($row as $key => $value) {
                if ($value === 'TRUE') {
                    $row[$key] = true;
                } elseif ($value === 'FALSE') {
                    $row[$key] = false;
                } elseif ($value === 'null') {
                    $row[$key] = null;
                }
            }

            $companyNumberBase++;

            $testCases[] = [
                'company_number'        => $companyNumberBase,
                'formation_product_id'  => $this->evaluateConstant($row['formation_product_id']),
                'mailbox_product_name'  => $this->evaluateConstant($row['mailbox_product_name']) ?? null,
                'service_overdue'       => $row['service_overdue'] ?? false,
                'post_item_type'        => $this->evaluateEnumValue($row['post_item_type']),
                'id_check_complete'     => $row['id_check_complete'] ?? true,
                'within_quota'          => $row['within_quota'] ?? true,
                'charging_attempts'     => $row['charging_attempts'],
                'handling_format'       => $this->evaluateConstant($row['handling_format']),
                'company_name'          => $row['company_name'] . '_' . "{$companyNumberBase}",
            ];
        }

        fclose($handle);
        return $testCases;
    }

    /**
     * Evaluates a string that represents a class constant
     *
     * @param string|null $constantString
     * @return mixed
     */
    private function evaluateConstant($constantString)
    {
        if ($constantString === null || $constantString === 'null') {
            return null;
        }

        $constantsMap = [
            'Package::PACKAGE_BASIC' => Package::PACKAGE_BASIC,
            'Package::PACKAGE_PRIVACY' => Package::PACKAGE_PRIVACY,
            'Package::PACKAGE_FULL_PRIVACY' => Package::PACKAGE_FULL_PRIVACY,
            'Package::PACKAGE_RENEWAL_PRIVACY' => Package::PACKAGE_RENEWAL_PRIVACY,

            'Product::PRODUCT_REGISTERED_OFFICE' => Product::PRODUCT_REGISTERED_OFFICE,
            'Product::PRODUCT_MAILBOX_STANDARD_RENEWAL' => Product::PRODUCT_MAILBOX_STANDARD_RENEWAL,
            'Product::PRODUCT_MAILBOX_PREMIUM_RENEWAL' => Product::PRODUCT_MAILBOX_PREMIUM_RENEWAL,
            'Product::PRODUCT_MAILBOX_BUSINESS_ADDRESS_RENEWAL' => Product::PRODUCT_MAILBOX_BUSINESS_ADDRESS_RENEWAL,

            'MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED' => MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,
            'MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT' => MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT,
            'MailboxProductPropertyHelper::PROCESSING_METHOD_POST' => MailboxProductPropertyHelper::PROCESSING_METHOD_POST,
        ];

        if (isset($constantsMap[$constantString])) {
            return $constantsMap[$constantString];
        }

        return $constantString;
    }

    /**
     * Evaluates a string that represents an enum value
     *
     * @param string $enumValueString
     * @return mixed
     */
    private function evaluateEnumValue($enumValueString)
    {
        $enumValuesMap = [
            'PostItemTypeEnum::TYPE_STATUTORY->value' => PostItemTypeEnum::TYPE_STATUTORY->value,
            'PostItemTypeEnum::TYPE_NON_STATUTORY->value' => PostItemTypeEnum::TYPE_NON_STATUTORY->value,
            'PostItemTypeEnum::TYPE_PARCEL->value' => PostItemTypeEnum::TYPE_PARCEL->value,
        ];

        if (isset($enumValuesMap[$enumValueString])) {
            return $enumValuesMap[$enumValueString];
        }

        return $enumValueString;
    }
}