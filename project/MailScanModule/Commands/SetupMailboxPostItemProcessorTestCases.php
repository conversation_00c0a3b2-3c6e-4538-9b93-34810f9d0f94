<?php

namespace MailScanModule\Commands;

use CompanyModule\Facades\MailForwardingFacade;
use CompanyModule\Facades\PostItemHandlingFacade;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;
use Entities\Customer;
use Entities\Order;
use Entities\OrderItem;
use IdModule\Dto\IdValidationAdmin;
use IdModule\Repositories\IdInfoRepository;
use IdModule\Verification\IdValidator;
use Libs\Exceptions\EntityNotFound;
use MailScanModule\ApiClient\IMailroomApiClient;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use Models\Products\Product;
use Psr\Log\LoggerInterface;
use Random\RandomException;
use Services\CustomerService;
use tests\helpers\CompanyHelper;
use tests\helpers\CustomerHelper;
use tests\helpers\ObjectHelper;
use Utils\Date;

class SetupMailboxPostItemProcessorTestCases
{
    private const MOCK_CUSTOMER_EMAIL = '<EMAIL>';
    private const MOCK_ADMIN_EMAIL = '<EMAIL>';
    private const MAX_QUOTA = 50;
    private array $testCases;

    /**
     * @throws RandomException
     */
    public function __construct(
        private readonly CustomerService $customerService,
        private EntityManager $entityManager,
        private readonly IdInfoRepository $idInfoRepository,
        private readonly IdValidator $idValidator,
        private readonly IMailroomApiClient $mailroomApiClient,
        private readonly LoggerInterface $logger,
        private readonly MailForwardingFacade $mailForwardingFacade,
        private readonly PostItemHandlingFacade $postItemHandlingFacade,
    ) {
        $builder = new TestCaseBuilder();
        $this->testCases = $builder->buildFromCsv(dirname(__DIR__, 2) . '/MailScanModule/Config/testScriptCases.csv');
    }

    /**
     * @throws \Exception
     */
    public function setup(string $testId = 'TEST'): void
    {
        $this->logger->info('Setting up test cases for MailboxPostItemProcessor...');

        $customer = $this->getOrCreateCustomer(
            sprintf('%s_%s', $testId, self::MOCK_CUSTOMER_EMAIL)
        );

        foreach ($this->testCases as $count => $testCase) {
            $this->logger->info(
                sprintf('Setting up test case %s/%s...', $count + 1, count($this->testCases))
            );

            if (is_null($testCase['company_name'])) {
                $testCase['company_name'] = sprintf(
                    '%s_%s',
                    $testId,
                    $testCase['company_number']
                );
            }
            if ($this->setupTestCase($testCase, $customer)) {
                $this->logger->info(sprintf('Test case %s setup successfully!', $count + 1));
            } else {
                $this->logger->info(sprintf('Failed to setup test case %s!', $count + 1));
            }
        }
    }

    public function validateCompanyIds(?Company $company): bool
    {
        try {
            foreach ($this->idInfoRepository->getIdInfoForCompany($company)->getEntities() as $entityInfo) {
                $entityInfo = $this->idInfoRepository->optionalIdInfoForEntityById($company, $entityInfo->getId());

                $validation = new IdValidationAdmin();
                $validation->status = true;
                $validation->message = IdValidationAdmin::AUTOMATED_VALIDATION_MESSAGE;
                $validation->notifyCustomer = false;

                foreach ($entityInfo->getCheckNames() as $key => $checkName) {
                    $validation->checkName = $checkName;

                    $this->idValidator->validateManually(
                        $entityInfo->getEntity(),
                        $validation,
                        self::MOCK_ADMIN_EMAIL,
                    );
                }
            }
        } catch (\Exception $e) {
            $this->logger->info(sprintf('Failed to validate IDs for company: %s!', $company->getId()));

            return false;
        }

        return true;
    }

    public function createMockOrderItem(\Entities\Customer $customer, ?Company $company, Product $product): OrderItem
    {
        $orderItem = new OrderItem(new Order($customer));
        $orderItem->setIsFee(false);
        $orderItem->setProductTitle('Mailbox Service');
        $orderItem->setPrice(0);
        $orderItem->setQty(1);
        $orderItem->setNotApplyVat(false);
        $orderItem->setNonVatableValue(0);
        $orderItem->setCompany($company);
        $orderItem->setSubTotal(0);
        $orderItem->setVat(0);
        $orderItem->setTotalPrice(0);
        $orderItem->setIncorporationRequired(false);
        $orderItem->setProduct($product);

        return $orderItem;
    }

    private function setupTestCase(array $testCase, $customer): bool
    {
        try {
            $companyCreation = CompanyHelper::createFullCompany(
                customer: $customer,
                companyName: $testCase['company_name'],
                companyNumber: $testCase['company_number'],
                productId: $testCase['formation_product_id'],
                includeRegisteredOffice: false
            );

            foreach ($companyCreation as $entity) {
                try {
                    $this->entityManager->persist($entity);
                } catch (\Doctrine\ORM\ORMInvalidArgumentException $e) {
                    // Service will be null and throw an exception that we ignore.
                }
            }
            $this->entityManager->flush();

            $company = $this->includeMailboxService(
                $customer,
                $companyCreation['company'],
                $testCase['mailbox_product_name'],
                $testCase['service_overdue']
            );

            if ($testCase['id_check_complete']) {
                $this->logger->info('Validating company IDs...');
                if ($this->validateCompanyIds($company)) {
                    $this->logger->info('Company IDs validated successfully!');
                } else {
                    $this->logger->info('Failed to validate company IDs!');

                    return false;
                }
            }

            if ($testCase['mailbox_product_name'] !== null) {
                $this->logger->info('Set handling settings...');

                $this->postItemHandlingFacade->setHandlingSettingByType(
                    company: $company,
                    type: $testCase['post_item_type'],
                    value: $this->handlingSettingByTypeAndFormat(
                        $testCase['post_item_type'],
                        $testCase['handling_format']
                    ),
                );

                if (isset($testCase['within_quota']) && !$testCase['within_quota']) {
                    $this->logger->info('Setting maximum quota...');
                    $this->setMaximumQuota($company, $testCase);
                }
            }

            $this->logger->info('Adding post item...');
            $this->mailroomApiClient->addPostItem(
                companyNumber: $company->getCompanyNumber(),
                companyName: $company->getCompanyName(),
                typeMail: $testCase['post_item_type'],
                operator: 'CMS-Testing',
                events: $testCase['events'] ?? [],
                details: $this->setDefaultDetails($testCase),
            );

            return true;
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage() . $e->getTraceAsString());

            return false;
        }
    }

    /**
     * @throws EntityNotFound
     * @throws \Exception
     */
    private function getOrCreateCustomer(string $email): \Entities\Customer
    {
        $customer = $this->customerService->getCustomerByEmail($email);

        if (is_null($customer)) { // @phpstan-ignore-line
            $customer = CustomerHelper::createCustomer($email);
        }

        $customer->setMailboxAttachmentsEnabled(false);

        return $customer;
    }

    private function handlingSettingByTypeAndFormat(
        string $type,
        string $format,
    ): int {
        return match (true) {
            (($type === PostItemTypeEnum::TYPE_STATUTORY->value || $type === PostItemTypeEnum::TYPE_NON_STATUTORY->value)
                && $format === MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED)
            || ($type === PostItemTypeEnum::TYPE_PARCEL->value
            && $format === MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT) => 1,
            (($type === PostItemTypeEnum::TYPE_STATUTORY->value || $type === PostItemTypeEnum::TYPE_NON_STATUTORY->value)
            && $format === MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT)
            || ($type === PostItemTypeEnum::TYPE_PARCEL->value
            && $format === MailboxProductPropertyHelper::PROCESSING_METHOD_POST) => 2,
            ($type === PostItemTypeEnum::TYPE_STATUTORY->value || $type === PostItemTypeEnum::TYPE_NON_STATUTORY->value)
            && $format === MailboxProductPropertyHelper::PROCESSING_METHOD_POST => 3,
            default => throw new \InvalidArgumentException(sprintf('Unknown type "%s" or format "%s"', $type, $format)),
        };
    }

    /**
     * @throws NonUniqueResultException
     */
    private function setMaximumQuota(?Company $company, array $testCase): void
    {
        $this->mailForwardingFacade->updateQuotas(
            company: $company,
            amount: self::MAX_QUOTA + 1,
            type: $testCase['post_item_type'],
            handlingOption: $testCase['handling_format'],
        );
    }

    private function setDefaultDetails(array $testCase)
    {
        $testCase['details']['test_case'] = sprintf(
            'company_number: %s, ' .
            'formation_product_id: %s, ' .
            'mailbox_product_name: %s, ' .
            'service_overdue: %s, ' .
            'post_item_type: %s, ' .
            'id_check_complete: %s, ' .
            'within_quota: %s, ' .
            'charging_attempts: %s, ' .
            'handling_format: %s',
            $testCase['company_number'],
            $testCase['formation_product_id'],
            $testCase['mailbox_product_name'] ?? 'no-service',
            $testCase['service_overdue'] ? 'yes' : 'no',
            $testCase['post_item_type'],
            $testCase['id_check_complete'] ? 'yes' : 'no',
            isset($testCase['within_quota']) ? ($testCase['within_quota'] ? 'yes' : 'no') : 'yes',
            $testCase['charging_attempts'] ?? '0',
            $testCase['handling_format'] ?? MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,
        );

        if ($testCase['charging_attempts'] && $testCase['charging_attempts'] > 0) {
            $this->logger->info('Setting charging attempts to: ' . $testCase['charging_attempts']);
            $testCase['details']['charging_attempts'] = $testCase['charging_attempts'];
            $testCase['details']['handling_fee'] = '1';
            $testCase['details']['extra_quota_fee'] = '2';
            $testCase['details']['forwarding_fee'] = '3';
        }

        return $testCase['details'];
    }

    private function includeMailboxService(
        Customer $customer,
        Company $company,
        string $mailboxServiceName = null,
        $isOverdue = false
    ): Company {
        if ($mailboxServiceName === null) {
            return $company;
        }

        $order = new Order($customer);
        $orderItem = ObjectHelper::createOrderItem($order);
        $company->setOrder($order);
        $service = ObjectHelper::createServiceFromProductName(
            $company,
            $orderItem,
            $mailboxServiceName,
            new Date('-150 days'),
            new Date('+175 days')
        );
        $service->getRenewalProductId();
        if ($isOverdue) {
            $service->setDtExpires(new Date('-5 day'));
        }
        $company->addService($service);

        return $company;
    }
}
