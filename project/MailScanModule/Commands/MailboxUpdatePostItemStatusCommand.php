<?php

declare(strict_types=1);

namespace MailScanModule\Commands;

use MailScanModule\ApiClient\IMailroomApiClient;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Dto\PostItemBag;
use MailScanModule\Emailers\MailboxEmailer;
use MailScanModule\Providers\MailboxUpdateStatusProvider;
use Psr\Log\LoggerInterface;
use Services\CompanyService;

class MailboxUpdatePostItemStatusCommand
{
    private const DEBUG_KEY_TOTAL = 'total';
    private const DEBUG_KEY_UNCHANGED = 'unchanged';
    private const DEBUG_KEY_ERRORS = 'companies_with_errors';

    protected array $updatingTotals;
    private bool $dryRun;
    private bool $debug;

    public function __construct(
        private readonly IMailroomApiClient $mailroomApiClient,
        private readonly LoggerInterface $logger,
        private readonly CompanyService $companyService,
        private readonly MailboxEmailer $emailer,
        private readonly MailboxUpdateStatusProvider $updateMailboxStatusProvider,
    ) {
        $this->updatingTotals = [];
    }

    /**
     * @throws \Exception
     */
    public function updatePostItemStatus(
        bool $dryRun = true,
        bool $debug = false,
    ): array {
        $this->dryRun = $dryRun;
        $this->debug = $debug;

        $this->logger->info('Initiating MailboxUpdatePostItemStatus command...');

        /** @var PostItemBag|null $itemBag */
        $itemBag = $this->mailroomApiClient->getRequireUpdatePostItems();

        $this->updatingTotals = [
            self::DEBUG_KEY_TOTAL => $itemBag->countAllPostItems(),
        ];

        if (empty($itemBag)) {
            $this->logger->info('No post items to update.');

            return $this->updatingTotals;
        }

        $this->logger->info(sprintf('Updating %s items...', $this->updatingTotals[self::DEBUG_KEY_TOTAL]));

        $itemsPerCompany = $this->sortItemsByCompanies($itemBag);

        $this->logProgress(
            'Items per company',
            [
                '("company number": count)' => json_encode(array_map('count', $itemsPerCompany)),
            ],
        );

        /** @var MailroomPostItemData[] $items */
        foreach ($itemsPerCompany as $companyNumber => $items) {
            try {
                $this->updateCompanyPostItemsStatus((string) $companyNumber, $items);
            } catch (\Throwable $e) {
                $this->logger->error($e->getMessage());
                $this->updateUpdatingTotals(self::DEBUG_KEY_ERRORS);
            }
        }

        $this->logger->info(sprintf(
            'Updating totals: %s',
            json_encode(
                $this->updatingTotals,
                JSON_PRETTY_PRINT
            )
        ));

        return $this->updatingTotals;
    }

    private function sortItemsByCompanies(PostItemBag $itemBag): array
    {
        $this->logger->info('Separating items by customer...');

        $companies = $itemBag->getCompaniesInBag();
        $itemsPerCompany = [];

        foreach ($companies as $companyNumber) {
            $itemsPerCompany[$companyNumber] = $itemBag->getCompanyPostItems($companyNumber);
        }

        return $itemsPerCompany;
    }

    private function logProgress(string $message, ?array $data, ?bool $suppressLog = false): void
    {
        if ($suppressLog && !$this->debug) {
            return;
        }

        $this->logger->info(
            sprintf('%s - %s', $message, json_encode($data))
        );
    }

    private function updateUpdatingTotals(string $key, int $itemsCount = 1): void
    {
        $this->updatingTotals[$key] = ($this->updatingTotals[$key] ?? 0) + $itemsCount;
    }

    /**
     * @throws \Exception
     */
    private function updateCompanyPostItemsStatus(string $companyNumber, array $items): void
    {
        $company = $this->companyService->getCompanyByCompanyNumber($companyNumber);

        if (is_null($company)) {
            $this->logger->error(
                sprintf(
                    'Company not found on CMS (company number: %s)',
                    $companyNumber
                )
            );

            return;
        }

        $this->logProgress(
            'Updating post items statuses for company',
            [
                'company number' => $companyNumber,
                'company name' => $company->getName(),
                'items count' => count($items),
            ],
        );

        /* @var MailroomPostItemData $postItem */
        foreach ($items as $postItem) {
            $this->logProgress(
                'Updating item',
                $postItem->jsonSerialize(),
                true
            );

            $newStatus = $this->updateMailboxStatusProvider->getNewStatus($postItem);

            if (!is_null($newStatus)) {
                $this->logProgress(
                    'Updating item status',
                    [
                        'post item id' => $postItem->getId(),
                        'old status' => $postItem->getStatus(),
                        'new status' => $newStatus,
                    ],
                    true
                );

                $this->updateUpdatingTotals($newStatus);
                $postItem->setDesiredStatus($newStatus);
                continue;
            }

            $this->updateUpdatingTotals(self::DEBUG_KEY_UNCHANGED);
        }

        if (!$this->dryRun) {
            $this->logProgress(
                'Updating post items status on mailroom',
                []
            );

            $this->mailroomApiClient->setPostItemStatus($items);
        }

        $this->emailer->sendOneEmailForEachItem(
            $company,
            $items,
            MailboxEmailer::PROCESSING_STEP_UPDATE,
            $this->dryRun
        );
    }
}
