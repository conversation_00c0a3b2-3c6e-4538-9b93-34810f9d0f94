<?php

declare(strict_types=1);

namespace MailScanModule\Factories;

use CompanyModule\Facades\PostItemHandlingFacade;
use Entities\Company;
use MailScanModule\ApiClient\MailroomApiClient;
use MailScanModule\Deciders\MailboxTierDecider;
use MailScanModule\Dto\MailboxEmailData;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Emailers\MailboxEmailer;
use MailScanModule\Facades\MailForwardingAddressFacade;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use MailScanModule\Services\PostItemService;

class MailboxEmailFactory
{
    public function __construct(
        private MailboxTierDecider $mailboxTierDecider,
        private PostItemHandlingFacade $postItemHandlingFacade,
        private MailForwardingAddressFacade $mailForwardingAddressFacade,
        private PostItemService $postItemService,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function createFromMailroomPostItemData(Company $company, MailroomPostItemData $item, string $processingStep): MailboxEmailData
    {
        $isReleased = $processingStep === MailboxEmailer::PROCESSING_STEP_RELEASED;
        $hasFailedToCharge = $processingStep === MailboxEmailer::PROCESSING_STEP_FAILED_TO_CHARGE;
        $lastEmailSent = $item->getDetail(MailroomApiClient::LAST_EMAIL_SENT_DETAIL_NAME);
        $hasIdCheck = $processingStep !== MailboxEmailer::PROCESSING_STEP_NO_ID_CHECK;

        $mailboxService = $company->getActiveOrLatestMailboxService();
        if (!is_null($mailboxService) && $mailboxService->isActive()) {
            $mailboxTier = $this->mailboxTierDecider->determineMailboxTier($mailboxService);
            $mailboxInitialProduct = $this->postItemService->getProductByTier($mailboxTier, $company);
            $handlingSettingValue = $this->postItemHandlingFacade->getHandlingSettingByType(
                $company,
                $item,
                $mailboxInitialProduct,
                $mailboxTier
            );
            $forwardingAddress = $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company)->getFullAddress();
            $isOverdue = $mailboxService->isOverdueAndExpired();
            $maximumQuota = MailboxProductPropertyHelper::getMaximumQuotaByTypeAndProcessingMethod(
                $mailboxInitialProduct,
                $item->getType(),
                $handlingSettingValue
            );
            $quotaPrice = MailboxProductPropertyHelper::getExtraQuotaFeeByFormat(
                $mailboxInitialProduct,
                $item->getFormat()
            );
            $releasePrice = MailboxProductPropertyHelper::getPayToReleaseFeeByFormat(
                $mailboxInitialProduct,
                $item->getFormat()
            );
            $withinQuota = $this->postItemService->isWithinQuotaMaximum(
                $company,
                $mailboxInitialProduct,
                $item,
                $handlingSettingValue
            );
        }

        return new MailboxEmailData(
            $company->getCompanyName(),
            $company->getCustomer()->getFirstName() ?? '', // @phpstan-ignore-line
            $company->getCustomer()->getFullName() ?? '', // @phpstan-ignore-line
            $item->getType(),
            $isReleased,
            $hasFailedToCharge,
            $item->getDesiredStatus() ?? $item->getStatus(),
            $item->getDtc(),
            $withinQuota ?? null,
            $lastEmailSent,
            $handlingSettingValue ?? null,
            $mailboxTier ?? null,
            $forwardingAddress ?? null,
            $maximumQuota ?? null,
            isset($quotaPrice) ? $this->getPriceLabelFromFloat($quotaPrice) : null,
            isset($releasePrice) ? $this->getPriceLabelFromFloat($releasePrice) : null,
            $isOverdue ?? null,
            $hasIdCheck
        );
    }

    private function getPriceLabelFromFloat(float $price): string
    {
        return sprintf(
            '£%s',
            number_format($price, 2)
        );
    }
}
