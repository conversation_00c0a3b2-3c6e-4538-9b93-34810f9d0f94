<?php

namespace MailScanModule\Factories;

use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Enums\StatusEnum;
use Utils\Helpers\ArrayHelper;

class MailroomPostItemDataFactory
{
    public const MAILROOM_SENDER_MAP = [
        "COMPANIES_HOUSE" => "Companies House",
        "HMRC" => "HMRC",
        "OTHER" => "Other",
    ];

    public static function createFromMailroomApiResponseItem(array $mailroomApiResponseItem): MailroomPostItemData
    {
        return new MailroomPostItemData(
            ArrayHelper::get($mailroomApiResponseItem, 'post_item_id'),
            ArrayHelper::get($mailroomApiResponseItem, 'company_name'),
            ArrayHelper::get($mailroomApiResponseItem, 'company_number'),
            ArrayHelper::get($mailroomApiResponseItem, 'type'),
            self::parseSender(ArrayHelper::get($mailroomApiResponseItem, 'sender')),
            ArrayHelper::get($mailroomApiResponseItem, 'file_name'),
            ArrayHelper::get($mailroomApiResponseItem, 'batch_number'),
            ArrayHelper::get($mailroomApiResponseItem, 'operator'),
            ArrayHelper::get($mailroomApiResponseItem, 'details'),
            ArrayHelper::get($mailroomApiResponseItem, 'events'),
            new \DateTime(ArrayHelper::get($mailroomApiResponseItem, ['dtc', 'date']), new \DateTimeZone('UTC')),
            ArrayHelper::get($mailroomApiResponseItem, 'status'),
            ArrayHelper::get($mailroomApiResponseItem, 'transaction_id', null),
        );
    }

    public static function MockFromArray(array $mailroomApiResponseItem): MailroomPostItemData
    {
        try {
            $dtc = new \DateTime(ArrayHelper::get($mailroomApiResponseItem, ['dtc', 'date']), new \DateTimeZone('UTC'));
        } catch (\Exception $e) {
            $dtc = new \DateTime();
        }

        return new MailroomPostItemData(
            ArrayHelper::get($mailroomApiResponseItem, 'post_item_id', '1'),
            ArrayHelper::get($mailroomApiResponseItem, 'company_name', 'Test Company'),
            ArrayHelper::get($mailroomApiResponseItem, 'company_number', '1'),
            ArrayHelper::get($mailroomApiResponseItem, 'type', 'non-statutory'),
            self::parseSender(ArrayHelper::get($mailroomApiResponseItem, 'sender', 'Other')),
            ArrayHelper::get($mailroomApiResponseItem, 'file_name', 'test.pdf'),
            ArrayHelper::get($mailroomApiResponseItem, 'batch_number', '12345678'),
            ArrayHelper::get($mailroomApiResponseItem, 'operator', 'abcdefghijk'),
            ArrayHelper::get($mailroomApiResponseItem, 'details', []),
            ArrayHelper::get($mailroomApiResponseItem, 'events', []),
            $dtc,
            ArrayHelper::get($mailroomApiResponseItem, 'status', null),
        );
    }

    public static function createFromKofaxArray(?array $postItem): MailroomPostItemData
    {
        return new MailroomPostItemData(
            id: $postItem['postItemId'],
            companyName: $postItem['companyName'],
            companyNumber: $postItem['companyNumber'],
            type: PostItemTypeEnum::TYPE_STATUTORY->value,
            sender: self::parseSender($postItem['typeId']),
            fileName: null,
            batch: $postItem['batchName'],
            operator: $postItem['agent'],
            details: [],
            events: [],
            dtc: new \DateTime($postItem['dtc']),
            status: $postItem['statusId'] === 'RELEASED' ? StatusEnum::STATUS_SCAN_ONLY->value : StatusEnum::STATUS_ADDED->value,
            isLegacy: true,
        );
    }

    private static function parseSender(string $sender): string
    {
        return self::MAILROOM_SENDER_MAP[$sender] ?? 'Other';
    }
}
