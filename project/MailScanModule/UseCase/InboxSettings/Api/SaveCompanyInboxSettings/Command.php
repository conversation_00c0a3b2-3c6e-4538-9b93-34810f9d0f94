<?php

declare(strict_types=1);

namespace MailScanModule\UseCase\InboxSettings\Api\SaveCompanyInboxSettings;

use CompanyModule\Entities\Settings\PostItemHandlingSetting;
use CompanyModule\Facades\MailForwardingFacade;
use CompanyModule\Facades\PostItemHandlingFacade;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;
use Exceptions\Technical\NodeException;
use MailScanModule\Exceptions\NotAMailboxProductException;
use MailScanModule\Facades\MailForwardingAddressFacade;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use MailScanModule\Providers\MailboxProductProvider;

readonly class Command
{
    public function __construct(
        private Factory $factory,
        private MailForwardingAddressFacade $mailForwardingAddressFacade,
        private MailForwardingFacade $mailForwardingFacade,
        private PostItemHandlingFacade $postItemHandlingFacade,
        private MailboxProductProvider $mailboxProductProvider,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function execute(Request $request): Response
    {
        try {
            if ($this->requireForwardingAddress($request->companyMailboxSettings)) {
                $this->setCompanyAddressSettings($request->company, $request->companyMailboxSettings);
            }

            $this->setCompanyMailboxSettings($request->company, $request->companyMailboxSettings);

            return $this->factory->makeResponse();
        } catch (\Throwable $e) {
            throw new \Exception(sprintf('Failed to save company settings: %s', $e->getMessage()));
        }
    }

    /**
     * @throws NonUniqueResultException
     */
    private function setCompanyAddressSettings(Company $company, array $settings): void
    {
        $this->mailForwardingAddressFacade->setForwardingAddress($company, $settings);
    }

    /**
     * @throws \Exception
     */
    private function setCompanyMailboxSettings(Company $company, array $settings): void
    {
        $handlingSettings = $this->postItemHandlingFacade->getPostItemHandlingSetting($company);

        if (
            $handlingSettings->getHandlingSettingByType(PostItemHandlingSetting::KEY_POST_ITEM_HANDLING) == $settings['postItemsSettings']
            && $handlingSettings->getHandlingSettingByType(PostItemHandlingSetting::KEY_PARCEL_HANDLING) == $settings['parcelsSettings']
        ) {
            return;
        }

        if (
            $settings['postItemsSettings'] > $this->getMaxTierForSetting($company, MailboxProductPropertyHelper::FORMAT_POST_ITEM)
            || $settings['parcelsSettings'] > $this->getMaxTierForSetting($company, MailboxProductPropertyHelper::FORMAT_PARCEL)
        ) {
            throw new \Exception('Your mailbox tier does not support this setting value.');
        }

        $handlingSettings->setHandlingSettingByType(PostItemHandlingSetting::KEY_POST_ITEM_HANDLING, $settings['postItemsSettings']);
        $handlingSettings->setHandlingSettingByType(PostItemHandlingSetting::KEY_PARCEL_HANDLING, $settings['parcelsSettings']);

        $this->mailForwardingFacade->setPostItemHandlingSetting($handlingSettings);
    }

    /**
     * @throws NotAMailboxProductException
     * @throws NodeException
     */
    private function getMaxTierForSetting(Company $company, string $type): int
    {
        $mailboxInitialProduct = $this->mailboxProductProvider->getInitialProductFromCompany($company);

        return MailboxProductPropertyHelper::getHandlingSettingByFormat($mailboxInitialProduct, $type);
    }

    private function requireForwardingAddress(array $settings): bool
    {
        return $settings['postItemsSettings'] === PostItemHandlingSetting::VALUE_ITEM_SCAN_AND_POST
            || $settings['parcelsSettings'] === PostItemHandlingSetting::VALUE_PARCEL_POST;
    }
}
