<?php

declare(strict_types=1);

namespace MailScanModule\Dto;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Entities\Customer;

readonly class PostItemBag implements \JsonSerializable
{
    public function __construct(
        private ArrayCollection $postItems,
    ) {
    }

    public function isEmpty(): bool
    {
        return $this->postItems->isEmpty();
    }

    public function getAllPostItems(): array
    {
        return $this->postItems->toArray();
    }

    public function countAllPostItems(): int
    {
        return $this->postItems->count();
    }

    public function getCompaniesInBag(): array
    {
        $companies = [];
        /** @var MailroomPostItemData $postItem */
        foreach ($this->postItems as $postItem) {
            $companyNumber = $postItem->getCompanyNumber();
            if ($companyNumber && !isset($companies[$companyNumber])) {
                $companies[$companyNumber] = true;
            }
        }

        return array_keys($companies);
    }

    public function getCompanyPostItems(string $companyNumber): array
    {
        $companyPostItems = [];
        foreach ($this->postItems as $postItem) {
            if ($postItem->getCompanyNumber() === $companyNumber) {
                $companyPostItems[] = $postItem;
            }
        }

        return $companyPostItems;
    }

    public function getCustomerPostItems(Customer $customer): array
    {
        $customerCompanies = $customer->getCompanies();
        $customerCompanyNumbers = [];
        foreach ($customerCompanies as $company) {
            $customerCompanyNumbers[] = $company->getCompanyNumber();
        }

        $customerPostItems = [];
        foreach ($this->postItems as $postItem) {
            if (in_array($postItem->getCompanyNumber(), $customerCompanyNumbers)) {
                $customerPostItems[] = $postItem;
            }
        }

        return $customerPostItems;
    }

    public function jsonSerialize(): array
    {
        return $this->postItems->toArray();
    }
}
