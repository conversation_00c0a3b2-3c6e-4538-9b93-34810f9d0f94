<?php

namespace MailScanModule\Dto;

/**
 * @deprecated use MailroomPostItemData instead. We are moving away from PostItemUpdater and the old process.
 */
class PostedMailData
{
    /**
     * @var array
     */
    private $postedMailItems;

    /**
     * @var int
     */
    private $itemCount;

    public function __construct(
        array $postedMailItems,
        int $itemCount,
    ) {
        $this->postedMailItems = $postedMailItems;
        $this->itemCount = $itemCount;
    }

    /**
     * @return array
     */
    public function getPostedMailItems(): array
    {
        return $this->postedMailItems;
    }

    /**
     * @return int
     */
    public function getItemCount(): int
    {
        return $this->itemCount;
    }
}
