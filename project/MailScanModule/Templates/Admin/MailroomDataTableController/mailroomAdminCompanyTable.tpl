{include file="@header.tpl" tabs=null}

{block "tabs"}
    <ul id="navlist">
        <li><a href="{url route="551"}">List</a></li>
        <li><a href="{url route="551 view" company_id=$company->getId()}">{$company->getCompanyName()}</a></li>
        <li><a href="{url route="admin_mailroom_post_items" company=$company->getId()}" class="current">Mailroom Post Items</a></li>
    </ul>
{/block}

{block content}
    <div class="width100 bg-white padcard-mobile">
        <div class="container">

            <div class="row datagrid">
                <div class="col-xs-12">
                    <table class="grid table table-bordered">
                        <thead class="hidden-xs">
                        <tr>
                            <th data-searchable="false" data-orderable="false">Date Received</th>
                            <th data-searchable="false" data-orderable="false">Post Item Id</th>
                            <th data-searchable="false" data-orderable="false">Batch Id</th>
                            <th data-searchable="false" data-orderable="false">Type</th>
                            <th data-searchable="false" data-orderable="false">Sender</th>
                            <th data-searchable="false" data-orderable="false">File Name</th>
                            <th data-searchable="false" data-orderable="false">Status</th>
                        </tr>
                        </thead>
                        <tbody>
                        {if !empty($postItems)}
                            {foreach $postItems as $item}
                                {include file='./mailroomPostItemRow.tpl' item=$item}
                            {/foreach}

                        {else}
                            <tr><td colspan="7">No post items found</td></tr>
                        {/if}
                        </tbody>
                    </table>

                    {if !empty($postItems)}
                        <div class="pagination-container text-center mt-3">
                            <ul class="pagination">
                                {math equation="ceil(x/y)" x=$total y=$psize assign="totalPages"}

                                {* Calculate start page number *}
                                {math equation="p-2" p=$pnum assign="startOffset"}
                                {math equation="max(1,x)" x=$startOffset assign="startPage"}

                                {* Calculate end page number *}
                                {math equation="p+2" p=$pnum assign="endOffset"}
                                {math equation="min(x,y)" x=$endOffset y=$totalPages assign="endPage"}

                                {* First Page Button - only show if first page is not in range *}
                                {if $startPage > 1}
                                    <li class="page-item">
                                        <a class="page-link" href="{url route='admin_mailroom_post_items' company=$company->getId() pnum=1}">First</a>
                                    </li>
                                {/if}

                                {* Previous Button *}
                                {if $pnum > 1}
                                    <li class="page-item">
                                        <a class="page-link" href="{url route='admin_mailroom_post_items' company=$company->getId() pnum=($pnum-1)}">Previous</a>
                                    </li>
                                {/if}

                                {for $i=$startPage to $endPage}
                                    <li class="page-item {if $i == $pnum}active{/if}">
                                        <a class="page-link" href="{url route='admin_mailroom_post_items' company=$company->getId() pnum=$i}">{$i}</a>
                                    </li>
                                {/for}

                                {* Next Button *}
                                {if $pnum < $totalPages}
                                    <li class="page-item">
                                        <a class="page-link" href="{url route='admin_mailroom_post_items' company=$company->getId() pnum=($pnum+1)}">Next</a>
                                    </li>
                                {/if}

                                {* Last Page Button - only show if last page is not in range *}
                                {if $endPage < $totalPages}
                                    <li class="page-item">
                                        <a class="page-link" href="{url route='admin_mailroom_post_items' company=$company->getId() pnum=$totalPages}">Last</a>
                                    </li>
                                {/if}
                            </ul>
                        </div>
                    {/if}
                </div>
            </div>

        </div>
    </div>

{/block}
{include file="@footer.tpl"}

<style>
.pagination {
    display: inline-flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
}
.pagination > li {
    margin: 0 2px;
}
.pagination > li > a {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    line-height: 1.25;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6;
    text-decoration: none;
}
.pagination > li.active > a {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}
.pagination-container {
    margin: 20px 0;
}
</style>
