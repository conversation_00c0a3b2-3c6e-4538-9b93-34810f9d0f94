<?php

namespace MailScanModule\Helpers;

use http\Exception\InvalidArgumentException;
use MailScanModule\Enums\StatusEnum;
use Models\Products\BasketProduct;

class MailboxProductPropertyHelper
{
    public const TYPE_STATUTORY = 'statutory';
    public const TYPE_NON_STATUTORY = 'non-statutory';
    public const TYPE_PARCEL = 'parcel';
    public const ITEM_TYPES = [
        'statutory'     => self::TYPE_STATUTORY,
        'non-statutory' => self::TYPE_NON_STATUTORY,
        'parcel'        => self::TYPE_PARCEL,
    ];

    public const PROCESSING_METHOD_SCANNED = 'scanned';
    public const PROCESSING_METHOD_COLLECT = 'collect';
    public const PROCESSING_METHOD_POST = 'post';
    public const PROCESSING_METHODS = [
        'scan' => self::PROCESSING_METHOD_SCANNED,
        'collect' => self::PROCESSING_METHOD_COLLECT,
        'post'    => self::PROCESSING_METHOD_POST,
    ];

    public const DESIRED_STATUSES = [
        self::PROCESSING_METHOD_SCANNED => StatusEnum::STATUS_SCAN_ONLY->value,
        self::PROCESSING_METHOD_COLLECT => StatusEnum::STATUS_TO_BE_COLLECTED->value,
        self::PROCESSING_METHOD_POST => StatusEnum::STATUS_TO_BE_FORWARDED->value,
    ];

    public const FORMAT_POST_ITEM = 'post-item';
    public const FORMAT_PARCEL = 'parcel';
    public const FORMATS = [
        'post-item' => self::FORMAT_POST_ITEM,
        'parcel'    => self::FORMAT_PARCEL,
    ];

    public static function getAcceptanceByType(BasketProduct $product, string $type): bool
    {
        return match ($type) {
            self::TYPE_PARCEL => $product->getMailboxAcceptanceParcel() === 1,
            self::TYPE_STATUTORY => $product->getMailboxAcceptanceStatutory() === 1,
            self::TYPE_NON_STATUTORY => $product->getMailboxAcceptanceNonStatutory() === 1,
            default => false,
        };
    }

    public static function hasUnlimitedQuotasByTypeAndProcessingMethod(BasketProduct $product, string $type, int $processingMethod): bool
    {
        $processingMethodAsString = self::getProcessingMethodAsString($type, $processingMethod);

        return match ($type) {
            self::TYPE_PARCEL => match ($processingMethodAsString) {
                self::PROCESSING_METHOD_COLLECT => $product->getMailboxQuotaParcelCollect() === -1,
                self::PROCESSING_METHOD_POST => $product->getMailboxQuotaParcelPost() === -1,
                default => false,
            },
            self::TYPE_STATUTORY => match ($processingMethodAsString) {
                self::PROCESSING_METHOD_SCANNED => $product->getMailboxQuotaStatutoryScanned() === -1,
                self::PROCESSING_METHOD_COLLECT => $product->getMailboxQuotaStatutoryCollect() === -1,
                self::PROCESSING_METHOD_POST => $product->getMailboxQuotaStatutoryPost() === -1,
                default => false,
            },
            self::TYPE_NON_STATUTORY => match ($processingMethodAsString) {
                self::PROCESSING_METHOD_SCANNED => $product->getMailboxQuotaNonStatutoryScanned() === -1,
                self::PROCESSING_METHOD_COLLECT => $product->getMailboxQuotaNonStatutoryCollect() === -1,
                self::PROCESSING_METHOD_POST => $product->getMailboxQuotaNonStatutoryPost() === -1,
                default => false,
            },
            default => false,
        };
    }

    public static function getMaximumQuotaByTypeAndProcessingMethod(BasketProduct $product, string $type, int $processingMethod): int
    {
        $processingMethod = self::getProcessingMethodAsString($type, $processingMethod);

        $maximumQuota = match ($type) {
            self::TYPE_PARCEL => match ($processingMethod) {
                self::PROCESSING_METHOD_COLLECT => $product->getMailboxQuotaParcelCollect(),
                self::PROCESSING_METHOD_POST => $product->getMailboxQuotaParcelPost(),
                default => -2,
            },
            self::TYPE_STATUTORY => match ($processingMethod) {
                self::PROCESSING_METHOD_SCANNED => $product->getMailboxQuotaStatutoryScanned(),
                self::PROCESSING_METHOD_COLLECT => $product->getMailboxQuotaStatutoryCollect(),
                self::PROCESSING_METHOD_POST => $product->getMailboxQuotaStatutoryPost(),
                default => -2,
            },
            self::TYPE_NON_STATUTORY => match ($processingMethod) {
                self::PROCESSING_METHOD_SCANNED => $product->getMailboxQuotaNonStatutoryScanned(),
                self::PROCESSING_METHOD_COLLECT => $product->getMailboxQuotaNonStatutoryCollect(),
                self::PROCESSING_METHOD_POST => $product->getMailboxQuotaNonStatutoryPost(),
                default => -2,
            },
            default => -2,
        };

        if ($maximumQuota === -2) {
            throw new InvalidArgumentException('Invalid type or processing method');
        }

        return $maximumQuota;
    }

    public static function getExtraQuotaFeeByFormat(BasketProduct $product, string $format): float
    {
        $result = match ($format) {
            self::FORMAT_POST_ITEM => $product->getMailboxExtraQuotaPostItemFee(),
            self::FORMAT_PARCEL => $product->getMailboxExtraQuotaParcelFee(),
            default => -1,
        };

        if ($result === -1) {
            throw new InvalidArgumentException('Invalid format');
        }

        return $result;
    }

    public static function getHandlingFeeByFormat(BasketProduct $product, string $format): float
    {
        $result = match ($format) {
            self::FORMAT_POST_ITEM => $product->getMailboxHandlingFeePostItem(),
            self::FORMAT_PARCEL => $product->getMailboxHandlingFeeParcel(),
            default => -1,
        };

        if ($result === -1) {
            throw new InvalidArgumentException('Invalid format');
        }

        return $result;
    }

    public static function getForwardingFeeByFormat(BasketProduct $product, string $format): float
    {
        $result = match ($format) {
            self::FORMAT_POST_ITEM => $product->getMailboxForwardingFeePostItem(),
            self::FORMAT_PARCEL => $product->getMailboxForwardingFeeParcel(),
            default => -1,
        };

        if ($result === -1) {
            throw new InvalidArgumentException('Invalid format');
        }

        return $result;
    }

    public static function getPayToReleaseFeeByFormat(BasketProduct $product, string $format): float
    {
        $result = match ($format) {
            self::FORMAT_POST_ITEM => $product->getMailboxPayToReleaseFeePostItem(),
            self::FORMAT_PARCEL => $product->getMailboxPayToReleaseFeeParcel(),
            default => -1,
        };

        if ($result === -1) {
            throw new InvalidArgumentException('Invalid format');
        }

        return $result;
    }

    public static function getHandlingSettingByFormat(BasketProduct $product, string $format): int
    {
        return match ($format) {
            self::FORMAT_POST_ITEM => $product->getMailboxSettingPostItem(),
            self::FORMAT_PARCEL => $product->getMailboxSettingParcel(),
            default => throw new InvalidArgumentException('Invalid format'),
        };
    }

    public static function getProcessingMethodAsString(string $type, int $processingMethod): string
    {
        if ($type === self::TYPE_PARCEL) {
            if ($processingMethod === 1) {
                return self::PROCESSING_METHOD_COLLECT;
            }
            if ($processingMethod === 2) {
                return self::PROCESSING_METHOD_POST;
            }
            throw new InvalidArgumentException('Invalid processing method for parcel. Can only be 1 (collect) or 2 (post)');
        } else {
            if ($processingMethod === 1) {
                return self::PROCESSING_METHOD_SCANNED;
            }
            if ($processingMethod === 2) {
                return self::PROCESSING_METHOD_COLLECT;
            }
            if ($processingMethod === 3) {
                return self::PROCESSING_METHOD_POST;
            }
            throw new InvalidArgumentException('Invalid processing method for post item. Can only be 1 (scan), 2 (collect) or 3 (post)');
        }
    }
}
