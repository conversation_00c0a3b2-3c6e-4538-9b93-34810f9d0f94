<?php

declare(strict_types=1);

namespace MailScanModule\Emailers;

use chfiling\type\DocumentContent;
use EmailModule\IEmail;
use EmailModule\IEmailGateway;
use EmailModule\IEmailLog;
use EmailModule\Loaders\IEmailLoader;
use Entities\Company;
use Entities\Customer;
use MailScanModule\ApiClient\IMailroomApiClient;
use MailScanModule\Deciders\MailboxEmailDecider;
use MailScanModule\Dto\MailboxEmailData;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Factories\MailboxEmailFactory;
use MailScanModule\Providers\MailboxEmailNameProvider;
use Psr\Log\LoggerInterface;
use Utils\File;

class MailboxEmailer
{
    public const PROCESSING_STEP_NO_ID_CHECK = 'no-id-check';
    public const PROCESSING_STEP_NO_MAILBOX_SERVICE = 'no-mailbox-service';
    public const PROCESSING_STEP_SERVICE_OVERDUE = 'service-overdue';
    public const PROCESSING_STEP_RELEASED = 'released';
    public const PROCESSING_STEP_FAILED_TO_CHARGE = 'failed-to-charge';
    public const PROCESSING_STEP_RTS = 'return-to-sender';
    public const PROCESSING_STEP_WAITING_FOR_PAYMENT = 'waiting-for-payment';
    public const PROCESSING_STEP_UPDATE = 'updating-status';
    private const MAILBOX_FOLDER_NAME = 'Mailbox-Emails';
    // TODO: review all email template names after specs are updated.
    private const MAX_LENGTH_IN_BYTES = 52428800;

    public function __construct(
        private IEmailGateway $emailGateway,
        private IEmailLoader $emailLoader,
        private IMailroomApiClient $mailroomApiClient,
        private MailboxEmailFactory $mailboxEmailFactory,
        private MailboxEmailNameProvider $mailboxEmailProvider,
        private MailboxEmailDecider $mailboxEmailDecider,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @param mixed $dryRun
     *
     * @throws \Exception
     */
    public function sendOneEmailForEachItem(Company $company, array $items, string $processingStep, $dryRun = true): void
    {
        $itemsForApi = [];
        foreach ($items as $item) {
            $emailData = $this->mailboxEmailFactory->createFromMailroomPostItemData($company, $item, $processingStep);
            $emailName = $this->mailboxEmailProvider->getMailboxEmailName($emailData, $processingStep);

            if (is_null($emailName)) {
                $this->logger->info(
                    sprintf(
                        'No email to send regarding post item %s and company number %s',
                        $item->getId(),
                        $company->getCompanyNumber()
                    )
                );
                continue;
            }
            if ($emailData->lastEmailSent !== $emailName) {
                $this->logger->info(
                    sprintf(
                        'Sending email %s to %s, regarding post item %s and company number %s',
                        $emailName,
                        $company->getCustomer()->getEmail(),
                        $item->getId(),
                        $company->getCompanyNumber()
                    )
                );
            } elseif ($this->mailboxEmailDecider->reminderShouldBeSent($emailData, $emailName)) {
                $emailName = $this->mailboxEmailProvider->getReminderEmailName($emailName);
                $this->logger->info(
                    sprintf(
                        'Sending reminder email %s to %s, regarding post item %s and company number %s',
                        $emailName,
                        $company->getCustomer()->getEmail(),
                        $item->getId(),
                        $company->getCompanyNumber()
                    )
                );
            } else {
                $this->logger->info(
                    sprintf(
                        'Skipping email %s to %s, regarding post item %s and company number %s (email already sent)',
                        $emailName,
                        $company->getCustomer()->getEmail(),
                        $item->getId(),
                        $company->getCompanyNumber()
                    )
                );
                continue;
            }

            if ($dryRun) {
                continue;
            }

            try {
                $emailLog = $this->sendEmail($company, $emailName, $emailData, $item);

                $itemsForApi[] = [
                    'post_item_id' => $item->getId(),
                    'email_log_id' => $emailLog->getId(),
                    'email_name' => $emailName,
                ];
            } catch (\Throwable $e) {
                $this->logger->critical($e->getMessage(), ['exception' => $e]);
            }
        }

        if (!empty($itemsForApi) && !$dryRun) {
            $this->mailroomApiClient->setLastEmailSent($itemsForApi);
        }
    }

    private function loadEmail(string $templateName, Customer $customer, array $context): IEmail
    {
        $email = $this->emailLoader->getEmail(
            File::fromExistingPath(
                sprintf(
                    '%s/cms/Digital-Mailroom/%s/%s.md',
                    EMAIL_DIR,
                    self::MAILBOX_FOLDER_NAME,
                    $templateName
                )
            ),
            $context
        );

        $email->setTo($customer->getEmail());

        return $this->addAttachments($customer, $email, $context);
    }

    private function addAttachments(Customer $customer, IEmail $email, array $context): IEmail
    {
        $attachmentLink = $context['postItem']->getDetail('pdf_link') ?? null;

        if (
            $customer->hasMailboxAttachmentsEnabled()
            && !is_null($attachmentLink)
            && $context['emailData']->isReleased
        ) {
            $email->addAttachment(
                $this->getAttachmentName($context['postItem']),
                file_get_contents(
                    $attachmentLink,
                    false,
                    null,
                    0,
                    self::MAX_LENGTH_IN_BYTES
                ),
                DocumentContent::PDF
            );
        }

        return $email;
    }

    private function sendEmail(
        Company $company,
        string $emailName,
        MailboxEmailData $emailData,
        MailroomPostItemData $item,
    ): IEmailLog {
        $email = $this->loadEmail(
            templateName: $emailName,
            customer: $company->getCustomer(),
            context: [
                'emailData' => $emailData,
                'postItem' => $item,
            ],
        );

        return $this->emailGateway->send($email, $company->getCustomer());
    }

    private function getAttachmentName(MailroomPostItemData $postItem): string
    {
        try {
            $sanitizedCompanyName = preg_replace('/[\/\\\\?%*:|"<>]/', '-', $postItem->getCompanyName());
            $sanitizedDtc = preg_replace('/[\/\\\\?%*:|"<> :]/', '-', $postItem->getDtc()->format('Y-m-d-H-i-s'));

            return sprintf('%s_%s.pdf', $sanitizedCompanyName, $sanitizedDtc);
        } catch (\Throwable $e) {
            return sprintf('post-item_%s.pdf', time());
        }
    }
}
