<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />

        <service class="MailScanModule\Controllers\CompanyInboxController"
                 id="mail_scan_module.controllers.company_inbox_controller">
            <argument type="service" id="templating_module.front_renderer"/>
            <argument type="service" id="user_module.services.customer_availability"/>
            <argument id="symfony.request" type="service"/>
        </service>
    </services>
</container>
