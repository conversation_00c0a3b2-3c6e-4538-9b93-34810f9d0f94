services:
- %module_dir%/Config/di.xml
- %module_dir%/Config/Api/CompanyInbox/di.xml
- %module_dir%/Config/Di/CompanyInbox/di.xml
- %module_dir%/Config/Di/InboxSettings/di.xml
- %module_dir%/Config/Di/InboxSettings/save-company-inbox-settings.xml
- %module_dir%/Config/Di/InboxSettings/save-customer-inbox-settings.xml

routes:
  - %module_dir%/Config/routes.yml
  - %module_dir%/Config/Api/CompanyInbox/routes.yml
  - %module_dir%/Config/Routes/CompanyInbox/routes.yml
  - %module_dir%/Config/Routes/InboxSettings/routes.yml
  - %module_dir%/Config/Routes/InboxSettings/save-company-inbox-settings.yml
  - %module_dir%/Config/Routes/InboxSettings/save-customer-inbox-settings.yml

doctrine:
  entities: %module_dir%/Entities

kofax:
  export_dir: "gs://kofax-msg/lookup"
  items_dir: "gs://kofax-msg/post_items"

admin_menu:
  mail_forwarding_search:
    node_id: 51533453
    parent_node_id: 550 # real node id where it will be inserted
    route: mail_forwarding_search
    role: staff
    title: Mail Forwarding Search

symfony_forms:
  validation_files:
    - %module_dir%/Config/validations.yml


