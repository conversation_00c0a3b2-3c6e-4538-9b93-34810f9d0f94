formation_product_id,mailbox_product_name,post_item_type,handling_format,within_quota,charging_attempts,id_check_complete,service_overdue,company_name,post_status
Package::PACKAGE_BASIC,Product::PRODUCT_MAILBOX_STANDARD_RENEWAL,PostItemTypeEnum::TYPE_STATUTORY->value,MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,TRUE,null,FALSE,FALSE,MAIL_SCAN_3MONTHS_should-to-be-destroyed,added
Package::PACKAGE_BASIC,Product::PRODUCT_REGISTERED_OFFICE,PostItemTypeEnum::TYPE_NON_STATUTORY->value,MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED,TRUE,null,TRUE,FALSE,MAIL_SCAN_3MONTHS_should-to-be-destroyed,waiting_payment
Package::PACKAGE_BASIC,Product::PRODUCT_MAILBOX_STANDARD_RENEWAL,PostItemTypeEnum::TYPE_STATUTORY->value,MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT,TRUE,null,TRUE,FALSE,MAIL_COLLECT_7DAYS_should-be-scan-only,to_be_collected
Package::PACKAGE_BASIC,Product::PRODUCT_MAILBOX_STANDARD_RENEWAL,PostItemTypeEnum::TYPE_PARCEL->value,MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT,TRUE,null,FALSE,FALSE,PARCEL_COLLECT_7DAYS_should-to-be-destroyed,added
Package::PACKAGE_BASIC,Product::PRODUCT_MAILBOX_STANDARD_RENEWAL,PostItemTypeEnum::TYPE_PARCEL->value,MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT,TRUE,null,TRUE,FALSE,PARCEL_COLLECT_7DAYS_should-to-be-destroyed,to_be_collected
Package::PACKAGE_BASIC,Product::PRODUCT_MAILBOX_PREMIUM_RENEWAL,PostItemTypeEnum::TYPE_PARCEL->value,MailboxProductPropertyHelper::PROCESSING_METHOD_POST,TRUE,null,TRUE,FALSE,PARCEL_POST_7DAYS_should-to-be-destroyed,waiting_payment
Package::PACKAGE_BASIC,Product::PRODUCT_MAILBOX_STANDARD_RENEWAL,PostItemTypeEnum::TYPE_PARCEL->value,MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT,TRUE,null,TRUE,FALSE,PARCEL_COLLECT_3DAYS_should-send-reminder-collect-email,to_be_collected
Package::PACKAGE_BASIC,Product::PRODUCT_MAILBOX_PREMIUM_RENEWAL,PostItemTypeEnum::TYPE_PARCEL->value,MailboxProductPropertyHelper::PROCESSING_METHOD_POST,TRUE,null,TRUE,FALSE,PARCEL_POST_3DAYS_should-send-reminder-payment-email,waiting_payment