my_post_items:
  path: /my-company-inbox/
  defaults:
    controller: mail_scan_module.controllers.data_tables_controller
    action: render
    template: project/MailScanModule/Templates/Tables/postItems.tpl
  options:
    excludeFromSitemap: true

my_post_items_json:
  path: /my-company-inbox/json/
  defaults:
    controller: mail_scan_module.controllers.data_tables_controller
    action: search
    template: project/MailScanModule/Templates/Tables/postItemRow.tpl
  options:
    excludeFromSitemap: true

my_post_items_admin:
  path: /my-company-inbox/admin/{customer}/
  defaults:
    controller: mail_scan_module.controllers.data_tables_controller
    action: adminPage
    template: project/MailScanModule/Templates/Tables/postItems.tpl
    requirements:
      customer:
        converter: doctrine
  options:
    excludeFromSitemap: true

post_item_download:
  path: /my-company-inbox/download/{postItem}/
  defaults:
    controller: mail_scan_module.controllers.post_item_controller
    action: retrieve
  options:
    excludeFromSitemap: true

post_item_forward:
  path: /my-company-inbox/forward/{postItemId}/
  defaults:
    controller: mail_scan_module.controllers.post_item_controller
    action: forwardNonStatutoryPostItem
  options:
    excludeFromSitemap: true

# admin
admin_post_item_download:
    path: /admin/post-items/download/{postItem}/
    defaults:
      controller: mail_scan_module.controllers.post_item_controller
      action: download
      requirements:
        postItem:
          admin: true

admin_post_items:
  path: /admin/post-items/company/{company}/
  defaults:
    controller: mail_scan_module.controllers.admin.post_item_data_table_controller
    action: showPostItems
    page: 551
    requiredController: AdminModule\Controlers\CompaniesAdminControler
    requirements:
      company:
        converter: doctrine

admin_mailroom_post_items:
  path: /admin/mailroom-post-items/company/{company}/
  defaults:
    controller: mail_scan_module.controllers.admin.mailroom_data_table_controller
    action: mailroomAdminCompanyTable
    page: 551
    requiredController: AdminModule\Controlers\CompaniesAdminControler
    requirements:
      company:
        converter: doctrine

admin_post_items_json:
  path: /admin/post-items/company/{company}/json/
  defaults:
    controller: mail_scan_module.controllers.admin.post_item_data_table_controller
    action: searchPostItems
    page: 551
    requiredController: AdminModule\Controlers\CompaniesAdminControler
    requirements:
      company:
        converter: doctrine

admin_post_item_delete:
  path: /admin/post-items/delete/{postItem}/
  defaults:
    controller: mail_scan_module.controllers.post_item_controller
    action: delete
    requirements:
      postItem:
        admin: true

mail_forwarding_search:
  path: /admin/mail-forwarding-search/
  defaults:
    controller: mail_scan_module.controllers.admin.search_controller
    action: main
    feature: role_staff

mail_forwarding_search_api:
  path: /api/mail-scan/mailroom-search/
  defaults:
    controller: mail_scan_module.controllers.api.search_api_controller
    action: mailroomCompanySearch

mailroom_company_search:
  path: /api/mail-scan/mailroom-cms-company-search/
  defaults:
    controller: mail_scan_module.controllers.api.search_api_controller
    action: mailroomCmsCompanySearch

get_company_mail_forwarding_address_setting:
  path: /api/mail-scan/company-address-setting/{companyNumber}/
  defaults:
    controller: mail_scan_module.controllers.api.company_info_api
    action: getCompanyMailForwardingAddressSetting

mail_forwarding_search_validation_api:
  path: /api/mail-scan/mailroom-search-validation/{companyInfo}/
  methods: [ GET ]
  defaults:
    controller: mail_scan_module.controllers.api.search_api_controller
    action: mailroomCompanySearchValidation

mail_forwarding_search_print_label:
  path: /admin/mail-forwarding-search/print-label/{data}/
  defaults:
    controller: mail_scan_module.controllers.admin.search_controller
    action: printLabel
    feature: role_staff
    _format: 'json'

mail_forwarding_search_log_rts:
  path: /admin/mail-forwarding-search/log-rts/{data}/
  defaults:
    controller: mail_scan_module.controllers.admin.search_controller
    action: logRts
    feature: role_staff
    _format: 'json'
