<?php

declare(strict_types=1);

namespace StrapiModule\Dto;

use SerializingModule\SerializableInterface;

class StrapiCollection implements SerializableInterface, ResponseInterface
{
    public const COLLECTION_NAME = 'hello';

    /**
     * @var Meta
     */
    private $meta; /** @phpstan-ignore-line */

    public function getMeta(): Meta
    {
        return $this->meta;
    }

    public function isSuccessful(): bool
    {
        return true;
    }
}
