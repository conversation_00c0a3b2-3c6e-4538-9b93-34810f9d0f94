<?php

namespace ServiceSettingsModule\Services;

use Doctrine\ORM\Internal\Hydration\IterableResult;
use Entities\Company;
use Entities\Customer;
use Entities\Event;
use Entities\Order;
use Entities\Payment\Token;
use Entities\Service;
use Entities\ServiceSettings;
use EventDetailModule\Entities\EventDetail;
use EventDetailModule\Services\EventDetailService;
use Factories\ServiceViewFactory;
use OmnipayModule\Controllers\Api\OmnipayController;
use Repositories\ServiceSettingsRepository;
use Services\EventService;
use Services\ServiceService;
use ServiceSettingsModule\Facades\ServiceSettingsFacade;

class ServiceSettingsService
{

    /**
     * @var ServiceSettingsRepository
     */
    private $repository;

    /**
     * @var ServiceService
     */
    private $serviceService;

    /**
     * @var ServiceSettingsFacade
     */
    private $serviceSettingsFacade;

    /**
     * @var ServiceViewFactory
     */
    private $serviceViewFactory;

    /**
     * @var EventService
     */
    private $eventService;

    /**
     * @var EventDetailService
     */
    private $eventDetailService;

    /**
     * @param ServiceSettingsRepository $repository
     * @param ServiceService $serviceService
     * @param ServiceSettingsFacade $serviceSettingsFacade
     * @param ServiceViewFactory $serviceViewFactory
     * @param EventService $eventService
     * @param EventDetailService $eventDetailService
     */
    public function __construct(
        ServiceSettingsRepository $repository,
        ServiceService $serviceService,
        ServiceSettingsFacade $serviceSettingsFacade,
        ServiceViewFactory $serviceViewFactory,
        EventService $eventService,
        EventDetailService $eventDetailService
    )
    {
        $this->repository = $repository;
        $this->serviceService = $serviceService;
        $this->serviceSettingsFacade = $serviceSettingsFacade;
        $this->serviceViewFactory = $serviceViewFactory;
        $this->eventService = $eventService;
        $this->eventDetailService = $eventDetailService;
    }

    /**
     * @param ServiceSettings $entity
     */
    public function save(ServiceSettings $entity)
    {
        $this->repository->saveEntity($entity);
    }

    /**
     * @param ServiceSettings $entity
     */
    public function flush(ServiceSettings $entity)
    {
        $this->repository->flush($entity);
    }

    /**
     * @param Company $company
     * @param string $serviceTypeId
     * @return ServiceSettings|NULL
     */
    public function getSettingsByType(Company $company, $serviceTypeId)
    {
        return $this->repository->getSettingsByType($company, $serviceTypeId);
    }

    /**
     * @param Service $service
     * @return ServiceSettings|NULL
     */
    public function getSettingsByService(Service $service)
    {
        return $this->repository->getSettingsByService($service);
    }

    /**
     * @param Token $token
     * @return ServiceSettings[]
     */
    public function getSettingsByToken(Token $token)
    {
        return $this->repository->getSettingsByToken($token);
    }

    public function getSettingsWithEnabledAutoRenewal(): IterableResult
    {
        return $this->repository->getSettingsWithEnabledAutoRenewal();
    }

    /**
     * @param Company $company
     * @param string $serviceTypeId
     * @return ServiceSettings
     */
    public function getOrCreateSettingsByType(Company $company, $serviceTypeId)
    {
        $settings = $this->getSettingsByType($company, $serviceTypeId);
        if (!$settings) {
            $settings = new ServiceSettings($company, $serviceTypeId);
            $this->repository->saveEntity($settings);
        }

        return $settings;
    }

    /**
     * @param Company $company
     */
    public function disableEmailRemindersByCompany(Company $company)
    {
        foreach ($company->getGroupedServices() as $service) {
            $settings = $this->getSettingsByService($service);
            if ($settings) {
                $this->serviceSettingsFacade->disableEmailReminders($settings);
            }
        }
    }

    /**
     * @param Company $company
     */
    public function disableAutoRenewalByCompany(Company $company)
    {
        foreach ($company->getGroupedServices() as $service) {
            $settings = $this->getSettingsByService($service);
            if ($settings) {
                $this->serviceSettingsFacade->disableAutoRenewal($settings);
            }
        }
    }

    public function disableAutoRenewalByService(Service $service)
    {
        $settings = $this->getSettingsByService($service);
        if ($settings) {
            $this->serviceSettingsFacade->disableAutoRenewal($settings);
        }
    }

    /**
     * @param Order $order
     * @param Token $token
     */
    public function enableAutoRenewalByOrder(Order $order, Token $token)
    {
        foreach ($order->getRenewableServices() as $service) {
            $settings = $this->getSettingsByService($service);
            if ($service->isRenewable() && $service->getProduct()->isAutoRenewalAllowed && $settings) {
                $this->serviceSettingsFacade->enableAutoRenewal($settings, $token);
            }
        }
    }

    /**
     * @param Order $order
     */
    public function disableAutoRenewalByOrder(Order $order)
    {
        foreach ($order->getRenewableServices() as $service) {
            $settings = $this->getSettingsByService($service);
            if ($settings) {
                $this->serviceSettingsFacade->disableAutoRenewal($settings);
            }
        }
    }

    /**
     * @param Token $token
     */
    public function suspendAutoRenewalByToken(Token $token)
    {
        $servicesSettings = $this->getSettingsByToken($token);
        foreach ($servicesSettings as $serviceSettings) {
            $this->serviceSettingsFacade->suspendAutoRenewal($serviceSettings);
        }
    }

    /**
     * @param Customer $customer
     * @param Token $token
     */
    public function reenableAutoRenewalByCustomer(Customer $customer, Token $token)
    {
        $servicesSettings = $this->getSettingsToReenableAutoRenewalByCustomer($customer);
        foreach ($servicesSettings as $serviceSettings) {
            if (!$serviceSettings->isServiceCancelled()) {
                $this->serviceSettingsFacade->enableAutoRenewal($serviceSettings, $token);   
            }
        }
    }

    /**
     * @param Customer $customer
     * @return ServiceSettings[]
     */
    public function getSettingsToReenableAutoRenewalByCustomer(Customer $customer)
    {
        $settingsToReenable = [];
        $servicesSettings = $this->repository->getSettingsWithInvalidRenewalTokenByCustomer($customer);
        foreach ($servicesSettings as $serviceSettingsData) {
            /** @var ServiceSettings $serviceSettings */
            $serviceSettings = $serviceSettingsData[0];
            $services = $this->serviceService->getCompanyServicesByType($serviceSettings->getCompany(), $serviceSettings->getServiceTypeId());
            $serviceView = $this->serviceViewFactory->create($services);

            if ($serviceView->canToggleAutoRenewal()) {
                $settingsToReenable[] = $serviceSettings;
            }
        }

        return $settingsToReenable;
    }

    /**
     * @param Customer $customer
     * @return bool
     */
    public function hasCustomerServicesWithInvalidRenewalToken(Customer $customer)
    {
        $servicesSettings = $this->repository->getSettingsWithInvalidRenewalTokenByCustomer($customer);
        foreach ($servicesSettings as $serviceSettingsData) {
            /** @var ServiceSettings $serviceSettings */
            $serviceSettings = $serviceSettingsData[0];
            $services = $this->serviceService->getCompanyServicesByType($serviceSettings->getCompany(), $serviceSettings->getServiceTypeId());
            $serviceView = $this->serviceViewFactory->create($services);

            if ($serviceView->canToggleAutoRenewal()) {
                return TRUE;
            }
        }

        return FALSE;
    }

    /**
     * @param Company $company
     */
    public function disableServicesByCompany(Company $company): void
    {
        $this->disableAutoRenewalByCompany($company);
        $this->disableEmailRemindersByCompany($company);
    }

    public function flushUpdates(): void
    {
        $entityManager = $this->repository->getEntityManager();
        $entityManager->flush();
        $entityManager->clear();
    }

    public function disableAutoRenewalByCustomer(Customer $customer): array
    {
        /** @var ServiceSettings[] $settings */
        $settings = $this->repository->getAutoRenewalEnabledSettingsByCustomer($customer);

        if (empty($settings)) return [];

        $event = $this->eventService->notify(
            OmnipayController::OMNIPAY_AUTO_RENEWAL_DISABLED_EVENT,
            $customer->getId()
        );

        $disabledSettingIds = [];

        foreach ($settings as $setting) {
            $this->serviceSettingsFacade->disableAutoRenewal($setting);
            $this->eventDetailService->notify($event->getId(), 'serviceSettingId', $setting->getId());

            $disabledSettingIds[] = $setting->getId();
        }

        return $disabledSettingIds;
    }

    public function enableAutoRenewalByCustomer(Customer $customer): array
    {
        if ($customer->isWholesale()) return [];

        $event = $this->eventService->getEvent(
            OmnipayController::OMNIPAY_AUTO_RENEWAL_DISABLED_EVENT,
            $customer->getId()
        );

        if ($event)
            $settings = $this->getSettingsWithAutoRenewalDisabledForEvent($event);
        else
            $settings = $this->repository->getAutoRenewalDisabledSettingsByCustomer($customer);

        if (empty($settings)) return [];

        $enabledSettingIds = [];
        foreach ($settings as $setting) {
            if ($setting->isServiceCancelled()) continue;
            $this->serviceSettingsFacade->enableAutoRenewal($setting);
            $enabledSettingIds[] = $setting->getId();
        }

        return $enabledSettingIds;
    }

    private function getSettingsWithAutoRenewalDisabledForEvent(Event $event): array
    {
        $eventDetails = $this->eventDetailService->getEventDetailsByEventId($event->getId());

        if (empty($eventDetails)) return [];

        $settings = [];
        foreach ($eventDetails as $eventDetail) {
            $settingId = $eventDetail->getEventDetailValue();
            $setting = $this->repository->find($settingId);

            if ($setting && !$setting->isAutoRenewalEnabled() && !$setting->isServiceCancelled())
                $settings[] = $setting;
        }

        return $settings;
    }
}
