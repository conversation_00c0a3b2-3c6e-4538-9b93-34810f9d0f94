<?php

namespace OmnipayModule\Providers;

use DateInterval;
use Doctrine\Common\Cache\Cache;
use Exception;
use <PERSON><PERSON>\Serializer\Exception\Exception as SerializerException;
use OmnipayModule\ApiClient\OmnipayApiClient;
use OmnipayModule\Entities\OmnipayCardResponseDetails\OmnipayCard;
use OmnipayModule\Entities\OmnipayCardResponseDetails\OmnipayCardResponseDetails;
use SerializingModule\SerializerInterface;
use Utils\Date;
use Utils\Helpers\ArrayHelper;

class OmnipayCardProvider
{
    public const GROUP_BY_PRECEDENCE = 'precedence';
    const CACHE_NAMESPACE_PAYMENT_METHODS = 'paymentMethods';
    const CACHE_NAMESPACE_EXPIRING_TOKENS = 'expiringTokens';

    /**
     * @var OmnipayApiClient
     */
    private $omnipayApiClient;

    /**
     * @var OmnipayCardResponseDetails
     */
    private $cardSerializer;

    /**
     * @var Cache
     */
    private $cache;

    public function __construct(
        OmnipayApiClient $omnipayApiClient,
        SerializerInterface $cardSerializer,
        Cache $cache
    )
    {
        $this->omnipayApiClient = $omnipayApiClient;
        $this->cardSerializer = $cardSerializer;
        $this->cache = $cache;
    }

    // TODO: Eventually refactor - replace all usages with getPaymentMethods
    public function getCards(
        int $customerId,
        string $groupBy = self::GROUP_BY_PRECEDENCE,
        string $accountId = null,
        bool $isError = false,
        bool $includeInMantleReport = false
    ): ?OmnipayCardResponseDetails
    {
        if ($cached = $this->getCachedData(self::CACHE_NAMESPACE_PAYMENT_METHODS, $customerId))
            return $cached;

        $cardsResponse = $this->omnipayApiClient->getCards(
            $customerId,
            $groupBy,
            $accountId,
            $isError,
            $includeInMantleReport
        );

        try {
            if ($cardsResponse->isSuccess()) {

                /** @var OmnipayCardResponseDetails $response */
                $body = $cardsResponse->getBody();

                $response = $this->cardSerializer->unserialize($body, OmnipayCardResponseDetails::class);
                $response->setGroupedBy($groupBy);

                $this->saveCachedData(self::CACHE_NAMESPACE_PAYMENT_METHODS, $customerId, $response);
                return $response;
            }

            return null;
        } catch (Exception $e) {
            return null;
        }
    }

    public function linkCard(
        int $customerId,
        string $paymentMethodId,
        string $accountId
    ): ?OmnipayCard
    {
        try {
            $cards = $this->getCards($customerId);

            if (empty($cards)) {
                return null;
            }

            $mainCard = $cards->getMainCard();
            $backupCards = $cards->getBackupCards();

            if ($mainCard && $mainCard->getId() === $paymentMethodId) {
                $fingerprint = $mainCard->getFingerprint();
            }

            if (!isset($fingerprint)) {
                foreach ($backupCards as $backupCard) {
                    /** @var OmnipayCard $backupCard */
                    if ($backupCard->getId() === $paymentMethodId) {
                        $fingerprint = $backupCard->getFingerprint();
                        break;
                    }
                }
            }

            $connectedCardResponse = $this->omnipayApiClient->connectPaymentMethod(
                $customerId,
                $paymentMethodId,
                $fingerprint ?? '',
                $accountId
            );

            if ($connectedCardResponse->isSuccess()) {
                $body = json_decode($connectedCardResponse->getBody(), true);
                $cardData = json_encode(ArrayHelper::get($body, ['data', 'payment_method'], []));
                return $this->cardSerializer->unserialize($cardData, OmnipayCard::class);
            }

            return null;
        } catch (Exception $e) {
            return null;
        }
    }

    public function getPaymentMethods(
        int $customerId,
        string $groupBy = self::GROUP_BY_PRECEDENCE,
        bool $isError = true
    ): ?OmnipayCardResponseDetails
    {
        if ($cached = $this->getCachedData(self::CACHE_NAMESPACE_PAYMENT_METHODS, $customerId))
            return $cached;

        $paymentMethodsResponse = $this->omnipayApiClient->getPaymentMethods(
            $customerId,
            $groupBy,
            $isError
        );

        try {
            if ($paymentMethodsResponse->isSuccess()) {
                /** @var OmnipayCardResponseDetails $response */
                $response = $this->cardSerializer->unserialize(
                    $paymentMethodsResponse->getBody(),
                    OmnipayCardResponseDetails::class
                );

                $response->setGroupedBy($groupBy);
                $this->saveCachedData(self::CACHE_NAMESPACE_PAYMENT_METHODS, $customerId, $response);

                return $response;
            }

            return null;
        } catch (SerializerException $e) {
            return $this->getCards($customerId, $groupBy);
        } catch (Exception $e) {
            return null;
        }
    }

    public function getActivePaymentMethod(int $customerId): ?OmnipayCard
    {
        $paymentMethods = $this->getPaymentMethods($customerId);
        return $paymentMethods ? $paymentMethods->getMainCard() : null;
    }

    public function getBackupPaymentMethods(int $customerId): array
    {
        $paymentMethods = $this->getPaymentMethods($customerId);
        return $paymentMethods ? $paymentMethods->getBackupCards() : [];
    }

    public function getExpiringCards(Date $fromDate, Date $toDate, bool $defaultCards): array
    {
        $cacheKey = sprintf('%s-%s', $fromDate->format('my'), $toDate->format('my'));

        if ($cached = $this->getCachedData(self::CACHE_NAMESPACE_EXPIRING_TOKENS, $cacheKey))
            return $cached;

        $fromDateString = $fromDate->format('my');
        $toDateString = $toDate->format('my');
        $expiringTokensResponse = $this->omnipayApiClient->getExpiringTokens($fromDateString, $toDateString);

        /** @var OmnipayCardResponseDetails $response */
        $response = $this->cardSerializer->unserialize(
            $expiringTokensResponse->getBody(),
            OmnipayCardResponseDetails::class
        );

        $cards = $response->getCards();

        $this->saveCachedData(self::CACHE_NAMESPACE_EXPIRING_TOKENS, $cacheKey, $cards);

        return array_filter($cards, function (OmnipayCard $card) use ($defaultCards) {
            return $card->isDefault() === $defaultCards;
        });
    }

    public function removeToken(int $customerId, string $tokenIdentifier): bool
    {
        $response = $this->omnipayApiClient->removeToken($customerId, $tokenIdentifier);
        return $response->isSuccess();
    }

    public function getExpiringCardsByCustomer(int $customerId, int $limit = 30): array
    {
        try {
            $paymentMethods = $this->getPaymentMethods($customerId, self::GROUP_BY_PRECEDENCE, false);

            if (!$paymentMethods) return [];

            $limitDate = new Date();
            $interval = new DateInterval(sprintf('P%dD', $limit));

            $expiringPaymentMethods = [];

            if ($mainCard = $paymentMethods->getMainCard()) {
                $expiryDate = $mainCard->getCardExpiryDate();

                if ($expiryDate && $limitDate >= $expiryDate->sub($interval)) {
                    $expiringPaymentMethods[] = $mainCard;
                }
            }

            /** @var OmnipayCard $paymentMethod */
            foreach ($paymentMethods->getBackupCards() as $paymentMethod) {
                $expiryDate = $paymentMethod->getCardExpiryDate();

                if ($expiryDate && $limitDate >= $expiryDate->sub($interval)) {
                    $expiringPaymentMethods[] = $paymentMethod;
                }
            }

            return array_unique($expiringPaymentMethods, SORT_REGULAR);
        } catch (Exception $e) {
            return [];
        }
    }

    public function getDirectDebit(int $customerId, bool $isError = true): ?OmnipayCard
    {
        $paymentMethods = $this->getPaymentMethods($customerId, self::GROUP_BY_PRECEDENCE, $isError);
        if (!$paymentMethods) return null;

        return $paymentMethods->getDirectDebit();
    }

    public function hasPaymentMethods(int $customerId): bool
    {
        $paymentMethods = $this->getPaymentMethods($customerId);

        if (!$paymentMethods) return false;
        if ($paymentMethods->getMainCard()) return true;
        if (!empty($paymentMethods->getBackupCards())) return true;
        if ($paymentMethods->getDirectDebit()) return true;
        return false;
    }

    private function getCachedData(string $namespace, string $key): OmnipayCardResponseDetails|array|null
    {
        $cacheKey = $this->getCacheKey($namespace, $key);

        if ($this->cache->contains($cacheKey))
            return $this->cache->fetch($cacheKey);

        return null;
    }

    private function saveCachedData(string $namespace, string $key, OmnipayCardResponseDetails|array|null $data): void
    {
        $this->cache->save(
            $this->getCacheKey($namespace, $key),
            $data
        );
    }

    private function getCacheKey(string $namespace, string $key): string
    {
        return sprintf('%s.%s', $namespace, $key);
    }
}