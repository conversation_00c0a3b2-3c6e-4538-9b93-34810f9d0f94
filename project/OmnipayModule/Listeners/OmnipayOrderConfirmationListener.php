<?php

namespace OmnipayModule\Listeners;

use Entities\Order;
use Exception;
use Monolog\Logger;
use OmnipayModule\ApiClient\IOmnipayApiClient;
use OmnipayModule\Responses\FailureResponse;
use Services\TransactionService;
use Symfony\Component\HttpFoundation\Response;
use PaymentModule\Events\PaymentEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Repositories\OrderRepository;
use Utils\Helpers\ArrayHelper;

class OmnipayOrderConfirmationListener implements EventSubscriberInterface
{

    private const ORDER_STATUS_ASSOCIATED_ALREADY_EXISTED = 'payment_or_order_already_exists';

    public function __construct(
        private readonly OrderRepository $orderRepository,
        private readonly IOmnipayApiClient $apiClient,
        private readonly Logger $logger,
        private readonly TransactionService $transactionService
    ) {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'payment.succeeded' => 'onPaymentSuccess'
        ];
    }

    public function onPaymentSuccess(PaymentEvent $paymentEvent): void
    {
        if ($paymentEvent->getPaymentResponse()->isOmnipay() && !$paymentEvent->getPaymentResponse()->isFromWebhook()) {
            try {
                /** @var Order|null $order */
                $order = $this->orderRepository->find($paymentEvent->getBasket()->getOrderId());
                $transaction = $this->transactionService->getTransactionByOrderId($order->getId());
                $intentId = $paymentEvent->getPaymentResponse()->getPaymentIntentId();

                if (empty($order)) {
                    $customerId = $paymentEvent->getCustomer()->getId();
                    $this->logger->error(
                        "Order is missing for Omnipay paymentEvent with customer ID: $customerId",
                        ['exception' => 'Missing order']
                    );
                }

                if (empty($intentId)) {
                    $customerId = $paymentEvent->getCustomer()->getId();
                    $this->logger->error(
                        "IntentId is missing for Omnipay paymentEvent with customer ID: $customerId",
                        ['exception' => 'Missing intent id']
                    );
                }

                $response = $this->apiClient->saveOrder($intentId, $order->getId(), $transaction->getId());

                if ($response instanceof FailureResponse || $response->getCode() >= Response::HTTP_BAD_REQUEST) {
                    $this->logger->error('Error trying to reach PAYMENT_ORDER Omnipay endpoint', ['response' => $response]);
                    return;
                }

                $messageBody = json_decode($response->getBody(), true);
                $orderStatus = ArrayHelper::get($messageBody, 'status');
                $message = ArrayHelper::get($messageBody, 'message');

                if ($orderStatus === self::ORDER_STATUS_ASSOCIATED_ALREADY_EXISTED) {
                    $this->logger->error('Omnipay API response: ' . $message, ['Omnipay Order Association - Error' => (string) $message]);
                    return;
                }

                $this->logger->info(
                    sprintf('Order ID: %s associated with payment intent ID: %s', $order->getId(), $intentId),
                    ['Omnipay Order Association' => (string) $message]
                );

            } catch (Exception $e) {
                $this->logger->error('Error - Omnipay listener saving order and payment intent id', ['exception' => $e]);
            }
        }

        return;
    }
}
