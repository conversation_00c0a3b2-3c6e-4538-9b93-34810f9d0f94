<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">

    <services>
        <defaults public="true" />
        <service id="omnipay_module.controllers.api.omnipay_controller" class="OmnipayModule\Controllers\Api\OmnipayController">
            <argument id="services.customer_service" type="service"/>
            <argument id="login_module.services.login_service" type="service"/>
            <argument id="session" type="service"/>
            <argument id="login_module.providers.session_data_provider" type="service"/>
            <argument id="omnipay_module.facades.api_token_payment_facade" type="service"/>
            <argument id="service_settings_module.services.service_settings_service" type="service"/>
            <argument id="error.loggers.monolog" type="service"/>
            <argument id="omnipay_module.deciders.omnipay_decider" type="service"/>
        </service>

        <service id="omnipay_module.controllers.api.open_routes_controller" class="OmnipayModule\Controllers\Api\OpenRoutesController">
            <argument id="services.customer_service" type="service"/>
            <argument id="user_module.services.customer_availability" type="service"/>
            <argument id="payment_module.payment_types.payment_maker_omnipay" type="service"/>
            <argument id="payment_module.redirections.payment_redirection" type="service"/>
            <argument id="payment_module.payment_types.omnipay_payment" type="service"/>
            <argument id="payment_module.loggers.payment_gateway_logger" type="service"/>
            <argument id="payment_module.processors.payment_processor" type="service"/>
            <argument id="symfony.request" type="service"/>
            <argument id="repositories.transaction_repository" type="service"/>
            <argument id="services.transaction_service" type="service"/>
            <argument id="omnipay_module.emailers.direct_debit_emailer" type="service"/>
            <argument id="omnipay_module.services.direct_debit_service" type="service"/>
            <argument id="pay_by_phone_module.services.pay_by_phone_service" type="service"/>
        </service>

        <service id="omnipay_module.commands.enable_auto_renewal_command" class="OmnipayModule\Commands\EnableAutoRenewalCommand">
            <argument id="repositories.service_settings_repository" type="service"/>
            <argument id="cron.loggers.default_logger" type="service"/>

            <tag name="cron.command" command-name="omnipay:enable_auto_renewal" action="execute" />
        </service>

        <service class="OmnipayModule\Listeners\OmnipayOrderConfirmationListener" id="omnipay_module.listeners.omnipay_order_confirmation_listener">
            <argument id="repositories.order_repository" type="service"/>
            <argument id="omnipay_module.api_client.omnipay_api_client" type="service"/>
            <argument id="error.loggers.monolog" type="service"/>
            <argument id="services.transaction_service" type="service"/>

            <tag name="kernel.event_subscriber" priority="100"/>
        </service>

        <service class="OmnipayModule\ApiClient\OmnipayApiClient" id="omnipay_module.api_client.omnipay_api_client">
            <argument id="omnipay_module.http_client" type="service"/>
            <argument id="omnipay_module.resolvers.response_resolver" type="service"/>
            <argument id="payment_module.loggers.payment_gateway_logger" type="service"/>
            <argument type="service" id="router_module.helpers.controller_helper"/>
            <argument>%omnipay%</argument>
            <argument>%environment%</argument>
            <argument>%omnipay.ignored_response_errors%</argument>
        </service>

        <service class="HttpClient\Client" id="omnipay_module.http_client">
            <factory service="http_client.factories.client_factory" method="createBasicAuth"/>
            <argument id="omnipay_module.http_client.credentials" type="service"/>
            <argument>%omnipay.url%</argument>
        </service>

        <service class="HttpClient\Entities\Credentials" id="omnipay_module.http_client.credentials">
            <argument>%omnipay.username%</argument>
            <argument>%omnipay.password%</argument>
        </service>

        <service class="OmnipayModule\Resolvers\ResponseResolver" id="omnipay_module.resolvers.response_resolver"/>

        <service public="true" id="omnipay_module.deciders.omnipay_decider" class="OmnipayModule\Deciders\OmnipayDecider">
            <argument id="user_module.services.customer_availability" type="service"/>
            <argument id="customer_module.services.payment_gateway_service" type="service"/>
            <argument id="repositories.service_repository" type="service"/>
            <argument id="omnipay_module.api_client.omnipay_api_client" type="service"/>
            <argument id="payment_module.loggers.payment_gateway_logger" type="service"/>
        </service>

        <service class="Doctrine\Common\Cache\ArrayCache" id="omnipay_module.cache.card_provider_cache"/>

        <service id="omnipay_module.providers.omnipay_card_provider" class="OmnipayModule\Providers\OmnipayCardProvider">
            <argument id="omnipay_module.api_client.omnipay_api_client" type="service"/>
            <argument type="service" id="omnipay_module.card_serializer"/>
            <argument type="service" id="omnipay_module.cache.card_provider_cache"/>
        </service>

        <service id="omnipay_module.providers.omnipay_payment_provider" class="OmnipayModule\Providers\OmnipayPaymentProvider">
            <argument id="omnipay_module.api_client.omnipay_api_client" type="service"/>
            <argument type="service" id="omnipay_module.payment_serializer"/>
            <argument>%omnipay.cms_domain%</argument>
        </service>

        <service class="SerializingModule\Serializer" id="omnipay_module.card_serializer">
            <factory class="SerializingModule\SerializerFactory" method="create" />
            <call method="addMetadataDir">
                <argument>%omnipay_module.card_serializer.config_path%</argument>
                <argument>%omnipay_module.card_serializer.entities_prefix%</argument>
            </call>
            <call method="addSubscriber">
                <argument id="omnipay_module.subscribers.omnipay_card_response_subscriber" type="service"/>
            </call>
        </service>

        <service class="SerializingModule\Serializer" id="omnipay_module.payment_serializer">
            <factory class="SerializingModule\SerializerFactory" method="create" />
            <call method="addMetadataDir">
                <argument>%omnipay_module.payment_serializer.config_path%</argument>
                <argument>%omnipay_module.payment_serializer.entities_prefix%</argument>
            </call>
            <call method="addSubscriber">
                <argument id="omnipay_module.subscribers.omnipay_payment_response_subscriber" type="service"/>
            </call>
        </service>

        <service class="OmnipayModule\Subscribers\OmnipayPaymentResponseSubscriber"
                 id="omnipay_module.subscribers.omnipay_payment_response_subscriber"
        />

        <service class="OmnipayModule\Subscribers\OmnipayCardResponseSubscriber"
                 id="omnipay_module.subscribers.omnipay_card_response_subscriber"
        />

        <service class="OmnipayModule\Facades\ApiTokenPaymentFacade" id="omnipay_module.facades.api_token_payment_facade">
            <argument id="omnipay_module.providers.omnipay_card_provider" type="service"/>
            <argument id="omnipay_module.api_client.omnipay_api_client" type="service"/>
            <argument id="omnipay_module.deciders.omnipay_decider" type="service"/>
        </service>

        <service class="OmnipayModule\Emailers\DirectDebitEmailer"
                 id="omnipay_module.emailers.direct_debit_emailer">
            <argument id="email_module.gateways.default_gateway" type="service"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
            <argument id="url_generator" type="service"/>
            <argument id="user_module.creators.one_time_password_auth_token_creator" type="service"/>
        </service>

        <service id="omnipay_module.commands.move_sagepay_customers_to_omnipay"
                 class="OmnipayModule\Commands\MoveSagepayCustomersToOmnipayCommand">
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="services.customer_service" type="service"/>
            <argument id="omnipay_module.deciders.omnipay_decider" type="service"/>

            <tag name="cron.command" command-name="omnipay:move_sagepay_customers" action="execute" />
        </service>

        <service class="OmnipayModule\Services\DirectDebitService" id="omnipay_module.services.direct_debit_service">
            <argument id="payment_module.services.charge_service" type="service"/>
            <argument id="repositories.transaction_repository" type="service"/>
            <argument id="services.event_service" type="service"/>
        </service>

        <service id="omnipay_module.commands.retry_failed_direct_debit_payments_command"
                 class="OmnipayModule\Commands\RetryFailedDirectDebitPaymentsCommand">
            <argument id="services.order_service" type="service"/>
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="services.event_service" type="service"/>
            <argument id="omnipay_module.providers.omnipay_card_provider" type="service"/>
            <argument id="omnipay_module.services.direct_debit_service" type="service"/>
            <argument id="omnipay_module.emailers.direct_debit_emailer" type="service"/>

            <tag name="cron.command" command-name="omnipay:retry_direct_debit" action="execute" />
        </service>

        <service id="omnipay_module.commands.notify_admins_about_failed_direct_debit_command"
                 class="OmnipayModule\Commands\NotifyAdminsAboutFailedDirectDebitCommand">
            <argument id="repositories.transaction_repository" type="service"/>
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="omnipay_module.emailers.direct_debit_emailer" type="service"/>
            <argument id="url_generator" type="service"/>

            <tag name="cron.command" command-name="omnipay:direct_debit_notify_admin" action="execute" />
        </service>

        <service class="OmnipayModule\Deciders\PaymentStatusDecider" id="omnipay_module.deciders.payment_status_decider">
            <argument>%omnipay_module.permanent_failures%</argument>
        </service>
    </services>

</container>
