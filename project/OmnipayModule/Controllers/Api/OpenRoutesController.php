<?php

namespace OmnipayModule\Controllers\Api;

use BasketModule\Contracts\IBasket;
use CustomerModule\Entities\Settings\PaymentGatewaySetting;
use Entities\Customer;
use Libs\Basket;
use Libs\Exceptions\EntityNotFound;
use Models\OldModels\Transaction;
use OmnipayModule\Emailers\DirectDebitEmailer;
use OmnipayModule\Entities\OmnipayPaymentResponseDetails\OmnipayPaymentResponseDetails;
use OmnipayModule\Exceptions\MissingParameterException;
use OmnipayModule\Exceptions\UnprocessableTransactionException;
use OmnipayModule\Services\DirectDebitService;
use PayByPhoneModule\Services\PayByPhoneService;
use PaymentModule\Contracts\IPaymentRedirection;
use PaymentModule\Entities\InlinePaymentLog;
use PaymentModule\Loggers\PaymentGatewayLogger;
use PaymentModule\PaymentTypes\IPaymentMaker;
use PaymentModule\PaymentTypes\OmnipayPayment;
use PaymentModule\Processors\PaymentProcessor;
use Repositories\TransactionRepository;
use RouterModule\ApiController;
use Services\CustomerService;
use Services\TransactionService;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use UserModule\Exceptions\CustomerRequiredException;
use UserModule\Services\ICustomerAvailability;
use Utils\Helpers\ArrayHelper;

class OpenRoutesController extends ApiController
{
    private const PERMANENT_FAILURES = [
        OmnipayPaymentResponseDetails::DIRECT_DEBIT_ACCOUNT_CLOSED_ERROR,
        OmnipayPaymentResponseDetails::DIRECT_DEBIT_INVALID_ACCOUNT_ERROR,
    ];

    private const NO_TRANSACTION_FOUND_ERROR = 'Transaction not found.';

    public function __construct(
        private readonly CustomerService $customerService,
        private readonly ICustomerAvailability $customerAvailability,
        private readonly IPaymentMaker         $paymentMaker,
        private readonly IPaymentRedirection   $paymentRedirection,
        private readonly OmnipayPayment        $omnipayPayment,
        private readonly PaymentGatewayLogger  $paymentGatewayLogger,
        private readonly PaymentProcessor      $paymentProcessor,
        private readonly Request               $request,
        private readonly TransactionRepository $transactionRepository,
        private readonly TransactionService    $transactionService,
        private readonly DirectDebitEmailer    $directDebitEmailer,
        private readonly DirectDebitService    $directDebitService,
        private readonly PayByPhoneService     $payByPhoneService,
    ) {
        $this->setApikeyRequiredFalse();
    }

    public function validate(IBasket $basket)
    {
        $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_REDIRECT_FROM_STRIPE);

        try {
            $vendor = $this->validateRequest('vendor');
        } catch (MissingParameterException $e) {
            $vendor = OmnipayPayment::OMNIPAY_VENDOR_STRIPE;
        }

        try {
            $vendorId = $this->getVendorId($vendor);

            if ($transaction = $this->transactionRepository->getTransactionByOrderCode($vendorId)) {
                return $this->paymentRedirection->redirectToConfirmation($transaction->getOrder()->getId());
            }

            $vendorKey = $vendor === OmnipayPayment::OMNIPAY_VENDOR_STRIPE ? 'payment_intent' : 'vendor_tx_code';

            $paymentData = [
                'vendor' => $vendor,
                'vendorId' => $vendorId,
                $vendorKey => $vendorId,
            ];

            if ($this->payByPhoneService->isPayByPhone()) {
                return $this->paymentMaker->authorizePayment(
                    $this->customerService->getCustomerById($this->payByPhoneService->getPayByPhoneCustomerId()),
                    $this->payByPhoneService->getPhonePaymentBasket(),
                    $paymentData
                );
            }

            return $this->paymentMaker->authorizePayment($this->customerAvailability->requireCustomer(), $basket, $paymentData);
        } catch (CustomerRequiredException $e) {
            $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_REDIRECT_FROM_STRIPE_ERROR, $e->getMessage());

            return $this->paymentRedirection->getFailure($e);
        } catch (MissingParameterException $e) {
            $message = sprintf(
                'Missing %s parameter.',
                $vendor === OmnipayPayment::OMNIPAY_VENDOR_STRIPE ? 'payment_intent' : 'vendor_tx_code'
            );
            $this->logPaymentGatewayEvent(
                PaymentGatewayLogger::EVENT_REDIRECT_FROM_STRIPE_ERROR,
                sprintf('%s - %s', $message, $e->getMessage())
            );

            return $this->paymentRedirection->getFailure($e);
        }
    }

    public function complete(): JsonResponse
    {
        $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_ASYNC_PAYMENT);

        try {
            $vendor = $this->validateRequest('vendor');
        } catch (MissingParameterException $e) {
            $vendor = OmnipayPayment::OMNIPAY_VENDOR_STRIPE;
        }

        try {
            $vendorId = $this->getVendorId($vendor);

            if ($transaction = $this->transactionRepository->getTransactionByOrderCode($vendorId)) {
                return new JsonResponse([
                    'order_id' => $transaction->getOrder()->getId(),
                    'transaction_id' => $transaction->getId(),
                ]);
            }

            $customerId = $this->validateRequest('customer_id');
            $customer = $this->customerService->getCustomerById($customerId, true);

            $serializedBasket = $this->validateRequest('serialized_basket');
            $basket = new Basket('webhook');
            $basket->initializeFromSerializedString($serializedBasket);

            $paymentData = [
                'vendor' => $vendor,
                'vendorId' => $vendorId,
                'isFromWebhook' => true,
            ];

            $paymentResponse = $this->omnipayPayment->authorizePayment(
                $basket->getPrice('total', true, $customer),
                $paymentData
            );

            $order = $this->paymentProcessor->processPayment($customer, $basket, $paymentResponse);

            $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_ASYNC_PAYMENT_SUCCESS);

            return new JsonResponse([
                'order_id' => $order->getId(),
                'transaction_id' => $this->transactionService->getTransactionByOrderId($order->getId())?->getId(),
            ]);
        } catch (\Exception $e) {
            $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_ASYNC_PAYMENT_ERROR, $e->getMessage());

            return $this->apiErrorResponse($e->getMessage(), [
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function credit(IBasket $basket)
    {
        $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_CREDIT_PAYMENT);

        try {
            $vendor = $this->validateRequest('vendor');
        } catch (MissingParameterException $e) {
            $vendor = OmnipayPayment::OMNIPAY_VENDOR_STRIPE;
        }

        try {
            try {
                /** @var Customer $customer */
                $customer = $this->customerAvailability->requireCustomer();
            } catch (CustomerRequiredException $e) {
                $customerId = $this->validateRequest('customer_id');
                $customer = $this->customerService->getCustomerById($customerId, true);
            }

            $vendorId = $this->getVendorId($vendor);
            $paymentData = [
                'vendor' => $vendor,
                'vendorId' => $vendorId,
                'useCredit' => true,
                'customer' => $customer,
            ];

            try {
                $origin = $this->validateRequest('origin');
            } catch (MissingParameterException $e) {
                $origin = OmnipayPayment::PAYMENT_ORIGIN_PAYMENT_PAGE;
            }

            if ($origin !== OmnipayPayment::PAYMENT_ORIGIN_INLINE) {
                return $this->paymentMaker->authorizePayment($customer, $basket, $paymentData);
            }

            $paymentResponse = $this->omnipayPayment->authorizePayment(
                $basket->getPrice('total', true, $customer),
                $paymentData
            );

            $order = $this->paymentProcessor->processPayment($customer, $basket, $paymentResponse);

            $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_CREDIT_PAYMENT_SUCCESS);

            return new JsonResponse([
                'order_id' => $order->getId(),
                'transaction_id' => $this->transactionService->getTransactionByOrderId($order->getId())?->getId(),
                'is_whole_sale' => $customer->isWholesale(),
            ]);
        } catch (\Exception $e) {
            $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_CREDIT_PAYMENT_ERROR, $e->getMessage());

            return $this->paymentRedirection->getFailure($e);
        }
    }

    public function inline(): JsonResponse
    {
        $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_INLINE_PAYMENT);

        try {
            $vendor = $this->validateRequest('vendor');
        } catch (MissingParameterException $e) {
            $vendor = OmnipayPayment::OMNIPAY_VENDOR_STRIPE;
        }

        try {
            $vendorId = $this->getVendorId($vendor);

            if ($transaction = $this->transactionRepository->getTransactionByOrderCode($vendorId)) {
                return new JsonResponse([
                    'order_id' => $transaction->getOrder()->getId(),
                    'transaction_id' => $transaction->getId(),
                ]);
            }

            $customerId = $this->validateRequest('customer_id');
            $customer = $this->customerService->getCustomerById($customerId, true);

            $serializedBasket = $this->validateRequest('serialized_basket');
            $basket = new Basket('inline');
            $basket->initializeFromSerializedString($serializedBasket);

            try {
                $useCredit = $this->validateRequest('use_credit');
            } catch (MissingParameterException $e) {
                $useCredit = false;
            }

            $paymentData = [
                'vendor' => $vendor,
                'vendorId' => $vendorId,
                'useCredit' => $useCredit,
                'customer' => $customer,
            ];

            $paymentResponse = $this->omnipayPayment->authorizePayment(
                $basket->getPrice('total', true, $customer),
                $paymentData
            );

            $order = $this->paymentProcessor->processPayment($customer, $basket, $paymentResponse);

            $firstItem = $basket->getFirstItem();
            $company = $firstItem ? $firstItem->getCompanyEntity() : null;

            if (!empty($firstItem) && !empty($company)) {
                $log = new InlinePaymentLog(
                    InlinePaymentLog::TYPE_PAYMENT_SUCCEEDED,
                    $firstItem->getId(),
                    'not-available',
                    $company->getCustomer(),
                    $company,
                    $basket->getPrice('total'),
                    'not-available',
                    'Pay',
                    null,
                    $order
                );

                $this->transactionRepository->persist($log);
                $this->transactionRepository->flush();
            }

            $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_INLINE_PAYMENT_SUCCESS);

            return new JsonResponse([
                'order_id' => $order->getId(),
                'transaction_id' => $this->transactionService->getTransactionByOrderId($order->getId())?->getId(),
                'is_whole_sale' => $customer->isWholesale(),
            ]);
        } catch (EntityNotFound $e) {
            $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_INLINE_PAYMENT_ERROR, $e->getMessage());

            if (!isset($basket)) {
                $basket = new Basket('inline');
            }

            $firstItem = $basket->getFirstItem();
            $company = $firstItem ? $firstItem->getCompanyEntity() : null;

            if (!empty($firstItem) && !empty($company)) {
                $log = new InlinePaymentLog(
                    InlinePaymentLog::TYPE_PAYMENT_FAILED,
                    $firstItem ? $firstItem->getId() : 0,
                    'not-available',
                    $company->getCustomer(),
                    $company,
                    $basket->getPrice('total'),
                    'not-available',
                    'Pay',
                    null,
                    null
                );

                $this->transactionRepository->persist($log);
                $this->transactionRepository->flush();
            }

            return $this->paymentRedirection->getFailure($e);
        } catch (MissingParameterException|\Exception $e) {
            $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_INLINE_PAYMENT_ERROR);

            return $this->apiErrorResponse($e->getMessage(), [
                'code' => $e->getCode(),
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function updateDirectDebitPayment(): JsonResponse
    {
        try {
            $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_DIRECT_DEBIT);
            [$transaction, $paymentStatus] = $this->getDirectDebitRequestData();
            $this->updateTransactionStatus($transaction, $paymentStatus);
            $this->processTransaction($transaction, $paymentStatus);

            return $this->apiSuccessResponse(['status' => 'success', 'transaction' => $transaction->getId()]);
        } catch (UnprocessableTransactionException $e) {
            $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_DIRECT_DEBIT_ERROR, $e->getMessage());

            return $this->apiErrorResponse(error: $e->getMessage(), statusCode: $e->getCode(), logError: false);
        } catch (\Throwable $e) {
            $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_DIRECT_DEBIT_ERROR, $e->getMessage());

            if ($e->getMessage() === self::NO_TRANSACTION_FOUND_ERROR) {
                return $this->apiErrorResponse(error: $e->getMessage(), logError: false);
            }

            return $this->apiErrorResponse($e->getMessage(), ['code' => Response::HTTP_BAD_REQUEST, 'message' => $e->getMessage()]);
        }
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function processTransaction(\Entities\Transaction $transaction, string $paymentStatus): void
    {
        match ($paymentStatus) {
            Transaction::STATUS_SUCCEEDED => $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_DIRECT_DEBIT_SUCCEEDED),
            Transaction::STATUS_FAILED => $this->processFailedTransaction($transaction),
            default => throw new \InvalidArgumentException('Invalid payment status'),
        };
    }

    /**
     * @throws \DateMalformedStringException
     */
    private function processFailedTransaction(\Entities\Transaction $failedTransaction): void
    {
        $errorCode   = $this->getDirectDebitErrorCode();
        $isPermanent = in_array($errorCode, self::PERMANENT_FAILURES, true);

        $method = $isPermanent
            ? 'sendPermanentFailureEmail'
            : 'sendFailedPaymentEmail';

        $this->directDebitEmailer->$method($failedTransaction);

        $isPermanent
            ? $this->directDebitService->saveRetryByCardEvent($failedTransaction->getOrder()->getId())
            : $this->transactionService->updateTransactionError($failedTransaction, $errorCode);

        $this->directDebitService->setRetryAt($failedTransaction);
    }

    private function updateTransactionStatus(\Entities\Transaction $transaction, string $paymentStatus): void
    {
        $this->transactionService->updateTransactionStatusId($transaction, $paymentStatus);
        $this->logPaymentGatewayEvent(PaymentGatewayLogger::EVENT_DIRECT_DEBIT_UPDATED);
    }

    /**
     * @throws MissingParameterException
     */
    private function validateRequest(string $parameter)
    {
        if (!$parameters = json_decode($this->request->getContent(), true)) {
            $parameters = $this->request->query->all();
        }

        $parameterValue = ArrayHelper::get($parameters, $parameter, null);

        if (!$parameterValue) {
            throw new MissingParameterException($parameter, Response::HTTP_BAD_REQUEST);
        }

        return $parameterValue;
    }

    /**
     * @throws MissingParameterException
     */
    private function getVendorId(string $vendor): string
    {
        return $vendor === OmnipayPayment::OMNIPAY_VENDOR_STRIPE
            ? $this->validateRequest('payment_intent')
            : $this->validateRequest('vendor_tx_code');
    }

    private function logPaymentGatewayEvent(string $event, ?string $exceptionMessage = null, ?string $errorCode = null): void
    {
        $log = ['data' => !empty($this->request->query->all()) ? $this->request->query->all() : []];

        if ($exceptionMessage) {
            $log['exception'] = $exceptionMessage;
        }
        if ($errorCode) {
            $log['error_code'] = $errorCode;
        }

        $this->paymentGatewayLogger->logEvent(
            $event,
            [],
            $log,
            PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
        );
    }

    /**
     * @throws MissingParameterException
     * @throws UnprocessableTransactionException
     */
    private function getDirectDebitRequestData(): array
    {
        return [
            $this->validateTransaction($this->validateRequest('payment_intent_id')),
            $this->validateRequest('payment_status'),
        ];
    }

    private function getDirectDebitErrorCode(): ?string
    {
        try {
            return $this->validateRequest('error_code');
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * @throws UnprocessableTransactionException
     */
    private function validateTransaction(string $paymentIntentId): \Entities\Transaction
    {
        $transaction = $this->transactionService->getTransactionByOrderCode($paymentIntentId);

        if (empty($transaction)) {
            throw new NotFoundHttpException('Transaction not found.');
        }

        if ($transaction->getStatusId() !== Transaction::STATUS_PROCESSING) {
            throw new UnprocessableTransactionException();
        }

        return $transaction;
    }
}
