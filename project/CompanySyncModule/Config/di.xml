<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="CompanySyncModule\Helpers\ResponseTimeHelper" id="company_sync_module.helpers.response_time_helper">
            <argument type="service" id="company_sync_module.repositories.company_repository"/>
        </service>

        <service class="CompanySyncModule\Repositories\CompaniesSyncRepository" id="company_sync_module.repositories.companies_sync_repository">
            <argument id="redis" type="service"/>
            <argument>cms</argument>
            <argument type="service" id="company_sync_module.serializer"/>
        </service>

        <service class="CompanySyncModule\Facades\CompaniesSyncFacade" id="company_sync_module.facades.companies_sync_facade">
            <argument id="company_sync_module.repositories.companies_sync_repository" type="service"/>
            <argument id="company_sync_module.helpers.response_time_helper" type="service"/>
            <argument type="service" id="company_sync_module.repositories.company_repository"/>
            <argument type="service" id="config"/>
            <argument type="service" id="monolog.logger.sync_companies"/>
        </service>

        <service class="CompanySyncModule\Mailers\SyncCompletedMailer" id="company_sync_module.mailers.sync_completed_mailer">
            <argument id="email_module.gateways.default_gateway" type="service"/>
            <argument type="service" id="repositories.customer_repository"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
        </service>

        <service class="CompanySyncModule\Subscribers\SyncSubscriber" id="company_sync_module.subscribers.sync_subscriber">
            <argument id="company_sync_module.mailers.sync_completed_mailer" type="service"/>
            <argument id="company_sync_module.repositories.companies_sync_repository" type="service"/>
            <argument type="service" id="monolog.logger.sync_companies"/>
            <tag name="kernel.event_subscriber" />
        </service>

        <service class="CompanySyncModule\Commands\SyncCompaniesCommand" id="company_module.commands.sync_companies_command">
            <argument type="service" id="services.customer_service"/>
            <argument type="service" id="company_sync_module.repositories.company_repository"/>
            <argument type="service" id="company_sync_module.services.synchronization_service"/>
            <argument type="service" id="monolog.logger.sync_companies"/>
            <argument type="service" id="symfony.event_dispatcher"/>
            <argument type="service" id="doctrine.orm.entity_manager"/>


            <tag name="console.command" command-name="sync:companies" action="sync" />
        </service>

        <service class="CompanySyncModule\Repositories\CompanyRepository" id="company_sync_module.repositories.company_repository">
            <argument type="service" id="repositories.company_repository"/>
        </service>

        <service class="CompanySyncModule\Services\SynchronizationService" id="company_sync_module.services.synchronization_service">
            <argument type="service" id="companies_house.companies_house" />
            <argument type="service" id="services.company_service" />
            <argument type="service" id="error.loggers.monolog" />
            <argument type="service" id="companies_house_module.repositories.member_repository"/>
            <argument type="service" id="company_sync_module.services.old_company_synchronization" />
        </service>

        <service class="SerializingModule\Serializer" id="company_sync_module.serializer">
            <factory class="SerializingModule\SerializerFactory" method="create"/>
            <call method="addMetadataDir">
                <argument>%company_sync_module.serializer.config_path%</argument>
                <argument>%company_sync_module.serializer.entities_prefix%</argument>
            </call>
        </service>

        <service class="CompanySyncModule\Commands\SyncCompaniesNumbersCommand" id="company_sync_module.commands.sync_companies_numbers_command">
            <argument id="repositories.company_repository" type="service"/>
            <argument id="company_sync_module.services.synchronization_service" type="service"/>
            <argument id="cron.loggers.default_logger" type="service"/>
            <argument id="company_sync_module.mailers.sync_report_mailer" type="service" />
            <tag name="console.command" command-name="sync:companies:admin" action="sync" />
        </service>

        <service class="CompanySyncModule\Controllers\Admin\CompaniesSyncController" id="company_sync_module.controllers.admin.companies_sync_controller">
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="templating_module.renderers.admin_renderer" type="service"/>
            <argument id="company_sync_module.facades.companies_admin_sync_facade" type="service"/>
        </service>

        <service class="CompanySyncModule\Facades\CompaniesAdminSyncFacade" id="company_sync_module.facades.companies_admin_sync_facade">
            <argument id="config" type="service"/>
            <argument id="company_sync_module.commands.sync_companies_numbers_command" type="service"/>
        </service>

        <service class="CompanySyncModule\Mailers\SyncReportMailer" id="company_sync_module.mailers.sync_report_mailer">
            <argument id="email_module.gateways.default_gateway" type="service"/>
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
            <argument>%company_sync_module.companies_sync.report_template_path%</argument>
        </service>

        <service id="company_sync_module.loggers.synchronization_logger" class="CompanySyncModule\Loggers\SynchronizationLogger">
            <argument type="service">
                <service class="Monolog\Logger">
                    <argument type="string">synchronization</argument>
                    <argument type="collection">
                        <argument type="service" id="monolog.handler.stream_handler.stdout" />
                        <argument type="service">
                            <service class="Monolog\Handler\StreamHandler">
                                <argument type="string">%log.dir.const%/synchronization.output</argument>
                            </service>
                        </argument>
                        <argument type="service" id="error_module.handlers.stack_driver_handler.debug" />
                    </argument>
                </service>
            </argument>
        </service>

        <service class="CompanySyncModule\Controllers\Api\SyncController" id="company_sync_module.controllers.api.sync_controller">
            <argument type="service" id="doctrine.orm.entity_manager"/>
            <argument type="service" id="company_sync_module.services.synchronization_service"/>
            <argument type="service" id="services.company_service"/>
        </service>

        <service class="CompanySyncModule\Services\OldCompanySynchronization" id="company_sync_module.services.old_company_synchronization">
            <argument id="services.company_service" type="service"/>
            <argument id="error.loggers.monolog" type="service"/>
        </service>

        <service class="CompanySyncModule\Commands\TestApiResultsCommand" id="company_sync_module.commands.test_api_results_command">
            <argument id="companies_house.companies_house" type="service"/>
            <tag name="console.command" command-name="test:ch:country" action="match" />
        </service>


    </services>
</container>
