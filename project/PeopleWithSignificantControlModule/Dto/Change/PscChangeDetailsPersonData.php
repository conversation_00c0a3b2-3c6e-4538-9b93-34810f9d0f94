<?php

namespace PeopleWithSignificantControlModule\Dto\Change;

use CompaniesHouseModule\Dto\ChangeResidentialAddressData;
use CompaniesHouseModule\Dto\ChangeServiceAddressData;
use CompaniesHouseModule\Dto\DateOfChangeData;
use CompaniesHouseModule\Entities\PscPerson;
use PeopleWithSignificantControlModule\Interfaces\IChangeable;

class PscChangeDetailsPersonData implements IChangeable
{
    /**
     * @var ChangePersonData
     */
    private $person;

    /**
     * @var ChangeServiceAddressData
     */
    private $serviceAddress;

    /**
     * @var ChangeResidentialAddressData
     */
    private $residentialAddress;

    /**
     * @var ChangeNatureOfControlsData
     */
    private $natureOfControls;

    /**
     * @var DateOfChangeData
     */
    private $dateOfChange;

    /**
     * @param PscPerson $person
     * @return PscChangeDetailsPersonData
     */
    public static function fromPerson(PscPerson $person): PscChangeDetailsPersonData
    {
        $self = new self();
        $self->setPerson(ChangePersonData::fromPerson($person));
        $self->setServiceAddress(ChangeServiceAddressData::fromAddress($person->getAddress()));
        $self->setResidentialAddress(ChangeResidentialAddressData::fromAddress($person->getResidentialAddress()));
        $self->setNatureOfControls(ChangeNatureOfControlsData::fromNatureOfControl($person->getNatureOfControl()));
        $self->setDateOfChange(DateOfChangeData::default());
        return $self;
    }

    /**
     * @return ChangePersonData
     */
    public function getPerson(): ?ChangePersonData
    {
        return $this->person;
    }

    /**
     * @param ChangePersonData $person
     */
    public function setPerson(ChangePersonData $person): void
    {
        $this->person = $person;
    }

    /**
     * @return ChangeServiceAddressData
     */
    public function getServiceAddress(): ChangeServiceAddressData
    {
        return $this->serviceAddress;
    }

    /**
     * @param ChangeServiceAddressData $serviceAddress
     */
    public function setServiceAddress(ChangeServiceAddressData $serviceAddress): void
    {
        $this->serviceAddress = $serviceAddress;
    }

    /**
     * @return ChangeResidentialAddressData
     */
    public function getResidentialAddress(): ChangeResidentialAddressData
    {
        return $this->residentialAddress;
    }

    /**
     * @param ChangeResidentialAddressData $residentialAddress
     */
    public function setResidentialAddress(ChangeResidentialAddressData $residentialAddress): void
    {
        $this->residentialAddress = $residentialAddress;
    }

    /**
     * @return ChangeNatureOfControlsData
     */
    public function getNatureOfControls(): ChangeNatureOfControlsData
    {
        return $this->natureOfControls;
    }

    /**
     * @param ChangeNatureOfControlsData $natureOfControls
     */
    public function setNatureOfControls(ChangeNatureOfControlsData $natureOfControls): void
    {
        $this->natureOfControls = $natureOfControls;
    }

    /**
     * @return DateOfChangeData
     */
    public function getDateOfChange(): DateOfChangeData
    {
        return $this->dateOfChange;
    }

    /**
     * @param DateOfChangeData $dateOfChange
     */
    public function setDateOfChange(DateOfChangeData $dateOfChange): void
    {
        $this->dateOfChange = $dateOfChange;
    }

    public function isChange(): bool
    {
        return $this->person->isChange() || $this->serviceAddress->isChange()
            || $this->residentialAddress->isChange() || $this->natureOfControls->isChange();
    }
}
