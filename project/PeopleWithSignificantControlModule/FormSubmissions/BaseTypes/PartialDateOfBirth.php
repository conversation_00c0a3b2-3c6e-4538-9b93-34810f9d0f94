<?php

namespace PeopleWithSignificantControlModule\FormSubmissions\BaseTypes;

use DateTime;
use Utils\Date;

class PartialDateOfBirth
{
    /**
     * @var int
     */
    private $month;

    /**
     * @var int
     */
    private $year;

    /**
     * @param int $month
     * @param int $year
     */
    public function __construct(int $month, int $year)
    {
        $this->month = $month;
        $this->year = $year;
    }

    /**
     * @param DateTime $date
     * @return PartialDateOfBirth
     */
    public static function fromDateTime(DateTime $date): PartialDateOfBirth
    {
        return new self($date->format('n'), $date->format('Y'));
    }

    /**
     * @param Date $date
     * @return PartialDateOfBirth
     */
    public static function fromDate(Date $date): PartialDateOfBirth
    {
        return new self($date->format('n'), $date->format('Y'));
    }

    /**
     * @return int
     */
    public function getMonth(): ?int
    {
        return $this->month;
    }

    /**
     * @return int
     */
    public function getYear(): ?int
    {
        return $this->year;
    }
}
