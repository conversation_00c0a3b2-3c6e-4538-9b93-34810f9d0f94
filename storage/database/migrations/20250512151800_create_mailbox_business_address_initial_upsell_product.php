<?php

use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Phinx\Migration\AbstractMigration;

class CreateMailboxBusinessAddressInitialUpsellProduct extends AbstractMigration
{
    public const PRODUCT_NAME = 'mailbox_business_address_initial_upsell';
    public const RENEWAL_PRODUCT_NAME = 'mailbox_business_address_renewal_monthly';

    public function up()
    {
        $helper = new NodeMigrationHelper($this);

        if ($nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME)) {
            return $nodeId;
        }

        $page = new Page('Mailbox Business Address Initial (Upsell)');

        $renewalNodeId = $helper->getExistingNodeId(self::RENEWAL_PRODUCT_NAME);
        echo $renewalNodeId;
        $properties = [
            new Property('address1', '20'),
            new Property('address2', 'Wenlock Road'),
            new Property('address3', ''),
            new Property('additional', ''),
            new Property('associatedDescription', "The registered office is the official address of a limited company and must be a physical location in the UK. Our service is ideal if you want to protect your residential address from being shown on the public register or if you're an overseas customer who needs a UK address. NB. This is not a full mail forwarding service. Only mail from Companies House, HMRC and other Government bodies will be forwarded (this mail is forwarded to you free of charge)"),
            new Property('associatedIconClass', 'fa-building'),
            new Property('associatedImageId', '1187'),
            new Property('associatedPrice', '29.99'),
            new Property('associatedProducts3', ''),
            new Property('associatedText', "Use our prestigious N1 London address as your company's registered office and keep your residential address off the public register."),
            new Property('availableForPayByPhone', 'NOT_AVAILABLE'),
            new Property('bankingEnabled', ''),
            new Property('bankingOptions', ''),
            new Property('bankingRequired', ''),
            new Property('basketText', ''),
            new Property('blacklistedContainedProducts', ''),
            new Property('blacklistedEquivalentProducts', ''),
            new Property('cashBackAmount', '0'),
            new Property('conditionedById', ''),
            new Property('countryId', 'GB-ENG'),
            new Property('county', ''),
            new Property('customAssociatedIcon', ''),
            new Property('duration', '+1 month'),
            new Property('emailCmsFileAttachments', '4'),
            new Property('emailText', $this->getEmailText()),
            new Property('isAutoRenewalAllowed', '1'),
            new Property('isFeefoEnabled', ''),
            new Property('isIdCheckRequired', '1'),
            new Property('isInitialProduct', '1'),
            new Property('isRenewalProduct', ''),
            new Property('isVoServiceEligible', ''),
            new Property('lockCompany', ''),
            new Property('markUp', ''),
            new Property('maxQuantityOne', ''),
            new Property('nonVatableDescription', ''),
            new Property('nonVatableValue', ''),
            new Property('notApplyVat', ''),
            new Property('offerPrice', '29.99'),
            new Property('offerProductId', ''),
            new Property('onlyOne', '0'),
            new Property('onlyOneItem', ''),
            new Property('onlyOurCompanies', '0'),
            new Property('optionalRenewalProductId', ''),
            new Property('postcode', 'N1 7GU'),
            new Property('price', '29.99'),
            new Property('printedCertificateOptionEnabled', ''),
            new Property('productValue', '29.99'),
            new Property('registrationReviewEnabled', ''),
            new Property('removableFromBasket', '1'),
            new Property('removeFromBasketConfirmation', ''),
            new Property('renewalDtStartBehavior', ''),
            new Property('renewalProductId', $renewalNodeId),
            new Property('requiredCompanyNumber', '1'),
            new Property('requiredCorePackage', ''),
            new Property('requiredIncorporatedCompanyNumber', '0'),
            new Property('responsibleEmails', ''),
            new Property('sageNominalCode', ''),
            new Property('saveToCompany', '0'),
            new Property('serviceTypeId', 'REGISTERED_OFFICE'),
            new Property('showInMyServicesPage', ''),
            new Property('specialPrice', '29.99'),
            new Property('toolkitOfferTypes', ''),
            new Property('town', 'London'),
            new Property('upgradeDescription', $this->getUpgradeDescription()),
            new Property('upgradeImageId', '425'),
            new Property('voServiceDurationInMonths', '0'),
            new Property('wholesalePrice', '29.99'),
        ];

        $node = new Node(
            $page,
            $properties,
            self::PRODUCT_NAME,
            164,
            'DefaultControler',
            'RegisterOfficeAdminControler',
            140
        );
        $node->setExcludeFromSitemap(false);
        $helper = new NodeMigrationHelper($this);
        $helper->create($node);
    }

    public function down()
    {
        $helper = new NodeMigrationHelper($this);
        $nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME);

        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getEmailText(): string
    {
        return <<<EOD
    <p>Thank you for choosing our Mail Forwarding Service and trusting us to handle your mail securely and professionally. You will need to <a href="https://www.companiesmadesimple.com/company-inbox/settings/?utm_source=&utm_medium=Email&utm_campaign=Mail-Forwarding-1933-Purchase-Confirmation-set-forwarding-address&utm_content=" target="_blank">finish setting up your service</a> before you can use it. Please follow the instructions below.</p>
    <h3>What’s Included in Your Service:</h3>
    <ul>
        <li><strong>Premium Mailbox with Secure Handling</strong><br>
            All government and business mail will be scanned and securely stored in your online mailbox for easy access.</li>
        <li><strong>Fast & Reliable Physical Mail Forwarding</strong><br>
            We’ll forward up to 10 letters per month to your chosen address.</li>
        <li><strong>Parcel Reception & Notifications</strong><br>
            You’ll be notified when a parcel arrives. Choose to collect it or have it forwarded for a small fee.</li>
        <li><strong>Use of Registered Office Address & Directors’ Service Address</strong><br>
            Use our 20 Wenlock Road address as your Registered Office Address and Directors’ Service Address. This protects your privacy by keeping your residential address off public records.</li>
    </ul>
    <h3>Finish setting up your service:</h3>
    <h4>Tell us where you want to receive your mail</h4>
    <p>To enable physical mail forwarding that is included with your service, please provide the address you’d like us to use.</p>
    <p><a href="https://www.companiesmadesimple.com/company-inbox/settings/?utm_source=&utm_medium=Email&utm_campaign=Mail-Forwarding-1933-Purchase-Confirmation-set-forwarding-address&utm_content=" target="_blank">Set My Forwarding Address Now</a></p>
    <h4>Check your ID status</h4>
    <p>To complete the setup of your account, we are required to verify the identity of all account holders and company officers. This is in line with Anti-Money Laundering (AML) and Know Your Customer (KYC) regulations.</p> 
    <p>The ID verification process is securely conducted through Credas, a trusted industry-leading provider.  Learn more <a href="https://support.companiesmadesimple.com/hc/en-us/articles/************-Proof-of-ID-and-Address-Requirements-Comprehensive-Guide" target="_blank">here</a>.</p>
    <p>If you haven’t already completed an ID check, please do so now to ensure your service is activated without delay.</p>
    <p><a href="https://www.companiesmadesimple.com/id-check/?utm_source=&utm_medium=Email &utm_campaign=Mail-Forwarding-1933-Purchase-Confirmation-ID-verification&utm_content=" target="_blank">Check My ID Status Now</a></p>
EOD;
    }

    private function getUpgradeDescription(): string
    {
        return <<<EOD
This is a 'Mailbox Business Address Initial (Upsell)' placeholder for the upgrade description text
EOD;
    }
}
