<?php

use Models\Products\AnnualReturn;
use Models\Products\BundlePackage;
use Models\Products\CompanyNameChange;
use Models\Products\Package;
use Models\Products\Product;
use Phinx\Migration\AbstractMigration;
use MigrationModule\Helpers\NodeMigrationHelper;

class ChPriceChangeAllProducts extends AbstractMigration
{
    const PRICE_PROPERTY = 'price';
    const WHOLESALE_PRICE_PROPERTY = 'wholesalePrice';
    const ASSOCIATED_PRICE_PROPERTY = 'associatedPrice';
    const PRODUCT_VALUE_PROPERTY = 'productValue';
    const OFFER_PRICE_PROPERTY = 'offerPrice';
    const NONVATABLE_VALUE_PROPERTY = 'nonVatableValue';

    public function up()
    {
        $migrationHelper = new NodeMigrationHelper($this);
        $products = [
            Package::PACKAGE_BASIC => ['59.99', '50'],
            Package::PACKAGE_PRIVACY => ['79.99', '50'],
            Package::PACKAGE_COMPREHENSIVE => ['99.99', '84'],
            Package::PACKAGE_ULTIMATE => ['169.99', '84'],
            Package::PACKAGE_LIMITED_LIABILITY_PARTNERSHIP => ['99.99', '50'],
            Package::PACKAGE_LIMITED_BY_GUARANTEE_WITHOUT_REGISTERED_OFFICE => ['89.99', '50'],
            Package::PACKAGE_LIMITED_BY_GUARANTEE => ['129.99', '50'],
            Package::PACKAGE_INTERNATIONAL => ['179.99', '84'],
            Package::PACKAGE_CONTRACTOR => ['59.99', '50'],
            Product::PRODUCT_GUARANTEED_SAME_DAY => ['39.99', '28'],
            CompanyNameChange::CHANGE_NAME_SAME_DAY_PRODUCT => ['129.99', '83'],
            Package::PACKAGE_WHOLESALE_STARTER => ['54.99', '50'],
            Package::PACKAGE_WHOLESALE_EXECUTIVE => ['59.99', '50'],
            Package::PACKAGE_WHOLESALE_PROFESSIONAL => ['79.99', '50'],
            AnnualReturn::ANNUAL_RETURN_FEE_PRODUCT => ['39.99', '34'],
            AnnualReturn::ANNUAL_RETURN_SERVICE_PRODUCT => ['69.99', '34'],
            AnnualReturn::ANNUAL_RETURN_EXPRESS_SERVICE_PRODUCT => ['79.99', '34'],
            Product::PRODUCT_PSC_REGISTER_CONFIRMATION_STATEMENT => ['69.99', '34'],
            Product::PRODUCT_SHELF_COMPANY_2023_STANDARD => ['199.99', '10'],
            Product::PRODUCT_SHELF_COMPANY_2023_PREMIUM => ['249.99', '44'],
            Product::PRODUCT_SHELF_COMPANY_2024_STANDARD => ['149.99', '10'],
            Product::PRODUCT_SHELF_COMPANY_2024_PREMIUM => ['199.99', '44'],
            Package::PACKAGE_RESERVE_COMPANY_NAME => ['99.99', '50'],
            Package::PACKAGE_RESERVE_COMPANY_NAME_ANNUAL_RENEWAL => ['99.99', '34'],
            Product::PRODUCT_TRANSFER_SHARES_CONFIRMATION_STATEMENT_1 => ['79.99', '34'],
            Product::PRODUCT_TRANSFER_SHARES_CONFIRMATION_STATEMENT_MULTIPLE => ['89.99', '34'],
            Product::PRODUCT_TRANSFER_SHARES_CONFIRMATION_STATEMENT_4_5 => ['99.99', '34'],
            Product::PRODUCT_ISSUE_SHARES_CONFIRMATION_STATEMENT_1 => ['79.99', '34'],
            Product::PRODUCT_ISSUE_SHARES_CONFIRMATION_STATEMENT_MULTIPLE => ['89.99', '34'],
            Product::PRODUCT_ISSUE_SHARES_CONFIRMATION_STATEMENT_4_5 => ['99.99', '34'],
            BundlePackage::BUNDLE_DORMANT_COMPANY_ACCOUNTS_CONFIRMATION_STATEMENT => ['99.99', '34'],
            Product::PRODUCT_COMPANY_DISSOLUTION => ['89.99', '33'],
            Product::PRODUCT_COMPANY_DISSOLUTION_EXPRESS => ['99.99', '33'],
            CompanyNameChange::CHANGE_NAME_PRODUCT => ['79.99', '20'],
            Product::PRODUCT_DORMANT_COMPANY_ACCOUNTS => ['79.99', '']
        ];

        foreach ($products as $productName => $newValues) {
            $nodeId = $migrationHelper->getExistingNodeId($productName);
            if (empty($nodeId))
                throw new Exception(sprintf('Node with name %s does not exist!', $productName));

            [$newPrice, $newNonvatableValue] = $newValues;

            $migrationHelper->updateProperty($nodeId, self::PRICE_PROPERTY, $newPrice);
            $migrationHelper->updateProperty($nodeId, self::WHOLESALE_PRICE_PROPERTY, $newPrice);
            $migrationHelper->updateProperty($nodeId, self::ASSOCIATED_PRICE_PROPERTY, $newPrice);
            $migrationHelper->updateProperty($nodeId, self::PRODUCT_VALUE_PROPERTY, $newPrice);
            $migrationHelper->updateProperty($nodeId, self::OFFER_PRICE_PROPERTY, $newPrice);
            $migrationHelper->updateProperty($nodeId, self::NONVATABLE_VALUE_PROPERTY, $newNonvatableValue);
        }
    }

    public function down()
    {
        $migrationHelper = new NodeMigrationHelper($this);
        $products = [
            Package::PACKAGE_BASIC => ['12.99', '10'],
            Package::PACKAGE_PRIVACY => ['19.99', '10'],
            Package::PACKAGE_COMPREHENSIVE => ['44.99', '10'],
            Package::PACKAGE_ULTIMATE => ['99.99', '10'],
            Package::PACKAGE_LIMITED_LIABILITY_PARTNERSHIP => ['59.99', '10'],
            Package::PACKAGE_LIMITED_BY_GUARANTEE_WITHOUT_REGISTERED_OFFICE => ['49.99', '10'],
            Package::PACKAGE_LIMITED_BY_GUARANTEE => ['89.99', '10'],
            Package::PACKAGE_INTERNATIONAL => ['139.99', '10'],
            Package::PACKAGE_CONTRACTOR => ['49.99', '10'],
            Product::PRODUCT_GUARANTEED_SAME_DAY => ['24.99', ''],
            CompanyNameChange::CHANGE_NAME_SAME_DAY_PRODUCT => ['79.99', ''],
            Package::PACKAGE_WHOLESALE_STARTER => ['11.99', '10'],
            Package::PACKAGE_WHOLESALE_EXECUTIVE => ['15.99', '10'],
            Package::PACKAGE_WHOLESALE_PROFESSIONAL => ['34.99', '10'],
            AnnualReturn::ANNUAL_RETURN_FEE_PRODUCT => ['14.99', '13'],
            AnnualReturn::ANNUAL_RETURN_SERVICE_PRODUCT => ['49.99', '13'],
            AnnualReturn::ANNUAL_RETURN_EXPRESS_SERVICE_PRODUCT => ['59.99', '13'],
            Product::PRODUCT_PSC_REGISTER_CONFIRMATION_STATEMENT => ['49.99', '13'],
            Product::PRODUCT_SHELF_COMPANY_2023_STANDARD => ['149.99', ''],
            Product::PRODUCT_SHELF_COMPANY_2023_PREMIUM => ['199.99', ''],
            Product::PRODUCT_SHELF_COMPANY_2024_STANDARD => ['99.99', ''],
            Product::PRODUCT_SHELF_COMPANY_2024_PREMIUM => ['149.99', ''],
            Package::PACKAGE_RESERVE_COMPANY_NAME => ['99.99', ''],
            Package::PACKAGE_RESERVE_COMPANY_NAME_ANNUAL_RENEWAL => ['99.99', ''],
            Product::PRODUCT_TRANSFER_SHARES_CONFIRMATION_STATEMENT_1 => ['69.99', ''],
            Product::PRODUCT_TRANSFER_SHARES_CONFIRMATION_STATEMENT_MULTIPLE => ['79.99', ''],
            Product::PRODUCT_ISSUE_SHARES_CONFIRMATION_STATEMENT_1 => ['69.99', ''],
            Product::PRODUCT_ISSUE_SHARES_CONFIRMATION_STATEMENT_MULTIPLE => ['79.99', ''],
            BundlePackage::BUNDLE_DORMANT_COMPANY_ACCOUNTS_CONFIRMATION_STATEMENT => ['99.99', ''],
            Product::PRODUCT_COMPANY_DISSOLUTION => ['69.99', ''],
            Product::PRODUCT_COMPANY_DISSOLUTION_EXPRESS => ['99.99', ''],
            CompanyNameChange::CHANGE_NAME_PRODUCT => ['59.99', ''],
            Product::PRODUCT_DORMANT_COMPANY_ACCOUNTS => ['69.99', '']
        ];

        foreach ($products as $productName => $oldValues) {
            $nodeId = $migrationHelper->getExistingNodeId($productName);
            if (empty($nodeId))
                throw new Exception(sprintf('Node with name %s does not exist!', $productName));

            [$oldPrice, $oldNonvatableValue] = $oldValues;

            $migrationHelper->updateProperty($nodeId, self::PRICE_PROPERTY, $oldPrice);
            $migrationHelper->updateProperty($nodeId, self::WHOLESALE_PRICE_PROPERTY, $oldPrice);
            $migrationHelper->updateProperty($nodeId, self::ASSOCIATED_PRICE_PROPERTY, $oldPrice);
            $migrationHelper->updateProperty($nodeId, self::PRODUCT_VALUE_PROPERTY, $oldPrice);
            $migrationHelper->updateProperty($nodeId, self::OFFER_PRICE_PROPERTY, $oldPrice);
            $migrationHelper->updateProperty($nodeId, self::NONVATABLE_VALUE_PROPERTY, $oldNonvatableValue);
        }
    }
}
