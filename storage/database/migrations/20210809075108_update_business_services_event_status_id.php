<?php

use Phinx\Migration\AbstractMigration;

class UpdateBusinessServicesEventStatusId extends AbstractMigration
{
    public function up()
    {
        $this->execute("ALTER TABLE business_services_lead_events CHANGE typeId typeId ENUM('COMPANY_RESOLVED','EMAIL_SENT','EXPORTED','NO_LONGER_AVAILABLE','TEMPORARY_REJECTED') NOT NULL");
    }

    public function down()
    {
        $this->execute("ALTER TABLE business_services_lead_events CHANGE typeId typeId ENUM('EMAIL_SENT','EXPORTED','NO_LONGER_AVAILABLE','TEMPORARY_REJECTED') NOT NULL");
    }
}
