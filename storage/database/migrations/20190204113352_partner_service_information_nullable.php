<?php

use Phinx\Migration\AbstractMigration;

class PartnerServiceInformationNullable extends AbstractMigration
{
    public function up()
    {
        $this->table('business_services_partner_information')
            ->changeColumn('title', 'string', ['null' => TRUE])
            ->changeColumn('first_name', 'string', ['null' => TRUE])
            ->changeColumn('last_name', 'string', ['null' => TRUE])
            ->changeColumn('email', 'string', ['null' => TRUE])
            ->changeColumn('phone', 'string', ['null' => TRUE])
            ->changeColumn('name_number', 'string', ['null' => TRUE])
            ->changeColumn('street', 'string', ['null' => TRUE])
            ->changeColumn('postcode', 'string', ['null' => TRUE])
            ->changeColumn('town', 'string', ['null' => TRUE])
            ->changeColumn('country', 'string', ['null' => TRUE])
            ->update();
    }
    public function down()
    {
        $this->table('business_services_partner_information')
            ->changeColumn('title', 'string', ['null' => FALSE])
            ->changeColumn('first_name', 'string', ['null' => FALSE])
            ->changeColumn('last_name', 'string', ['null' => FALSE])
            ->changeColumn('email', 'string', ['null' => FALSE])
            ->changeColumn('phone', 'string', ['null' => FALSE])
            ->changeColumn('name_number', 'string', ['null' => FALSE])
            ->changeColumn('street', 'string', ['null' => FALSE])
            ->changeColumn('postcode', 'string', ['null' => FALSE])
            ->changeColumn('town', 'string', ['null' => FALSE])
            ->changeColumn('country', 'string', ['null' => FALSE])
            ->update();
    }
}
