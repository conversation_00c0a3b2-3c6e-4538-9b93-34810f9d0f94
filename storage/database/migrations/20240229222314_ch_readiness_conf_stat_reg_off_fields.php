<?php

use Phinx\Migration\AbstractMigration;

class ChReadinessConfStatRegOffFields extends AbstractMigration
{
    const ADDRESS_CHANGE_TABLE = 'ch_address_change';
    const ANNUAL_RETURN_TABLE = 'ch_annual_return';

    public function up()
    {
        $this->table(self::ADDRESS_CHANGE_TABLE)
            ->addColumn('accept_appropriate_office_address_statement', 'boolean', ['after' => 'po_box', 'null' => true])
            ->update();

        $this->table(self::ANNUAL_RETURN_TABLE)
            ->addColumn('registered_email_address', 'string', ['after' => 'made_up_date', 'null' => true])
            ->addColumn('is_lawful_purpose_statement', 'boolean', ['after' => 'registered_email_address', 'null' => true])
            ->update();
    }

    public function down()
    {
        $this->table(self::ADDRESS_CHANGE_TABLE)
            ->removeColumn('accept_appropriate_office_address_statement')
            ->update();

        $this->table(self::ANNUAL_RETURN_TABLE)
            ->removeColumn('registered_email_address')
            ->removeColumn('is_lawful_purpose_statement')
            ->update();
    }
}
