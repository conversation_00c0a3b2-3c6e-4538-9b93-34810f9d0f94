<?php

namespace CMS\Proxy\__CG__\Entities;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Service extends \Entities\Service implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }

    /**
     * {@inheritDoc}
     * @param string $name
     */
    public function & __get($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__get', [$name]);
        return parent::__get($name);
    }

    /**
     * {@inheritDoc}
     * @param string $name
     * @param mixed  $value
     */
    public function __set($name, $value)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__set', [$name, $value]);
        return parent::__set($name, $value);
    }

    /**
     * {@inheritDoc}
     * @param  string $name
     * @return boolean
     */
    public function __isset($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__isset', [$name]);

        return parent::__isset($name);
    }

    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Entities\\Service' . "\0" . 'serviceId', '' . "\0" . 'Entities\\Service' . "\0" . 'parent', '' . "\0" . 'Entities\\Service' . "\0" . 'serviceTypeId', '' . "\0" . 'Entities\\Service' . "\0" . 'productId', '' . "\0" . 'Entities\\Service' . "\0" . 'renewalProductId', '' . "\0" . 'Entities\\Service' . "\0" . 'serviceName', '' . "\0" . 'Entities\\Service' . "\0" . 'dtStart', '' . "\0" . 'Entities\\Service' . "\0" . 'dtExpires', '' . "\0" . 'Entities\\Service' . "\0" . 'stateId', '' . "\0" . 'Entities\\Service' . "\0" . 'initialDuration', '' . "\0" . 'Entities\\Service' . "\0" . 'initialDtStart', '' . "\0" . 'Entities\\Service' . "\0" . 'dtc', '' . "\0" . 'Entities\\Service' . "\0" . 'dtm', '' . "\0" . 'Entities\\Service' . "\0" . 'order', '' . "\0" . 'Entities\\Service' . "\0" . 'orderItem', '' . "\0" . 'Entities\\Service' . "\0" . 'company', '' . "\0" . 'Entities\\Service' . "\0" . 'product', '' . "\0" . 'Entities\\Service' . "\0" . 'renewalProduct', '' . "\0" . 'Entities\\Service' . "\0" . 'children'];
        }

        return ['__isInitialized__', '' . "\0" . 'Entities\\Service' . "\0" . 'serviceId', '' . "\0" . 'Entities\\Service' . "\0" . 'parent', '' . "\0" . 'Entities\\Service' . "\0" . 'serviceTypeId', '' . "\0" . 'Entities\\Service' . "\0" . 'productId', '' . "\0" . 'Entities\\Service' . "\0" . 'renewalProductId', '' . "\0" . 'Entities\\Service' . "\0" . 'serviceName', '' . "\0" . 'Entities\\Service' . "\0" . 'dtStart', '' . "\0" . 'Entities\\Service' . "\0" . 'dtExpires', '' . "\0" . 'Entities\\Service' . "\0" . 'stateId', '' . "\0" . 'Entities\\Service' . "\0" . 'initialDuration', '' . "\0" . 'Entities\\Service' . "\0" . 'initialDtStart', '' . "\0" . 'Entities\\Service' . "\0" . 'dtc', '' . "\0" . 'Entities\\Service' . "\0" . 'dtm', '' . "\0" . 'Entities\\Service' . "\0" . 'order', '' . "\0" . 'Entities\\Service' . "\0" . 'orderItem', '' . "\0" . 'Entities\\Service' . "\0" . 'company', '' . "\0" . 'Entities\\Service' . "\0" . 'product', '' . "\0" . 'Entities\\Service' . "\0" . 'renewalProduct', '' . "\0" . 'Entities\\Service' . "\0" . 'children'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Service $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getParent()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getParent', []);

        return parent::getParent();
    }

    /**
     * {@inheritDoc}
     */
    public function getServiceId()
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getServiceId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getServiceId', []);

        return parent::getServiceId();
    }

    /**
     * {@inheritDoc}
     */
    public function getServiceTypeId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getServiceTypeId', []);

        return parent::getServiceTypeId();
    }

    /**
     * {@inheritDoc}
     */
    public function getServiceName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getServiceName', []);

        return parent::getServiceName();
    }

    /**
     * {@inheritDoc}
     */
    public function setServiceName(string $serviceName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setServiceName', [$serviceName]);

        return parent::setServiceName($serviceName);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductTitle()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductTitle', []);

        return parent::getProductTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtStart()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtStart', []);

        return parent::getDtStart();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtExpires()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtExpires', []);

        return parent::getDtExpires();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtExpiresWithParentCheck(): ?\DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtExpiresWithParentCheck', []);

        return parent::getDtExpiresWithParentCheck();
    }

    /**
     * {@inheritDoc}
     */
    public function setStateId($stateId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStateId', [$stateId]);

        return parent::setStateId($stateId);
    }

    /**
     * {@inheritDoc}
     */
    public function getStateId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStateId', []);

        return parent::getStateId();
    }

    /**
     * {@inheritDoc}
     */
    public function getInitialDuration()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getInitialDuration', []);

        return parent::getInitialDuration();
    }

    /**
     * {@inheritDoc}
     */
    public function setInitialDuration($initialDuration)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setInitialDuration', [$initialDuration]);

        return parent::setInitialDuration($initialDuration);
    }

    /**
     * {@inheritDoc}
     */
    public function getInitialDtStart()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getInitialDtStart', []);

        return parent::getInitialDtStart();
    }

    /**
     * {@inheritDoc}
     */
    public function setInitialDtStart(\DateTimeInterface $initialDtStart)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setInitialDtStart', [$initialDtStart]);

        return parent::setInitialDtStart($initialDtStart);
    }

    /**
     * {@inheritDoc}
     */
    public function setDtc(\DateTime $dtc)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtc', [$dtc]);

        return parent::setDtc($dtc);
    }

    /**
     * {@inheritDoc}
     */
    public function getDtc()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtc', []);

        return parent::getDtc();
    }

    /**
     * {@inheritDoc}
     */
    public function setDtm($dtm)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtm', [$dtm]);

        return parent::setDtm($dtm);
    }

    /**
     * {@inheritDoc}
     */
    public function getDtm()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtm', []);

        return parent::getDtm();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrder', []);

        return parent::getOrder();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrderItem()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderItem', []);

        return parent::getOrderItem();
    }

    /**
     * {@inheritDoc}
     */
    public function getCompany()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompany', []);

        return parent::getCompany();
    }

    /**
     * {@inheritDoc}
     */
    public function getProductId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductId', []);

        return parent::getProductId();
    }

    /**
     * {@inheritDoc}
     */
    public function getProduct()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProduct', []);

        return parent::getProduct();
    }

    /**
     * {@inheritDoc}
     */
    public function getRenewalProduct()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRenewalProduct', []);

        return parent::getRenewalProduct();
    }

    /**
     * {@inheritDoc}
     */
    public function setParent($parent)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setParent', [$parent]);

        return parent::setParent($parent);
    }

    /**
     * {@inheritDoc}
     */
    public function setServiceTypeId($serviceTypeId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setServiceTypeId', [$serviceTypeId]);

        return parent::setServiceTypeId($serviceTypeId);
    }

    /**
     * {@inheritDoc}
     */
    public function setDtStart(\DateTimeInterface $dtStart)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtStart', [$dtStart]);

        return parent::setDtStart($dtStart);
    }

    /**
     * {@inheritDoc}
     */
    public function setDtExpires(\DateTimeInterface $dtExpires)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtExpires', [$dtExpires]);

        return parent::setDtExpires($dtExpires);
    }

    /**
     * {@inheritDoc}
     */
    public function setOrder(\Entities\Order $order)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrder', [$order]);

        return parent::setOrder($order);
    }

    /**
     * {@inheritDoc}
     */
    public function setOrderItem(\Entities\OrderItem $orderItem)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrderItem', [$orderItem]);

        return parent::setOrderItem($orderItem);
    }

    /**
     * {@inheritDoc}
     */
    public function setCompany(\Entities\Company $company)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompany', [$company]);

        return parent::setCompany($company);
    }

    /**
     * {@inheritDoc}
     */
    public function setProduct(\Models\Products\Product|\Framework\FNode $product)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProduct', [$product]);

        return parent::setProduct($product);
    }

    /**
     * {@inheritDoc}
     */
    public function setRenewalProduct(\Models\Products\Product|\Framework\FNode|null $renewalProduct = NULL): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRenewalProduct', [$renewalProduct]);

        parent::setRenewalProduct($renewalProduct);
    }

    /**
     * {@inheritDoc}
     */
    public function getServiceType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getServiceType', []);

        return parent::getServiceType();
    }

    /**
     * {@inheritDoc}
     */
    public function hasDates()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasDates', []);

        return parent::hasDates();
    }

    /**
     * {@inheritDoc}
     */
    public function isOnHold()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isOnHold', []);

        return parent::isOnHold();
    }

    /**
     * {@inheritDoc}
     */
    public function isOnHoldOn(\DateTime $date)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isOnHoldOn', [$date]);

        return parent::isOnHoldOn($date);
    }

    /**
     * {@inheritDoc}
     */
    public function isActive()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isActive', []);

        return parent::isActive();
    }

    /**
     * {@inheritDoc}
     */
    public function isActiveOn(\DateTime $date)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isActiveOn', [$date]);

        return parent::isActiveOn($date);
    }

    /**
     * {@inheritDoc}
     */
    public function isOverdueAndExpired(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isOverdueAndExpired', []);

        return parent::isOverdueAndExpired();
    }

    /**
     * {@inheritDoc}
     */
    public function isOverdue()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isOverdue', []);

        return parent::isOverdue();
    }

    /**
     * {@inheritDoc}
     */
    public function isOverdueOn(\DateTime $date)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isOverdueOn', [$date]);

        return parent::isOverdueOn($date);
    }

    /**
     * {@inheritDoc}
     */
    public function isExpired()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isExpired', []);

        return parent::isExpired();
    }

    /**
     * {@inheritDoc}
     */
    public function isExpiredOn(\DateTime $date)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isExpiredOn', [$date]);

        return parent::isExpiredOn($date);
    }

    /**
     * {@inheritDoc}
     */
    public function isActivatedAndNotExpired()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isActivatedAndNotExpired', []);

        return parent::isActivatedAndNotExpired();
    }

    /**
     * {@inheritDoc}
     */
    public function isActivatedAndNotExpiredOn(\DateTime $date)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isActivatedAndNotExpiredOn', [$date]);

        return parent::isActivatedAndNotExpiredOn($date);
    }

    /**
     * {@inheritDoc}
     */
    public function isActivatedAndExpired()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isActivatedAndExpired', []);

        return parent::isActivatedAndExpired();
    }

    /**
     * {@inheritDoc}
     */
    public function isActivatedAndExpiredOn(\DateTime $date)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isActivatedAndExpiredOn', [$date]);

        return parent::isActivatedAndExpiredOn($date);
    }

    /**
     * {@inheritDoc}
     */
    public function isExpiringAfter(\DateTime $date)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isExpiringAfter', [$date]);

        return parent::isExpiringAfter($date);
    }

    /**
     * {@inheritDoc}
     */
    public function getChildren()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getChildren', []);

        return parent::getChildren();
    }

    /**
     * {@inheritDoc}
     */
    public function addChild(\Entities\Service $service)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addChild', [$service]);

        return parent::addChild($service);
    }

    /**
     * {@inheritDoc}
     */
    public function clearParent()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearParent', []);

        return parent::clearParent();
    }

    /**
     * {@inheritDoc}
     */
    public function hasParent()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasParent', []);

        return parent::hasParent();
    }

    /**
     * {@inheritDoc}
     */
    public function isPackageType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPackageType', []);

        return parent::isPackageType();
    }

    /**
     * {@inheritDoc}
     */
    public function isCorePackageType(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isCorePackageType', []);

        return parent::isCorePackageType();
    }

    /**
     * {@inheritDoc}
     */
    public function isProductType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isProductType', []);

        return parent::isProductType();
    }

    /**
     * {@inheritDoc}
     */
    public function hasRenewalProduct()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasRenewalProduct', []);

        return parent::hasRenewalProduct();
    }

    /**
     * {@inheritDoc}
     */
    public function isRenewable()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isRenewable', []);

        return parent::isRenewable();
    }

    /**
     * {@inheritDoc}
     */
    public function isOneOff()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isOneOff', []);

        return parent::isOneOff();
    }

    /**
     * {@inheritDoc}
     */
    public function isDisabled()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isDisabled', []);

        return parent::isDisabled();
    }

    /**
     * {@inheritDoc}
     */
    public function isEnabled()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isEnabled', []);

        return parent::isEnabled();
    }

    /**
     * {@inheritDoc}
     */
    public function isUpgraded()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isUpgraded', []);

        return parent::isUpgraded();
    }

    /**
     * {@inheritDoc}
     */
    public function isDowngraded()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isDowngraded', []);

        return parent::isDowngraded();
    }

    /**
     * {@inheritDoc}
     */
    public function toArray()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'toArray', []);

        return parent::toArray();
    }

    /**
     * {@inheritDoc}
     */
    public function isEqual(\Entities\Service $service)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isEqual', [$service]);

        return parent::isEqual($service);
    }

    /**
     * {@inheritDoc}
     */
    public function isEqualType(\Entities\Service $service)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isEqualType', [$service]);

        return parent::isEqualType($service);
    }

    /**
     * {@inheritDoc}
     */
    public function hasChildren()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasChildren', []);

        return parent::hasChildren();
    }

    /**
     * {@inheritDoc}
     */
    public function getChildOfType($serviceTypeId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getChildOfType', [$serviceTypeId]);

        return parent::getChildOfType($serviceTypeId);
    }

    /**
     * {@inheritDoc}
     */
    public function hasChildOfType($serviceTypeId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasChildOfType', [$serviceTypeId]);

        return parent::hasChildOfType($serviceTypeId);
    }

    /**
     * {@inheritDoc}
     */
    public function getDaysToExpire()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDaysToExpire', []);

        return parent::getDaysToExpire();
    }

    /**
     * {@inheritDoc}
     */
    public function getDaysAfterExpire()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDaysAfterExpire', []);

        return parent::getDaysAfterExpire();
    }

    /**
     * {@inheritDoc}
     */
    public function getRenewalProductExpiryDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRenewalProductExpiryDate', []);

        return parent::getRenewalProductExpiryDate();
    }

    /**
     * {@inheritDoc}
     */
    public function containsType($serviceTypeId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'containsType', [$serviceTypeId]);

        return parent::containsType($serviceTypeId);
    }

    /**
     * {@inheritDoc}
     */
    public function isAutoRenewalAllowed()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isAutoRenewalAllowed', []);

        return parent::isAutoRenewalAllowed();
    }

    /**
     * {@inheritDoc}
     */
    public function expiresToday()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'expiresToday', []);

        return parent::expiresToday();
    }

    /**
     * {@inheritDoc}
     */
    public function clone(\Entities\Company $company, \Entities\OrderItem $orderItem): \Entities\Service
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clone', [$company, $orderItem]);

        return parent::clone($company, $orderItem);
    }

    /**
     * {@inheritDoc}
     */
    public function getRenewalProductId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRenewalProductId', []);

        return parent::getRenewalProductId();
    }

    /**
     * {@inheritDoc}
     */
    public function hasChildrenOfAddressType(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasChildrenOfAddressType', []);

        return parent::hasChildrenOfAddressType();
    }

    /**
     * {@inheritDoc}
     */
    public function isAddressType(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isAddressType', []);

        return parent::isAddressType();
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomer(): ?\Entities\Customer
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomer', []);

        return parent::getCustomer();
    }

    /**
     * {@inheritDoc}
     */
    public function isMonthly(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isMonthly', []);

        return parent::isMonthly();
    }

    /**
     * {@inheritDoc}
     */
    public function isRegisteredOfficeService(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isRegisteredOfficeService', []);

        return parent::isRegisteredOfficeService();
    }

    /**
     * {@inheritDoc}
     */
    public function isMailboxPremiumService(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isMailboxPremiumService', []);

        return parent::isMailboxPremiumService();
    }

    /**
     * {@inheritDoc}
     */
    public function isMailboxStandardService(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isMailboxStandardService', []);

        return parent::isMailboxStandardService();
    }

    /**
     * {@inheritDoc}
     */
    public function isMailboxBusinessAddressService(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isMailboxBusinessAddressService', []);

        return parent::isMailboxBusinessAddressService();
    }

    /**
     * {@inheritDoc}
     */
    public function isMailboxService(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isMailboxService', []);

        return parent::isMailboxService();
    }

    /**
     * {@inheritDoc}
     */
    public function __call($name, $args)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__call', [$name, $args]);

        return parent::__call($name, $args);
    }

    /**
     * {@inheritDoc}
     */
    public function __unset($name)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__unset', [$name]);

        return parent::__unset($name);
    }

}
