<?php

namespace CMS\Proxy\__CG__\Entities\CompanyHouse\FormSubmission;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class OfficerChangeDetails extends \Entities\CompanyHouse\FormSubmission\OfficerChangeDetails implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }

    /**
     * {@inheritDoc}
     * @param string $name
     */
    public function & __get($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__get', [$name]);
        return parent::__get($name);
    }

    /**
     * {@inheritDoc}
     * @param string $name
     * @param mixed  $value
     */
    public function __set($name, $value)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__set', [$name, $value]);
        return parent::__set($name, $value);
    }

    /**
     * {@inheritDoc}
     * @param  string $name
     * @return boolean
     */
    public function __isset($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__isset', [$name]);

        return parent::__isset($name);
    }

    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'type', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'corporate', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'designatedInd', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'authentication', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'consentToAct', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'corporateName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'title', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'forename', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'middleName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'surname', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'dob', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newTitle', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newForename', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newMiddleName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newSurname', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newCorporateName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newPremise', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newStreet', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newThoroughfare', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newPostTown', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newCounty', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newCountry', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newPostcode', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newCareOfName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newPoBox', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialPremise', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialStreet', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialThoroughfare', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialPostTown', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialCounty', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialCountry', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialPostcode', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialSecureAddressInd', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newNationality', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newCountryOfResidence', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newOccupation', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newIdentificationType', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newPlaceRegistered', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newRegistrationNumber', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newLawGoverned', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newLegalForm'];
        }

        return ['__isInitialized__', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'type', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'corporate', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'designatedInd', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'authentication', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'consentToAct', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'corporateName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'title', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'forename', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'middleName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'surname', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'dob', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newTitle', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newForename', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newMiddleName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newSurname', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newCorporateName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newPremise', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newStreet', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newThoroughfare', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newPostTown', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newCounty', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newCountry', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newPostcode', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newCareOfName', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newPoBox', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialPremise', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialStreet', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialThoroughfare', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialPostTown', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialCounty', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialCountry', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialPostcode', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newResidentialSecureAddressInd', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newNationality', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newCountryOfResidence', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newOccupation', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newIdentificationType', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newPlaceRegistered', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newRegistrationNumber', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newLawGoverned', '' . "\0" . 'Entities\\CompanyHouse\\FormSubmission\\OfficerChangeDetails' . "\0" . 'newLegalForm'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (OfficerChangeDetails $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * {@inheritDoc}
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);

        parent::__clone();
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getType', []);

        return parent::getType();
    }

    /**
     * {@inheritDoc}
     */
    public function setType($type)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setType', [$type]);

        return parent::setType($type);
    }

    /**
     * {@inheritDoc}
     */
    public function getCorporate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCorporate', []);

        return parent::getCorporate();
    }

    /**
     * {@inheritDoc}
     */
    public function setCorporate($corporate)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCorporate', [$corporate]);

        return parent::setCorporate($corporate);
    }

    /**
     * {@inheritDoc}
     */
    public function getDesignatedInd()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDesignatedInd', []);

        return parent::getDesignatedInd();
    }

    /**
     * {@inheritDoc}
     */
    public function setDesignatedInd($designatedInd)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDesignatedInd', [$designatedInd]);

        return parent::setDesignatedInd($designatedInd);
    }

    /**
     * {@inheritDoc}
     */
    public function getAuthentication()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAuthentication', []);

        return parent::getAuthentication();
    }

    /**
     * {@inheritDoc}
     */
    public function setAuthentication($authentication)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAuthentication', [$authentication]);

        return parent::setAuthentication($authentication);
    }

    /**
     * {@inheritDoc}
     */
    public function hasConsentToAct()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasConsentToAct', []);

        return parent::hasConsentToAct();
    }

    /**
     * {@inheritDoc}
     */
    public function setConsentToAct($consentToAct)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setConsentToAct', [$consentToAct]);

        return parent::setConsentToAct($consentToAct);
    }

    /**
     * {@inheritDoc}
     */
    public function getCorporateName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCorporateName', []);

        return parent::getCorporateName();
    }

    /**
     * {@inheritDoc}
     */
    public function setCorporateName($corporateName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCorporateName', [$corporateName]);

        return parent::setCorporateName($corporateName);
    }

    /**
     * {@inheritDoc}
     */
    public function getTitle()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTitle', []);

        return parent::getTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function setTitle($title)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTitle', [$title]);

        return parent::setTitle($title);
    }

    /**
     * {@inheritDoc}
     */
    public function getForename()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getForename', []);

        return parent::getForename();
    }

    /**
     * {@inheritDoc}
     */
    public function setForename($forename)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setForename', [$forename]);

        return parent::setForename($forename);
    }

    /**
     * {@inheritDoc}
     */
    public function getMiddleName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMiddleName', []);

        return parent::getMiddleName();
    }

    /**
     * {@inheritDoc}
     */
    public function setMiddleName($middleName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMiddleName', [$middleName]);

        return parent::setMiddleName($middleName);
    }

    /**
     * {@inheritDoc}
     */
    public function getSurname()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSurname', []);

        return parent::getSurname();
    }

    /**
     * {@inheritDoc}
     */
    public function setSurname($surname)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSurname', [$surname]);

        return parent::setSurname($surname);
    }

    /**
     * {@inheritDoc}
     */
    public function getDob()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDob', []);

        return parent::getDob();
    }

    /**
     * {@inheritDoc}
     */
    public function setDob($dob)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDob', [$dob]);

        return parent::setDob($dob);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewTitle()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewTitle', []);

        return parent::getNewTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewTitle($newTitle)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewTitle', [$newTitle]);

        return parent::setNewTitle($newTitle);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewForename()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewForename', []);

        return parent::getNewForename();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewForename($newForename)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewForename', [$newForename]);

        return parent::setNewForename($newForename);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewMiddleName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewMiddleName', []);

        return parent::getNewMiddleName();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewMiddleName($newMiddleName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewMiddleName', [$newMiddleName]);

        return parent::setNewMiddleName($newMiddleName);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewSurname()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewSurname', []);

        return parent::getNewSurname();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewSurname($newSurname)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewSurname', [$newSurname]);

        return parent::setNewSurname($newSurname);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewCorporateName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewCorporateName', []);

        return parent::getNewCorporateName();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewCorporateName($newCorporateName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewCorporateName', [$newCorporateName]);

        return parent::setNewCorporateName($newCorporateName);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewPremise()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewPremise', []);

        return parent::getNewPremise();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewPremise($newPremise)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewPremise', [$newPremise]);

        return parent::setNewPremise($newPremise);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewStreet()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewStreet', []);

        return parent::getNewStreet();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewStreet($newStreet)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewStreet', [$newStreet]);

        return parent::setNewStreet($newStreet);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewThoroughfare()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewThoroughfare', []);

        return parent::getNewThoroughfare();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewThoroughfare($newThoroughfare)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewThoroughfare', [$newThoroughfare]);

        return parent::setNewThoroughfare($newThoroughfare);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewPostTown()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewPostTown', []);

        return parent::getNewPostTown();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewPostTown($newPostTown)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewPostTown', [$newPostTown]);

        return parent::setNewPostTown($newPostTown);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewCounty()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewCounty', []);

        return parent::getNewCounty();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewCounty($newCounty)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewCounty', [$newCounty]);

        return parent::setNewCounty($newCounty);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewCountry()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewCountry', []);

        return parent::getNewCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewCountry($newCountry)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewCountry', [$newCountry]);

        return parent::setNewCountry($newCountry);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewPostcode()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewPostcode', []);

        return parent::getNewPostcode();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewPostcode($newPostcode)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewPostcode', [$newPostcode]);

        return parent::setNewPostcode($newPostcode);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewCareOfName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewCareOfName', []);

        return parent::getNewCareOfName();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewCareOfName($newCareOfName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewCareOfName', [$newCareOfName]);

        return parent::setNewCareOfName($newCareOfName);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewPoBox()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewPoBox', []);

        return parent::getNewPoBox();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewPoBox($newPoBox)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewPoBox', [$newPoBox]);

        return parent::setNewPoBox($newPoBox);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewResidentialPremise()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewResidentialPremise', []);

        return parent::getNewResidentialPremise();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewResidentialPremise($newResidentialPremise)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewResidentialPremise', [$newResidentialPremise]);

        return parent::setNewResidentialPremise($newResidentialPremise);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewResidentialStreet()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewResidentialStreet', []);

        return parent::getNewResidentialStreet();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewResidentialStreet($newResidentialStreet)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewResidentialStreet', [$newResidentialStreet]);

        return parent::setNewResidentialStreet($newResidentialStreet);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewResidentialThoroughfare()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewResidentialThoroughfare', []);

        return parent::getNewResidentialThoroughfare();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewResidentialThoroughfare($newResidentialThoroughfare)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewResidentialThoroughfare', [$newResidentialThoroughfare]);

        return parent::setNewResidentialThoroughfare($newResidentialThoroughfare);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewResidentialPostTown()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewResidentialPostTown', []);

        return parent::getNewResidentialPostTown();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewResidentialPostTown($newResidentialPostTown)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewResidentialPostTown', [$newResidentialPostTown]);

        return parent::setNewResidentialPostTown($newResidentialPostTown);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewResidentialCounty()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewResidentialCounty', []);

        return parent::getNewResidentialCounty();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewResidentialCounty($newResidentialCounty)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewResidentialCounty', [$newResidentialCounty]);

        return parent::setNewResidentialCounty($newResidentialCounty);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewResidentialCountry()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewResidentialCountry', []);

        return parent::getNewResidentialCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewResidentialCountry($newResidentialCountry)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewResidentialCountry', [$newResidentialCountry]);

        return parent::setNewResidentialCountry($newResidentialCountry);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewResidentialPostcode()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewResidentialPostcode', []);

        return parent::getNewResidentialPostcode();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewResidentialPostcode($newResidentialPostcode)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewResidentialPostcode', [$newResidentialPostcode]);

        return parent::setNewResidentialPostcode($newResidentialPostcode);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewResidentialSecureAddressInd()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewResidentialSecureAddressInd', []);

        return parent::getNewResidentialSecureAddressInd();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewResidentialSecureAddressInd($newResidentialSecureAddressInd)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewResidentialSecureAddressInd', [$newResidentialSecureAddressInd]);

        return parent::setNewResidentialSecureAddressInd($newResidentialSecureAddressInd);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewNationality()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewNationality', []);

        return parent::getNewNationality();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewNationality($newNationality)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewNationality', [$newNationality]);

        return parent::setNewNationality($newNationality);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewCountryOfResidence()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewCountryOfResidence', []);

        return parent::getNewCountryOfResidence();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewCountryOfResidence($newCountryOfResidence)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewCountryOfResidence', [$newCountryOfResidence]);

        return parent::setNewCountryOfResidence($newCountryOfResidence);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewOccupation()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewOccupation', []);

        return parent::getNewOccupation();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewOccupation($newOccupation)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewOccupation', [$newOccupation]);

        return parent::setNewOccupation($newOccupation);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewIdentificationType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewIdentificationType', []);

        return parent::getNewIdentificationType();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewIdentificationType($newIdentificationType)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewIdentificationType', [$newIdentificationType]);

        return parent::setNewIdentificationType($newIdentificationType);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewPlaceRegistered()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewPlaceRegistered', []);

        return parent::getNewPlaceRegistered();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewPlaceRegistered($newPlaceRegistered)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewPlaceRegistered', [$newPlaceRegistered]);

        return parent::setNewPlaceRegistered($newPlaceRegistered);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewRegistrationNumber()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewRegistrationNumber', []);

        return parent::getNewRegistrationNumber();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewRegistrationNumber($newRegistrationNumber)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewRegistrationNumber', [$newRegistrationNumber]);

        return parent::setNewRegistrationNumber($newRegistrationNumber);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewLawGoverned()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewLawGoverned', []);

        return parent::getNewLawGoverned();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewLawGoverned($newLawGoverned)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewLawGoverned', [$newLawGoverned]);

        return parent::setNewLawGoverned($newLawGoverned);
    }

    /**
     * {@inheritDoc}
     */
    public function getNewLegalForm()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNewLegalForm', []);

        return parent::getNewLegalForm();
    }

    /**
     * {@inheritDoc}
     */
    public function setNewLegalForm($newLegalForm)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNewLegalForm', [$newLegalForm]);

        return parent::setNewLegalForm($newLegalForm);
    }

    /**
     * {@inheritDoc}
     */
    public function getId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getFormSubmissionId()
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getFormSubmissionId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFormSubmissionId', []);

        return parent::getFormSubmissionId();
    }

    /**
     * {@inheritDoc}
     */
    public function getFormIdentifier()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFormIdentifier', []);

        return parent::getFormIdentifier();
    }

    /**
     * {@inheritDoc}
     */
    public function setFormIdentifier($formIdentifier)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFormIdentifier', [$formIdentifier]);

        return parent::setFormIdentifier($formIdentifier);
    }

    /**
     * {@inheritDoc}
     */
    public function getLanguage()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLanguage', []);

        return parent::getLanguage();
    }

    /**
     * {@inheritDoc}
     */
    public function setLanguage($language)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLanguage', [$language]);

        return parent::setLanguage($language);
    }

    /**
     * {@inheritDoc}
     */
    public function getResponse()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getResponse', []);

        return parent::getResponse();
    }

    /**
     * {@inheritDoc}
     */
    public function setResponse($response)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setResponse', [$response]);

        return parent::setResponse($response);
    }

    /**
     * {@inheritDoc}
     */
    public function hasResponse()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasResponse', []);

        return parent::hasResponse();
    }

    /**
     * {@inheritDoc}
     */
    public function getRejectReference()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRejectReference', []);

        return parent::getRejectReference();
    }

    /**
     * {@inheritDoc}
     */
    public function setRejectReference($rejectReference)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRejectReference', [$rejectReference]);

        return parent::setRejectReference($rejectReference);
    }

    /**
     * {@inheritDoc}
     */
    public function getExaminerTelephone()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExaminerTelephone', []);

        return parent::getExaminerTelephone();
    }

    /**
     * {@inheritDoc}
     */
    public function setExaminerTelephone($examinerTelephone)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setExaminerTelephone', [$examinerTelephone]);

        return parent::setExaminerTelephone($examinerTelephone);
    }

    /**
     * {@inheritDoc}
     */
    public function getExaminerComment()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getExaminerComment', []);

        return parent::getExaminerComment();
    }

    /**
     * {@inheritDoc}
     */
    public function setExaminerComment($examinerComment)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setExaminerComment', [$examinerComment]);

        return parent::setExaminerComment($examinerComment);
    }

    /**
     * {@inheritDoc}
     */
    public function getDateCancelled()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDateCancelled', []);

        return parent::getDateCancelled();
    }

    /**
     * {@inheritDoc}
     */
    public function setDateCancelled(\DateTime $dateCancelled)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDateCancelled', [$dateCancelled]);

        return parent::setDateCancelled($dateCancelled);
    }

    /**
     * {@inheritDoc}
     */
    public function getDtc()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtc', []);

        return parent::getDtc();
    }

    /**
     * {@inheritDoc}
     */
    public function setDtc(\DateTime $dtc)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtc', [$dtc]);

        return parent::setDtc($dtc);
    }

    /**
     * {@inheritDoc}
     */
    public function getDtm()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtm', []);

        return parent::getDtm();
    }

    /**
     * {@inheritDoc}
     */
    public function setDtm(\DateTime $dtm)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtm', [$dtm]);

        return parent::setDtm($dtm);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompany()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompany', []);

        return parent::getCompany();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompany(\Entities\Company $company)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompany', [$company]);

        return parent::setCompany($company);
    }

    /**
     * {@inheritDoc}
     */
    public function getFormSubmissionErrors()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFormSubmissionErrors', []);

        return parent::getFormSubmissionErrors();
    }

    /**
     * {@inheritDoc}
     */
    public function setFormSubmissionErrors(array $formSubmissionErrors)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFormSubmissionErrors', [$formSubmissionErrors]);

        return parent::setFormSubmissionErrors($formSubmissionErrors);
    }

    /**
     * {@inheritDoc}
     */
    public function addFormSubmissionError(\Entities\CompanyHouse\FormSubmissionError $formSubmission)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addFormSubmissionError', [$formSubmission]);

        return parent::addFormSubmissionError($formSubmission);
    }

    /**
     * {@inheritDoc}
     */
    public function isResponseNull(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isResponseNull', []);

        return parent::isResponseNull();
    }

    /**
     * {@inheritDoc}
     */
    public function isAccepted(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isAccepted', []);

        return parent::isAccepted();
    }

    /**
     * {@inheritDoc}
     */
    public function isPending(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPending', []);

        return parent::isPending();
    }

    /**
     * {@inheritDoc}
     */
    public function isInternalFailure(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isInternalFailure', []);

        return parent::isInternalFailure();
    }

    /**
     * {@inheritDoc}
     */
    public function isError(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isError', []);

        return parent::isError();
    }

    /**
     * {@inheritDoc}
     */
    public function isWithhold(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isWithhold', []);

        return parent::isWithhold();
    }

    /**
     * {@inheritDoc}
     */
    public function isRejected(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isRejected', []);

        return parent::isRejected();
    }

    /**
     * {@inheritDoc}
     */
    public function isCancelled()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isCancelled', []);

        return parent::isCancelled();
    }

    /**
     * {@inheritDoc}
     */
    public function markAsCancelled()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'markAsCancelled', []);

        return parent::markAsCancelled();
    }

    /**
     * {@inheritDoc}
     */
    public function unsetResponse()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'unsetResponse', []);

        return parent::unsetResponse();
    }

    /**
     * {@inheritDoc}
     */
    public function setFormSubmissionType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFormSubmissionType', []);

        return parent::setFormSubmissionType();
    }

    /**
     * {@inheritDoc}
     */
    public function isIncorporationType(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isIncorporationType', []);

        return parent::isIncorporationType();
    }

    /**
     * {@inheritDoc}
     */
    public function isConfirmationStatementType(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isConfirmationStatementType', []);

        return parent::isConfirmationStatementType();
    }

    /**
     * {@inheritDoc}
     */
    public function getResponseMessage(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getResponseMessage', []);

        return parent::getResponseMessage();
    }

    /**
     * {@inheritDoc}
     */
    public function __call($name, $args)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__call', [$name, $args]);

        return parent::__call($name, $args);
    }

    /**
     * {@inheritDoc}
     */
    public function __unset($name)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__unset', [$name]);

        return parent::__unset($name);
    }

}
